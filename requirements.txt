###### Install dependencies using sudo pip3 install -r requirements.txt ########
###### By convention dependencies should be fixed major/minor version
Authlib==1.3.1
asyncio==3.4.*
avro-python3==1.10.*
aws-msk-iam-sasl-signer-python==1.0.*
aws-requests-auth==0.4.*
boto3==1.38.*
bs4==0.0.*
config-man==0.0.4
confluent-kafka==2.6.*
coverage==7.6.*
defusedxml==0.7.*
dnspython==2.6.1 # fix for vuln found as part if version in pymongo (remove once pymongo has been updated)
ecdsa==0.19.*
ez_setup==0.9
fastavro==1.10.*
firebase-admin==6.6.*
gherkin2robotframework==0.3
google-cloud-pubsub==2.12.*
gql[all]==3.5.*
jsonschema==4.6.*
kafka-python-ng==2.2.*
lxml==5.3.*
numpy==2.1.*
paho-mqtt==1.6.*
pexpect==4.9.*
polling==0.3.*
protobuf==5.29.*
pyaml_env==1.2.*
pyjks==20.0.0
pymongo==4.10.*
python-schema-registry-client==2.4.*
pyyaml==6.0.1
redis==5.2.*
retry==0.9.*
requests==2.32.*
requests-aws4auth==1.3.*
robotframework==6.1
robotframework-jsonlibrary==0.4.*
robotframework-xvfb==1.2.*
robotframework-pabot==4.1.1
robotframework-retryfailed==0.2.*
robotframework-requests==0.9.*
robotframework-seriallibrary==0.4.*
setuptools==78.1.*
simplejson==3.17.*
slack-sdk==3.33.*
urllib3==2.5.0
uuid==1.30
xvfbwrapper==0.2.*
html-to-json==2.0.0
google-api-python-client==2.154.0
#pytest deps
pytest == 7.4.*
pytest-bdd == 6.1.*
pytest-html
pytest-bdd-report
jsondiff == 2.2.1
h11 == 0.16.0 #fix for sec vuln in httpcore
httpcore>=1.0.2
