#!/usr/bin python3

import base64
import time
import json
import requests
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig
import kafkaProducerLib
import vcdpHiveBroker


SUBSCRIBING_TO_TOPIC_LOG = "Subscribing to topic:: "
OBG_SUB_DATA_FILE = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data.json"
OBG_SUB_DATA_DECODED_PAYLOAD = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-decoded-payload.json"
OBG_SUB_DATA_SUB_DATA = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-for-validation.json"
OBG_SUB_DATA_SUB_DATA_EVA25 = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-for-eva25-validation.json"
VEHICLE_URI = "vehicle/"
systemVarFile = "../variables/system.yml"


@not_keyword
def get_obg_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "obg-url")


@keyword("Publish and Subscribe to hive topic for OBG")
def pub_sub_to_obg_topic(
    payloadFile,
    brokerUser,
    brokerClientSecret,
    uniqueId,
    SubscribeTopic,
    certfile,
    ca_certs,
    keyfile,
    timeToSubs=0.5,
    writeToFile=False,
):
    PublishTopic = VEHICLE_URI + uniqueId + "/obg/data/discovery"
    print("Pub topic:", PublishTopic)
    openJsonData = open(payloadFile, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    payloadFile = json.dumps(jsonRequestBody)
    subDataFile = OBG_SUB_DATA_FILE
    SubsData, retain = vcdpHiveBroker.hive_connection_with_retries(
        uniqueId,
        brokerUser,
        brokerClientSecret,
        certfile,
        ca_certs,
        keyfile,
        writeToFile,
        SubscribeTopic,
        PublishTopic,
        payloadFile,
        timeToSubs,
        subDataFile,
        has_file_transform=False,
        max_retry=3,
    )
    return (SubsData, retain)


@keyword("OBG Vehicle register validation")
def vehicle_registration(data):
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    payload = jsonRequestBody["payload"]
    decodedpayload = base64.b64decode(payload)
    StrPayload = str(decodedpayload)
    if "errorCode" in StrPayload:
        jsonPayload = json.loads(decodedpayload)
        errorCode = jsonPayload["errorCode"]
        if errorCode == "0x01":
            response = "INVALID APPLICATION"
        elif errorCode == "0x02":
            response = "INVALID CHANNEL"
    else:
        response = "VEHICLE REGISTERED"

    return response


@keyword("Publish and Subscribe to hive topic for OBG multiple requests")
def pub_sub_multi_request_obg_topic(
    payloadFile,
    brokerUser,
    brokerClientSecret,
    uniqueId,
    SubscribeTopic,
    certfile,
    ca_certs,
    keyfile,
    timeToSubs=0.5,
    writeToFile=False,
):
    PublishTopic = VEHICLE_URI + uniqueId + "/obg/data/discovery"
    openJsonData = open(payloadFile, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    payloadFile = json.dumps(jsonRequestBody)
    subDataFile = OBG_SUB_DATA_FILE
    vcdpHiveBroker.hive_connection_with_retries(
        uniqueId,
        brokerUser,
        brokerClientSecret,
        certfile,
        ca_certs,
        keyfile,
        writeToFile,
        SubscribeTopic,
        PublishTopic,
        payloadFile,
        timeToSubs,
        subDataFile,
        has_file_transform=True,
        max_retry=3,
    )


@not_keyword
def file_transform(data):
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    file = open(
        OBG_SUB_DATA_DECODED_PAYLOAD,
        "w",
    )

    responsestr = str(jsonRequestBody["payload"])

    decoded = base64.b64decode(responsestr)  # Base64 decoding
    decodedString = str(decoded)

    decodedJson = decodedString.strip("b'")
    file.write(decodedJson)
    file.close()
    clipping_parameter(OBG_SUB_DATA_DECODED_PAYLOAD)


@not_keyword
def clipping_parameter(data):
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    del jsonRequestBody["createdTimestamp_s"]  # Removing timestamp

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    file = open(
        OBG_SUB_DATA_SUB_DATA,
        "w",
    )

    payloadstr = str(payload)
    file.write(payloadstr)
    file.close()


@keyword("OBG Vehicle register validation with multiple requests")
def file_validator_eva2():
    openJsonFile1 = open(
        "../json-request-data/obg-subscribed-expected-data/obg-subscibed-expected-data-for-validation.json",
        "r",
    )

    response = file_validator(openJsonFile1)
    return response


@keyword("OBG tear down")
def obg_tear_down(user, pw, uniqueId):
    token = api_gateway_token(user, pw)

    headers = {
        "accept": "*/*",
        "Content-Type": "application/json",
        "Requester": "Robot",
        "Authorization": "Bearer " + token,
    }
    strUniqueId = str(uniqueId)
    UniqueId = strUniqueId.strip("['']")
    teardownUrl = get_obg_url() + "/test/vehicle-applications/" + UniqueId
    print(teardownUrl)
    response = requests.request(
        "DELETE",
        teardownUrl,
        headers=headers,
    )
    response.status_code
    print(response.status_code)


@not_keyword
def api_gateway_token(User, Secret, calledAsBinaryFile=False):
    vcdpHiveBroker.setup_globals(calledAsBinaryFile)
    apigateway_url = getattr(vcdpHiveBroker.systemVars, "apigateway_url")
    system_vars = BaseConfig(parse_config(systemVarFile))
    clientid = getattr(system_vars, "client_id")
    payload = "client_id=" + clientid + "&username=" + User + "&password=" + Secret + "&grant_type=" + "password"

    kcHeader = {"Content-Type": "application/x-www-form-urlencoded"}

    response = requests.request("POST", apigateway_url, headers=kcHeader, data=payload)

    print("Got Token:: ")
    y = json.loads(response.text)

    return y["access_token"]


@keyword("OBG Vehicle register validation with single request")
def vehicle_registration_channel_check(data, data1):
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    file = open(
        OBG_SUB_DATA_DECODED_PAYLOAD,
        "w",
    )
    responsestr = str(jsonRequestBody["payload"])

    decoded = base64.b64decode(responsestr)  # Base64 decoding
    decodedString = str(decoded)

    decodedJson = decodedString.strip("b'")
    file.write(decodedJson)
    file.close()
    print(decodedJson)
    openJsonData1 = open(data1, "r")
    print(data1)
    readJsonData1 = openJsonData1.read()
    jsonRequestBody1 = json.loads(readJsonData1)

    file1 = open(
        OBG_SUB_DATA_SUB_DATA,
        "w",
    )
    responsestr1 = str(jsonRequestBody1)
    file1.write(responsestr1)
    file1.close()

    openJsonFile1 = open(
        OBG_SUB_DATA_SUB_DATA,
        "r",
    )

    readJsonData1 = openJsonFile1.read()
    readJsonDataUpdated = readJsonData1.replace("'", '"')
    JsonData = json.loads(readJsonDataUpdated)
    count = len(JsonData["channel_ids"])

    openJsonFile2 = open(
        OBG_SUB_DATA_DECODED_PAYLOAD,
        "r",
    )

    readJsonData2 = openJsonFile2.read()
    readJsonDataUpdated2 = readJsonData2.replace("'", '"')
    JsonData2 = json.loads(readJsonDataUpdated2)
    count2 = len(JsonData2["channelDefinition"][0]["channels"])

    if count == count2:
        for i in range(count):
            print(JsonData["channel_ids"][i])
            if JsonData["channel_ids"][i] in readJsonData2:
                i = i + 1
            else:
                print("CHANNEL NOT FOUND", JsonData["channel_ids"][i])
                response = "CHANNEL ID : " + JsonData["channel_ids"][i] + " NOT FOUND : VEHICLE NOT REGISTERED"
                return response

            response = "VEHICLE REGISTERED"
    else:
        response = "NO OF CHANNELS DOESN'T MATCH WITH THE CLIENT REQUEST"

    return response


@keyword("Application blacklist check")
def blacklist_check(blacklist):
    openJsonData = open("../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-for-validation.json", "r")
    readJsonData = openJsonData.read()
    readJsonDataUpdated = readJsonData.replace("'", '"')
    JsonData = json.loads(readJsonDataUpdated)
    count = len(JsonData["channelDefinition"])
    print("count : ", count)

    for i in range(count):
        print("2 for loop")
        if JsonData["channelDefinition"][i]["application"] == "RVC" or JsonData["channelDefinition"][i]["application"] == "EVT":
            AppBlacklist = str(JsonData["channelDefinition"][i]["blacklist"])
            if AppBlacklist.lower() == blacklist.lower():
                response = "BLACKLIST UPDATED"
            else:
                response = "BLACKLIST NOT UPDATED"
    i = i + 1

    return response


@keyword("OBG Vehicle register with VA validating with multiple requests")
def obg_multi_request_check(blacklist):
    if blacklist == "true":
        openJsonFile1 = open("../json-request-data/obg-subscribed-expected-data/obg-subscibed-expected-data-for-validation-blacklist-true.json", "r")
    elif blacklist == "false":
        openJsonFile1 = open("../json-request-data/obg-subscribed-expected-data/obg-subscibed-expected-data-for-validation.json", "r")
    elif blacklist == "noRVC":
        openJsonFile1 = open("../json-request-data/obg-subscribed-expected-data/obg-subscibed-expected-va-v2n-data-for-validation.json", "r")

    readJsonData1 = openJsonFile1.read()
    ExpectedFile = json.loads(readJsonData1)

    openJsonFile2 = open(
        OBG_SUB_DATA_SUB_DATA,
        "r",
    )
    readJsonData2 = openJsonFile2.read()
    ActualFile = json.loads(readJsonData2)

    # Convert JSON objects to string
    str1 = str(ExpectedFile)
    str2 = str(ActualFile)

    print("expectedfile : ", str1)
    print("actual file : ", str2)

    # Compare strings
    if str1 == str2:
        response = "VEHICLE REGISTERED WITH MULTIPLE REQUESTS"
    else:
        response = "INVALID REQUEST"

    return response


@keyword("Subscribe to Topic after consent is changed on vehicle")
def sub_obg_topic(
    uniqueId,
    fileTransform,
    SubscribeTopic,
    brokerUser,
    brokerClientSecret,
    certfile,
    ca_certs,
    keyfile,
    vin,
    Permission,
    timeToSubs=0.5,
    writeToFile=False,
):
    client = vcdpHiveBroker.connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, True, True, "")
    if writeToFile is False:
        client.on_message = vcdpHiveBroker.on_message
    else:
        client.on_message = vcdpHiveBroker.on_message_writeToFile
    client.loop_start()
    topic = SubscribeTopic
    print(SUBSCRIBING_TO_TOPIC_LOG + SubscribeTopic)

    client.subscribe(topic, qos=1)
    kafkaProducerLib.add_remove_vehicle_consent(vin, Permission)
    time.sleep(int(timeToSubs))
    client.loop_stop()
    client.disconnect()

    if fileTransform == "Yes":
        file = open(
            OBG_SUB_DATA_SUB_DATA,
            "w",
        )

        payload = json.loads(vcdpHiveBroker.SubsData)
        payloadData = payload["payload"]
        payloadStr = str(payloadData)
        decoded = base64.b64decode(payloadStr)
        decodedString = str(decoded)
        decodedJson = decodedString.strip("b'")
        JsonData = json.loads(decodedJson)
        del JsonData["createdTimestamp_s"]
        JsonData.update(JsonData)
        finalPayload = json.dumps(JsonData)
        file.write(finalPayload)
        file.close()

    else:
        return vcdpHiveBroker.SubsData


@keyword("OBG Vehicle register validation for EVA25 with multiple requests")
def file_validator_eva25():
    openJsonFile1 = open(
        "../json-request-data/obg-subscribed-expected-data/obg-subscibed-expected-data-for-eva25-validation.json",
        "r",
    )
    response = file_validator(openJsonFile1)

    return response


@not_keyword
def file_validator(openJsonFile1):

    readJsonData1 = openJsonFile1.read()
    ExpectedFile = json.loads(readJsonData1)

    openJsonFile2 = open(
        OBG_SUB_DATA_SUB_DATA,
        "r",
    )
    readJsonData2 = openJsonFile2.read()
    ActualFile = json.loads(readJsonData2)

    # Convert JSON objects to string
    str1 = str(ExpectedFile)
    str2 = str(ActualFile)

    # Compare strings
    if str1 == str2:
        response = "VEHICLE REGISTERED WITH MULTIPLE REQUESTS"
    else:
        response = "INVALID REQUEST"

    return response
