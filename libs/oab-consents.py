from robot.api.deco import keyword
import commonUtils
import time


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"


serviceUrl = "oneapp-backend-url"


@keyword("Get consents")
def get_user_consents(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + "/consents"

    max_retries = 10
    delay_seconds = 5

    for attempt in range(max_retries):
        response_text, response_status = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)

        if response_status == 200:
            return response_text, response_status

        print(f"Attempt {attempt+1} failed with status {response_status}. Retrying in {delay_seconds} seconds")
        time.sleep(delay_seconds)

    print("Max retries reached. Request failed.")
    return response_text, response_status


# PUT consent
@keyword("Update consents")
def update_users_consents(user_email, user_password, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/consents"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]
