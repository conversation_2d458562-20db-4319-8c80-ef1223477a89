from robot.api.deco import keyword
import commonUtils


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"


serviceUrl = "oneapp-backend-url"


# POST vehicle command
@keyword("Send vehicle command")
def send_vehicle_command(user_email, user_password, vehicleID, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + ME_URI + vehicleID + "/commands"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]
