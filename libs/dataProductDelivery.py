from pyaml_env import parse_config, BaseConfig
import requests
import json
import dv<PERSON>tateHand<PERSON>
from datetime import date
from robot.api.deco import keyword, not_keyword

from commonUtils import get_keycloak_token

today = date.today()

systemVarFile = "../variables/system.yml"
contentType = "application/json"
authType = "Bearer "
headers = {"accept": "*/*", "Content-Type": contentType}
dataProductEndpoint = "/data-product"
noResponseBody = "No response body returned"


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "data-product-delivery-url")


@not_keyword
def query_state_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "query-state-objects-url")


@keyword("Send data product")
def send_data_product(data, user, pw, dp, vin):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["dataProductId"] = dp
    jsonRequestBody["vins"] = vin

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    url = get_url()
    postDataProduct = url + dataProductEndpoint

    response = requests.request("POST", postDataProduct, headers=header, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return noResponseBody, response.status_code


@keyword("Update data product")
def update_data_product(dataProduct, user, pw):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    url = get_url()
    updateDataProduct = url + "/data-product/update?dataProductId="

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("POST", updateDataProduct + dataProduct, headers=header)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return noResponseBody, response.status_code


@keyword("Delete data product for a vin")
def delete_data_product(data, user, pw):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    payload = json.dumps(jsonRequestBody)

    url = get_url()
    deleteDataProduct = url + dataProductEndpoint

    response = requests.request("DELETE", deleteDataProduct, headers=header, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return noResponseBody, response.status_code


@keyword("Delete data product from all vins")
def delete_data_product_from_all_vins(dataProduct, user, pw):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    url = get_url()
    deleteDataProductFromAllVins = url + "/data-product/all?dataProductId="

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("DELETE", deleteDataProductFromAllVins + dataProduct, headers=header)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return noResponseBody, response.status_code


# Ash's e2e requests


@keyword("Send Data Product to VIN")
def send_data_product_to_vin(user, pw, dataProductId, vins, auth=True):
    url = get_url()
    dataproducturl = url + dataProductEndpoint

    token = get_keycloak_token(user, pw, auth)

    headers = {"Authorization": authType + token, "Content-Type": contentType}
    print(vins)
    payload = json.dumps({"dataProductId": dataProductId, "vins": vins})

    print(payload)
    response = requests.request("POST", dataproducturl, data=payload, headers=headers)
    print(response.status_code)
    return response.status_code, response.text


@keyword("Delete Data Product by VIN")
def delete_data_product_by_vin(user, pw, dataProductId, vin, auth=True):
    url = get_url()
    dataproducturl = url + dataProductEndpoint

    token = get_keycloak_token(user, pw, auth)

    headers = {"Authorization": authType + token, "Content-Type": contentType}

    payload = json.dumps({"dataProductId": dataProductId, "vins": [vin]})

    response = requests.request("DELETE", dataproducturl, data=payload, headers=headers)
    return response.status_code


@keyword("Delete All Data Products for VIN")
def delete_all_data_products_for_vin(user, pw, vin, auth=True):
    policies = dvStateHandler.vehicle_status(user, pw, vin, "policies")

    # Active DPs
    activeDataProducts = []
    activeConfigs = policies["dataProduct"]["active"]
    if activeConfigs is not None:
        activeConfigs = policies["dataProduct"]["active"]["configs"]
        if len(activeConfigs) != 0:
            for x in activeConfigs:
                activeDataProducts.append(x["dataProductId"])
            print("active data products::")
            print(activeDataProducts)

            for dp in activeDataProducts:
                print(dp)
                delete_data_product_by_vin(user, pw, dp, vin, auth=True)

    # Pending DPs
    pendingDataProducts = []
    pendingConfigs = policies["dataProduct"]["pending"]
    if pendingConfigs is not None:
        pendingConfigs = policies["dataProduct"]["pending"]["configs"]
        if len(pendingConfigs) != 0:
            for x in pendingConfigs:
                pendingDataProducts.append(x["dataProductId"])
            print("pending data products::")
            print(pendingDataProducts)

            for dp in pendingDataProducts:
                print(dp)
                delete_data_product_by_vin(user, pw, dp, vin, auth=True)


@keyword("Query State objects")
def query_state_object(data, user, pw, vin):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    jsonRequestBody = vin

    payload = json.dumps(jsonRequestBody)
    url = query_state_url()
    queryStateobject = url + "/dv/state/query-state"
    print(queryStateobject)
    print(payload)
    response = requests.request("POST", queryStateobject, headers=header, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return noResponseBody, response.status_code


@keyword("Policies controller from DV")
def policies_controller(data, user, pw, vin):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    jsonRequestBody["identifiers"][0] = vin

    payload = json.dumps(jsonRequestBody)

    url = query_state_url()
    queryStateobject = url + "/v2/vehicles/policies/dataProduct?identifierType=VIN"
    print(queryStateobject)
    print(payload)
    response = requests.request("POST", queryStateobject, headers=header, data=payload)

    print(type(response.text))

    if response.status_code != 403:
        responsestr = str(response.text)
        print("this is str response", type(responsestr))
        return response.text, response.status_code

    else:
        return noResponseBody, response.status_code
