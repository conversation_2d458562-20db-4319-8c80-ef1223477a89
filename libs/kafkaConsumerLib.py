#!/usr/bin python3

from kafka import KafkaConsumer
from kafka.errors import KafkaError
from robot.api.deco import keyword, not_keyword
import json
import time
import uuid
from pyaml_env import parse_config, BaseConfig
from datetime import datetime
import queue
import threading
from aws_msk_iam_sasl_signer import MSKAuthTokenProvider
import os
import boto3


__version__ = "2.0.0"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
TIMED_OUT = "timed out"

systemVarFile = "../variables/system.yml"
# Global dictionaries to store thread-specific data
data_queues = {}
stop_flags = {}


class MSKTokenProvider:
    def token(self):
        account_id = boto3.client("sts").get_caller_identity()["Account"]
        print("aws account id::", account_id)

        try:
            if "GITLAB_CI" in os.environ:
                oauth2_token, _ = MSKAuthTokenProvider.generate_auth_token_from_role_arn(
                    "eu-west-2", "arn:aws:iam::" + str(account_id) + ":role/jlr-gdd-seit-iam-msk-role"
                )
            else:
                oauth2_token, _ = MSKAuthTokenProvider.generate_auth_token("eu-west-2", aws_debug_creds=True)

        except Exception as e:
            print("Error generating auth token: %s", e)
            raise
        return oauth2_token


tp = MSKTokenProvider()


@not_keyword
def connect_kafka_consumer(
    topic,
    timeout_s,
    brokers="kafka-bootstrap-servers",
):
    system_vars = BaseConfig(parse_config(systemVarFile))
    kafkaBootstrapServers = getattr(system_vars, brokers)
    kafkaBootstrapServers = kafkaBootstrapServers.replace("9094", "9098")
    print(kafkaBootstrapServers)
    consumer_timeout_ms = int(timeout_s) * 1000
    clientid = "e2e-automated-test-" + (str(uuid.uuid4().fields[-1])[:8])
    try:
        consumer = KafkaConsumer(
            topic,
            client_id=clientid,
            bootstrap_servers=kafkaBootstrapServers,
            security_protocol="SASL_SSL",
            sasl_mechanism="OAUTHBEARER",
            sasl_oauth_token_provider=tp,
            value_deserializer=lambda m: json.loads(m.decode("utf-8")),
            consumer_timeout_ms=consumer_timeout_ms,
        )
    except KafkaError as ke:
        print("kafka.errors." + str(ke) + ":: " + str(ke))
        return -1

    except Exception as e:
        print("kafka FAILED")
        print(str(e))
        return -1

    return consumer


@not_keyword
def search_index_of_data(data, data_id):
    for x in range(len(data)):
        if (data[x]["data_id"]) == data_id:
            print("data found in index: " + str(x))
            return x

    print("data id: " + str(data_id) + " not in kafka data")
    return -1


@not_keyword
def subscribe_to_topic_as_thread(
    unique_id,
    topic,
    timeout_s,
    queue,
    unqiueIdKey="unique_id",
    brokers="kafka-bootstrap-servers",
    returnTimestamp=False,
):
    """
    Not a robot keyword.\n
    Function to be started as a thread.\n
    Used to consume a kafka topic whilst performing another action.\n
    Returns data in a queue\n\n

    Example:\n
        import threading, queue\n
        \n
        q = queue.Queue()\n
        thread_function = kafkaConsumerLib.SubscribeToTopic\n
        x = threading.Thread(target=thread_function, args=(vin, topic, timeout_s, q), daemon=True)\n
        x.start()\n
        time.sleep(3)\n
        # then send query file  \n
        statusCode = send_config(configId, vin, proxy)\n
        x.join()\n
        result = q.get()\n
    """
    consumer = connect_kafka_consumer(topic, timeout_s, brokers=brokers)
    print(str(consumer))

    end_epoch_time = int(time.time()) + int(timeout_s)
    print(end_epoch_time)

    dataValue = "-1"
    if consumer != -1:
        print(consumer.topics())
        for msg in consumer:
            msg_value = msg.value
            epoch_time = int(time.time())
            if end_epoch_time < epoch_time:
                print(TIMED_OUT)
                break
            if msg_value[unqiueIdKey] == unique_id:
                print(msg_value[unqiueIdKey])
                msg_value = msg.value
                print(msg_value)
                if returnTimestamp is False:
                    dataValue = msg_value
                    queue.put(dataValue)
                else:
                    timestamp = int(time.time() * 1000)
                    print("kafka message timestamp:: ", timestamp)
                    queue.put(timestamp)  # for latency test
                break
    else:
        print("Failed to connect to kafka")

    queue.put(dataValue)


@not_keyword
def subscribe_to_topic(
    unique_id,
    topic,
    timeout_s,
    unqiueIdKey="unique_id",
    brokers="kafka-bootstrap-servers",
):
    """
    Not a robot keyword.\n\n

    unique_id - optional, if blank returns messages for all vehicles.

    """
    consumer = connect_kafka_consumer(topic, timeout_s, brokers=brokers)

    end_epoch_time = int(time.time()) + int(timeout_s)

    dataValue = "-1"
    if consumer != -1:
        for msg in consumer:
            msg_value = msg.value
            epoch_time = int(time.time())
            if end_epoch_time < epoch_time:
                print(TIMED_OUT)
                break

            if unique_id == "":
                dataValue = msg.value
                break
            elif msg_value[unqiueIdKey] == unique_id:
                dataValue = msg.value
                print(dataValue)
                # break
    else:
        print("Failed to connect to kafka")

    return dataValue


@keyword("Count Number Of Signals")
def count_number_of_signals(data):
    """
    Returns the number of signals in VCDP enriched data.

    Examples:
    | ${result}= | Count Number Of Signals| data |

    """
    if not isinstance(data, dict):
        json_object = json.loads(data)
    else:
        json_object = data
    c = 0
    for x in range(len(json_object["data"])):
        if json_object["data"][x]["data_id"]:
            c = c + 1

    print("Number of data ids: " + str(c))
    return c


@keyword("Get Kafka Value")
def get_kafka_value(unique_id, topic, data_id, timeout_s):
    """
    Connects to VCDP Kafka consumer and returns the value for the speficied data id.

    Will fail with a 'Kafka Consummer Connection Error' if there is any issue connecting.

    Will return -1 if no vaule found for the data id.

    Examples:
    | ${result}= | Get Kafka Value | TSTLRGBL663001234 | DEMO_VA_CAN_DATA  | S00668 | 10 |
    | ${result}= | Get Kafka Value | TSTLRGBL663001234 | DEMO_VA_DIAGNOSTIC_DATA | D00642 | 120 |

    """
    dataValue = -1
    consumer = connect_kafka_consumer(topic, timeout_s)
    starttime = time.time()
    timeout = starttime + int(timeout_s)
    for msg in consumer:
        msg_value = msg.value
        if msg_value["unique_id"] == unique_id:
            print(msg_value["unique_id"])
            data = msg_value["data"]
            print(data)
            dataIndex = search_index_of_data(data, data_id)
            if dataIndex != -1:
                dataValue = msg_value["data"][dataIndex]["samples"][0]["value"]
                print(dataValue)
                break

        if time.time() > timeout:
            print(TIMED_OUT)
            dataValue = -1
            break

    return dataValue


@keyword("Get Kafka Query")
def get_kafka_query(unique_id, topic, query_id, timeout_s):
    """
    Connects to VCDP Kafka consumer and returns the data for the speficied query id.

    Will fail with a 'Kafka Consummer Connection Error' if there is any issue connecting.

    Will return -1 if no query is found.

    Examples:
    | ${result}= | Get Kafka Quer | TSTLRGBL663001234 | DEMO_VA_CAN_DATA  | Q10001 | 10 |

    """
    consumer = connect_kafka_consumer(topic, timeout_s)
    starttime = time.time()
    timeout = starttime + int(timeout_s)
    for msg in consumer:
        msg_value = msg.value
        if msg_value["unique_id"] == unique_id:
            if msg_value["query_id"] == query_id:
                print(msg_value["data"])
                return msg_value["data"]

        if time.time() > timeout:
            print(TIMED_OUT)
            break
    return -1


@keyword("Get Data Value From Query Data")
def get_data_value_from_query_data(queryData, data_id):
    """
    Returns the value for a data_id from query data.

    Will return -1 if no query is found.

    Example query data:

    [{'offset': 0, 'data_id': 'S00668', 'name': 'PowerMode', 'publisher': ['BCM_H'], 'scale': 1, 'samples': [{'timestamp_ms': 1640099390743, 'type': 'DATA_FLOAT', 'value': 7}]}, {'offset': 0, 'data_id': 'S00060', 'name': 'BrakeFluidLevelWarning', 'publisher': ['BCM_H'], 'scale': 1, 'samples': [{'timestamp_ms': 1640030821893, 'type': 'DATA_FLOAT', 'value': 1}]}]


    Examples:
    | ${result}= | Get Data Value From Query Data | ${queryData} | S00060 |

    """  # noqa: E501 disable line length check for comment
    dataIndex = search_index_of_data(queryData, data_id)
    if dataIndex != -1:
        dataValue = queryData[dataIndex]["samples"][0]["value"]
        print(dataValue)
        return dataValue
    else:
        return -1


@keyword("No data for VIN received in kafka")
def no_data_received_in_kafka(unique_id, topic, timeout_s):
    """
    Connects to VCDP Kafka consumer and checks that no data is recieved for the specified VIN

    Will fail with a 'Kafka Consummer Connection Error' if there is any issue connecting.


    Examples:
    | ${result}= | Get Kafka Value | TSTLRGBL663001234 | DEMO_VA_CAN_DATA  |  10 |
    | ${result}= | Get Kafka Value | TSTLRGBL663001234 | DEMO_VA_DIAGNOSTIC_DATA |  120 |
    """
    data_value = -1
    consumer = connect_kafka_consumer(topic, timeout_s)
    starttime = time.time()
    timeout = starttime + int(timeout_s)
    for msg in consumer:
        msg_value = msg.value

        if msg_value["unique_id"] == unique_id:
            print(msg_value["unique_id"])
            data = msg_value["data"]
            print(data)
            data_value = data
            break

        if time.time() > timeout:
            print(TIMED_OUT)
            data_value = -1
            break

    return data_value


@not_keyword
def subscribe_to_topic_without_timeout(thread_uuid, json_key, json_value, topic, timeout_s, brokers=""):
    """
    This method is used to subscribe to a VCDP kafka topic and store all the incoming messages in the queue
    with no timeout, as it is required to be executed in a separate thread.

    This is not a separate robot keyword as it is been called into the method which starts a new thread.

    Parameters:
    threadUuid: auto generated uuid for identification on different thread
    jsonKey: Search the key string in the json data.
    jsonValue: Should match the value provided for the respective key.
    topic: Kafka Topic name/variable
    timeout_s: Timeout is used for Kafka connectivity timeout. If consumer variable is empty even after the timeout
    mentioned, it breaks the thread.
    """
    # Create a new data queue for this thread
    data_queue = queue.Queue()
    data_queues[thread_uuid] = data_queue  # Store the queue with thread ID as key
    stop_flags[thread_uuid] = False  # Initialize stop flag for this thread

    consumer = connect_kafka_consumer(topic, timeout_s, brokers=brokers)
    while not stop_flags[thread_uuid]:
        if consumer != -1:
            for msg in consumer:
                msg_value = msg.value
                if msg_value[json_key] == json_value:
                    data_queue.put(msg_value)
                if stop_flags[thread_uuid]:
                    break
        else:
            print(
                "Failed to connect to kafka even after ",
                timeout_s,
                " seconds. Please check your AWS connection",
            )
            break

    return data_queue


@keyword("Subscribe to a topic in a thread")
def start_thread(json_key, json_value, topic, timeout_s, brokers=""):
    """
    This method is used to start the thread. Currently it is used to start a new thread for the subscription
    of the topic. Which will read all the incoming messages and stores the data in a queue based on
    jsonKey & jsonValue value provided and return the auto generate uuid for thread identification.

    Examples:
    ${inputThread} = Subscribe to a topic in a thread
    ...    vuid
    ...    49117609-d15a-49ac-b205-bda3440af8d6
    ...    ${kafkaDataTopic}
    ...    60
    """
    # Creating a random uuid for thread identification purpose
    threadUuid = str(uuid.uuid4().fields[-1])[:8]

    # Start the thread
    kafkaThread = threading.Thread(
        target=subscribe_to_topic_without_timeout,
        args=(threadUuid, json_key, json_value, topic, timeout_s, brokers),
    )
    kafkaThread.start()
    print("Thread has been started at ", datetime.fromtimestamp(time.time()), "!!!!")
    return threadUuid


@keyword("Stop the thread")
def stop_thread(threadUuid):
    """
    Used to stop the thread which was started to subscribe the topic.

    Example:
    Stop the thread    ${inputThread}

    Parameters:
    uuid = value comes from start_thread method
    """
    global stop_flags
    if threadUuid in stop_flags:
        # Set the stop flag for this thread
        stop_flags[threadUuid] = True
        print("Thread stop flag has been set to True for thread ID:", threadUuid)
    else:
        print("Thread ID not found in stop_flags:", threadUuid)
    print("Thread has been stopped at ", datetime.fromtimestamp(time.time()), "!!!!")


@keyword("Get Kafka Message for a topic from the thread")
def get_kafka_messages(threadUuid):
    """
    Get all the messages from the data queue after stopping the thread.

    Parameters:
    uuid = value comes from start_thread method

    Example:
    ${result} =    Get Kafka Message for a topic from the thread

    Example sequence of subscribing to a topic in a thread to stopping and collecting data would be:
    ${inputThread} = Subscribe to a topic in a thread
    ...
    ...    ${kafkaDataTopic}
    ...    60
    Stop the thread ${inputThread}
    ${result} =    Get Kafka Message for a topic from the thread    ${inputThread}
    """
    print(f"Getting data for uuid {threadUuid}")
    global data_queues
    result = []
    if threadUuid in data_queues:
        # Retrieve the data queue for this thread
        data_queue = data_queues[threadUuid]
        while not data_queue.empty():
            result.append(data_queue.get())

    return result
