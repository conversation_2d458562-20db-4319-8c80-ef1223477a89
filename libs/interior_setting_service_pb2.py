# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: interior_setting_service.proto
# Protobuf Python Version: 5.28.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
# from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# _runtime_version.ValidateProtobufRuntimeVersion(
#     _runtime_version.Domain.PUBLIC,
#     5,
#     28,
#     2,
#     '',
#     'interior_setting_service.proto'
# )
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1einterior_setting_service.proto\x12\x18interior_setting_service\"/\n-GetChildPresenceDetectionSettingStatusRequest\"\x98\x03\n.GetChildPresenceDetectionSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"\x8c\x01\n-SetChildPresenceDetectionSettingStatusRequest\x12[\n\x1b\x64river_assist_feature_state\x18\x01 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\xc3\x01\n.SetChildPresenceDetectionSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\x93\x03\n)NotifyChildPresenceDetectionSettingStatus\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\")\n\'GetDriverMonitoringSettingStatusRequest\"\x92\x03\n(GetDriverMonitoringSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"\x86\x01\n\'SetDriverMonitoringSettingStatusRequest\x12[\n\x1b\x64river_assist_feature_state\x18\x01 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\xbd\x01\n(SetDriverMonitoringSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\x8d\x03\n#NotifyDriverMonitoringSettingStatus\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"&\n$GetFatigueAssistSettingStatusRequest\"\x8f\x03\n%GetFatigueAssistSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"\x83\x01\n$SetFatigueAssistSettingStatusRequest\x12[\n\x1b\x64river_assist_feature_state\x18\x01 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\xba\x01\n%SetFatigueAssistSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\x8a\x03\n NotifyFatigueAssistSettingStatus\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"(\n&GetAttentionAssistSettingStatusRequest\"\x91\x03\n\'GetAttentionAssistSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"\x85\x01\n&SetAttentionAssistSettingStatusRequest\x12[\n\x1b\x64river_assist_feature_state\x18\x01 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\xbc\x01\n\'SetAttentionAssistSettingStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\"\x8c\x03\n\"NotifyAttentionAssistSettingStatus\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12[\n\x1b\x64river_assist_feature_state\x18\x02 \x01(\x0e\x32\x36.interior_setting_service.EnumDriverAssistFeatureState\x12i\n\"driver_assist_feature_availability\x18\x03 \x01(\x0e\x32=.interior_setting_service.EnumDriverAssistFeatureAvailability\x12h\n\x1e\x64river_assist_feature_substate\x18\x04 \x01(\x0e\x32@.interior_setting_service.EnumInteriorDriveAssistFeatureSubstate\"(\n&GetChildPresenceDetectionStatusRequest\"\xb1\x01\n\'GetChildPresenceDetectionStatusResponse\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12P\n\x15\x63hild_presence_status\x18\x02 \x01(\x0e\x32\x31.interior_setting_service.EnumChildPresenceStatus\"\xac\x01\n\"NotifyChildPresenceDetectionStatus\x12\x34\n\x06status\x18\x01 \x01(\x0e\x32$.interior_setting_service.EnumStatus\x12P\n\x15\x63hild_presence_status\x18\x02 \x01(\x0e\x32\x31.interior_setting_service.EnumChildPresenceStatus*\x98\x03\n\nEnumStatus\x12\x1b\n\x17\x45NUM_STATUS_UNSPECIFIED\x10\x00\x12\x12\n\x0e\x45NUM_STATUS_OK\x10\x01\x12\x1d\n\x19\x45NUM_STATUS_DATA_DEGRADED\x10\x02\x12\x1f\n\x1b\x45NUM_STATUS_DATA_UNRELIABLE\x10\x03\x12 \n\x1c\x45NUM_STATUS_DATA_UNAVAILABLE\x10\x04\x12+\n\'ENUM_STATUS_ERROR_INVALID_SERVICE_STATE\x10\x05\x12+\n\'ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE\x10\x06\x12/\n+ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION\x10\x07\x12)\n%ENUM_STATUS_ERROR_MISSING_INPUT_FIELD\x10\x08\x12)\n%ENUM_STATUS_ERROR_INVALID_INPUT_FIELD\x10\t\x12\x16\n\x12\x45NUM_STATUS_NOT_OK\x10\n*\x82\x01\n\x1c\x45numDriverAssistFeatureState\x12%\n!ENUM_DA_FEATURE_STATE_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_DA_FEATURE_STATE_OFF\x10\x01\x12\x1c\n\x18\x45NUM_DA_FEATURE_STATE_ON\x10\x02*\xad\x01\n#EnumDriverAssistFeatureAvailability\x12,\n(ENUM_DA_FEATURE_AVAILABILITY_UNSPECIFIED\x10\x00\x12*\n&ENUM_DA_FEATURE_AVAILABILITY_AVAILABLE\x10\x01\x12,\n(ENUM_DA_FEATURE_AVAILABILITY_UNAVAILABLE\x10\x02*\xaa\x02\n&EnumInteriorDriveAssistFeatureSubstate\x12(\n$ENUM_DA_FEATURE_SUBSTATE_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_DA_FEATURE_SUBSTATE_NORMAL\x10\x01\x12$\n ENUM_DA_FEATURE_SUBSTATE_FAILURE\x10\x02\x12\x32\n.ENUM_DA_FEATURE_SUBSTATE_COUNTRY_NOT_AVAILABLE\x10\x03\x12+\n\'ENUM_DA_FEATURE_SUBSTATE_SENSOR_BLOCKED\x10\x04\x12*\n&ENUM_DA_FEATURE_SUBSTATE_ACTIVE_IN_USE\x10\x05*\xcd\x01\n\x17\x45numChildPresenceStatus\x12#\n\x1f\x45NUM_CHILD_PRESENCE_UNSPECIFIED\x10\x00\x12 \n\x1c\x45NUM_CHILD_PRESENCE_DETECTED\x10\x01\x12$\n ENUM_CHILD_PRESENCE_NOT_DETECTED\x10\x02\x12&\n\"ENUM_CHILD_PRESENCE_DETECTED_FINAL\x10\x03\x12\x1d\n\x19\x45NUM_CHILD_PRESENCE_FAULT\x10\x04\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'interior_setting_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_ENUMSTATUS']._serialized_start=5176
  _globals['_ENUMSTATUS']._serialized_end=5584
  _globals['_ENUMDRIVERASSISTFEATURESTATE']._serialized_start=5587
  _globals['_ENUMDRIVERASSISTFEATURESTATE']._serialized_end=5717
  _globals['_ENUMDRIVERASSISTFEATUREAVAILABILITY']._serialized_start=5720
  _globals['_ENUMDRIVERASSISTFEATUREAVAILABILITY']._serialized_end=5893
  _globals['_ENUMINTERIORDRIVEASSISTFEATURESUBSTATE']._serialized_start=5896
  _globals['_ENUMINTERIORDRIVEASSISTFEATURESUBSTATE']._serialized_end=6194
  _globals['_ENUMCHILDPRESENCESTATUS']._serialized_start=6197
  _globals['_ENUMCHILDPRESENCESTATUS']._serialized_end=6402
  _globals['_GETCHILDPRESENCEDETECTIONSETTINGSTATUSREQUEST']._serialized_start=60
  _globals['_GETCHILDPRESENCEDETECTIONSETTINGSTATUSREQUEST']._serialized_end=107
  _globals['_GETCHILDPRESENCEDETECTIONSETTINGSTATUSRESPONSE']._serialized_start=110
  _globals['_GETCHILDPRESENCEDETECTIONSETTINGSTATUSRESPONSE']._serialized_end=518
  _globals['_SETCHILDPRESENCEDETECTIONSETTINGSTATUSREQUEST']._serialized_start=521
  _globals['_SETCHILDPRESENCEDETECTIONSETTINGSTATUSREQUEST']._serialized_end=661
  _globals['_SETCHILDPRESENCEDETECTIONSETTINGSTATUSRESPONSE']._serialized_start=664
  _globals['_SETCHILDPRESENCEDETECTIONSETTINGSTATUSRESPONSE']._serialized_end=859
  _globals['_NOTIFYCHILDPRESENCEDETECTIONSETTINGSTATUS']._serialized_start=862
  _globals['_NOTIFYCHILDPRESENCEDETECTIONSETTINGSTATUS']._serialized_end=1265
  _globals['_GETDRIVERMONITORINGSETTINGSTATUSREQUEST']._serialized_start=1267
  _globals['_GETDRIVERMONITORINGSETTINGSTATUSREQUEST']._serialized_end=1308
  _globals['_GETDRIVERMONITORINGSETTINGSTATUSRESPONSE']._serialized_start=1311
  _globals['_GETDRIVERMONITORINGSETTINGSTATUSRESPONSE']._serialized_end=1713
  _globals['_SETDRIVERMONITORINGSETTINGSTATUSREQUEST']._serialized_start=1716
  _globals['_SETDRIVERMONITORINGSETTINGSTATUSREQUEST']._serialized_end=1850
  _globals['_SETDRIVERMONITORINGSETTINGSTATUSRESPONSE']._serialized_start=1853
  _globals['_SETDRIVERMONITORINGSETTINGSTATUSRESPONSE']._serialized_end=2042
  _globals['_NOTIFYDRIVERMONITORINGSETTINGSTATUS']._serialized_start=2045
  _globals['_NOTIFYDRIVERMONITORINGSETTINGSTATUS']._serialized_end=2442
  _globals['_GETFATIGUEASSISTSETTINGSTATUSREQUEST']._serialized_start=2444
  _globals['_GETFATIGUEASSISTSETTINGSTATUSREQUEST']._serialized_end=2482
  _globals['_GETFATIGUEASSISTSETTINGSTATUSRESPONSE']._serialized_start=2485
  _globals['_GETFATIGUEASSISTSETTINGSTATUSRESPONSE']._serialized_end=2884
  _globals['_SETFATIGUEASSISTSETTINGSTATUSREQUEST']._serialized_start=2887
  _globals['_SETFATIGUEASSISTSETTINGSTATUSREQUEST']._serialized_end=3018
  _globals['_SETFATIGUEASSISTSETTINGSTATUSRESPONSE']._serialized_start=3021
  _globals['_SETFATIGUEASSISTSETTINGSTATUSRESPONSE']._serialized_end=3207
  _globals['_NOTIFYFATIGUEASSISTSETTINGSTATUS']._serialized_start=3210
  _globals['_NOTIFYFATIGUEASSISTSETTINGSTATUS']._serialized_end=3604
  _globals['_GETATTENTIONASSISTSETTINGSTATUSREQUEST']._serialized_start=3606
  _globals['_GETATTENTIONASSISTSETTINGSTATUSREQUEST']._serialized_end=3646
  _globals['_GETATTENTIONASSISTSETTINGSTATUSRESPONSE']._serialized_start=3649
  _globals['_GETATTENTIONASSISTSETTINGSTATUSRESPONSE']._serialized_end=4050
  _globals['_SETATTENTIONASSISTSETTINGSTATUSREQUEST']._serialized_start=4053
  _globals['_SETATTENTIONASSISTSETTINGSTATUSREQUEST']._serialized_end=4186
  _globals['_SETATTENTIONASSISTSETTINGSTATUSRESPONSE']._serialized_start=4189
  _globals['_SETATTENTIONASSISTSETTINGSTATUSRESPONSE']._serialized_end=4377
  _globals['_NOTIFYATTENTIONASSISTSETTINGSTATUS']._serialized_start=4380
  _globals['_NOTIFYATTENTIONASSISTSETTINGSTATUS']._serialized_end=4776
  _globals['_GETCHILDPRESENCEDETECTIONSTATUSREQUEST']._serialized_start=4778
  _globals['_GETCHILDPRESENCEDETECTIONSTATUSREQUEST']._serialized_end=4818
  _globals['_GETCHILDPRESENCEDETECTIONSTATUSRESPONSE']._serialized_start=4821
  _globals['_GETCHILDPRESENCEDETECTIONSTATUSRESPONSE']._serialized_end=4998
  _globals['_NOTIFYCHILDPRESENCEDETECTIONSTATUS']._serialized_start=5001
  _globals['_NOTIFYCHILDPRESENCEDETECTIONSTATUS']._serialized_end=5173
# @@protoc_insertion_point(module_scope)
