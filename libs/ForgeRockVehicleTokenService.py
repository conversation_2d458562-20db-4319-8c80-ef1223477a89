#!/usr/bin python3
import requests
from json import JSONDecodeError
import json
from datetime import datetime as dt
from pyaml_env import BaseConfig, parse_config
import time
import os
import hashlib
from authlib.jose import jwt
import uuid


def generate_vehicle_client_id(vin, tcu, algorithm="sha256"):
    # generate client id which is hash value of VIN and TCU
    h = hashlib.new(algorithm)
    h.update(str(vin + tcu).encode("utf-8"))
    vehicle_client_id = h.hexdigest()
    return vehicle_client_id


def setup_globals(calledAsBinaryFile):
    # binary doesn't like relative paths and have different paths to the robot tests
    global systemFile, system_vars, vehicle_private_key, vehicle_certfile, vehicle_keyfile
    global jwt_claims, token_request_header, token_request_body, jwt_header
    if calledAsBinaryFile:
        systemFile = os.path.abspath(os.path.join(os.path.dirname(__file__), "variables/system.yml"))
    else:
        systemFile = "../variables/system.yml"


def get_signed_jwt(claim, key, alg):
    return jwt.encode({"alg": alg}, claim, key)


def read_json_file(json_file):
    return json.load(open(json_file))


def update_claim(claim, vehicle_client_id, claim_aud):
    claim["iat"] = int(dt.now().timestamp())
    claim["nbf"] = int(dt.now().timestamp())
    claim["exp"] = int(dt.now().timestamp()) + 600
    claim["iss"] = vehicle_client_id
    claim["sub"] = vehicle_client_id
    claim["aud"] = claim_aud
    claim["jti"] = str(uuid.uuid4())
    return claim


def get_timestamp():
    return str(int(time.time() * 1000))


def get_forgerock_token(
    api_endpoint,
    vehicle_client_id,
    token_request_header,
    token_request_body,
    vehicle_certfile="",
    vehicle_keyfile="",
    calledAsBinaryFile=True,
    alg="",
):
    header = read_json_file(token_request_header)
    payload = read_json_file(token_request_body)
    payload["client_assertion"] = get_client_assertion(vehicle_client_id, calledAsBinaryFile, alg)
    # Sending request to FR with signed jwt
    response = requests.post(url=api_endpoint, headers=header, data=payload, cert=(vehicle_certfile, vehicle_keyfile))
    try:
        access_token = json.loads(response.content)["access_token"]
    except (KeyError, JSONDecodeError) as error:
        print(error)
        access_token = ""
    return access_token


def get_client_assertion(vehicle_client_id, calledAsBinaryFile=True, alg=""):
    setup_globals(calledAsBinaryFile)
    system_vars = BaseConfig(parse_config(systemFile))
    vehicle_private_key = str(getattr(system_vars, "vehicle_private_keyfile"))
    claim_aud = getattr(system_vars, "keycloak_url")
    jwt_claims = str(getattr(system_vars, "jwt_claims"))
    vehicle_key = read_json_file(vehicle_private_key)
    jwt_token_claims = read_json_file(jwt_claims)
    updated_jwt_token_claims = update_claim(jwt_token_claims, vehicle_client_id, claim_aud)
    return get_signed_jwt(updated_jwt_token_claims, vehicle_key, alg)


def get_vehicle_passphrass_mqtt_client_id(api_endpoint, access_token, vehicle_certfile, vehicle_keyfile):
    Token = "Bearer " + access_token
    header = {"Authorization": Token}

    # Sending request to FR with token to retrieve passprase
    response = requests.get(url=api_endpoint, headers=header, cert=(vehicle_certfile, vehicle_keyfile))
    passphrase = json.loads(response.content)["passphrase"]
    mqtt_client_id = json.loads(response.content)["id"]
    return passphrase, mqtt_client_id


def get_token_passphrase_mqtt_client_id(vehicle_client_id, calledAsBinaryFile=True, alg="RS256"):
    """
    Function to get forgerock token, passphrase and mqtt client id of vehicles
    to connect to hive mq topic
    """
    setup_globals(calledAsBinaryFile)
    system_vars = BaseConfig(parse_config(systemFile))
    vehicle_certfile = str(getattr(system_vars, "vehicle_certfile"))
    vehicle_keyfile = str(getattr(system_vars, "vehicle_keyfile"))
    token_request_header = str(getattr(system_vars, "token_request_header"))
    token_request_body = str(getattr(system_vars, "token_request_body"))
    # forgerock token request variables
    forgerock_token_url = getattr(system_vars, "forgerock_token_url")
    forgerock_passphrase_url = getattr(system_vars, "forgerock_passphrase_url")

    # Request token from FR
    token = get_forgerock_token(
        forgerock_token_url,
        vehicle_client_id,
        token_request_header,
        token_request_body,
        vehicle_certfile,
        vehicle_keyfile,
        calledAsBinaryFile,
        alg=alg,
    )
    # Request to FR for passphrase
    passphrase, mqtt_client_id = get_vehicle_passphrass_mqtt_client_id(forgerock_passphrase_url, token, vehicle_certfile, vehicle_keyfile)
    return token, passphrase, mqtt_client_id


def get_fr_token_wireless_car(iss_sub, calledAsBinaryFile=True):
    """
    Function to get forgerock token for wirless car connections
    This function uses the PS256 signing algoritham
    """
    setup_globals(calledAsBinaryFile)
    system_vars = BaseConfig(parse_config(systemFile))
    token_request_header = str(getattr(system_vars, "token_request_header"))
    token_request_body = str(getattr(system_vars, "token_request_body"))
    # forgerock token request variables
    forgerock_token_url = getattr(system_vars, "forgerock_token_url")

    # Request token from FR
    token = get_forgerock_token(
        api_endpoint=forgerock_token_url,
        vehicle_client_id=iss_sub,
        token_request_header=token_request_header,
        token_request_body=token_request_body,
        vehicle_certfile=str(getattr(system_vars, "masterdata_certificate")),
        vehicle_keyfile=str(getattr(system_vars, "masterdata_key")),
        calledAsBinaryFile=calledAsBinaryFile,
        alg="PS256",
    )
    return token
