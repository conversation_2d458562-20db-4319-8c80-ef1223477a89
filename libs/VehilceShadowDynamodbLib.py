import boto3
import boto3.dynamodb
import time
import traceback
from robot.api.deco import keyword
import os

# Table Name
table_name = "AppSyncOneAppShadowState"


def connect_to_dynamodb():
    try:
        if "GITLAB_CI" in os.environ:
            session = boto3.session.Session()
        else:
            session = boto3.session.Session(profile_name=os.environ.get("AWS_PROFILE"))

        dynamodb = session.resource(service_name="dynamodb", region_name="eu-west-2")

    except ConnectionError as ce:
        print("Database Connection error" + str(ce) + ":: " + str(ce))
        return -1

    except Exception:
        print("Database connection FAILED")
        traceback.print_exc()
        return -1

    print("Connected to DynamoDB")
    return dynamodb


@keyword("Get All TableName")
def get_all_table_name():
    dynamodb = connect_to_dynamodb()

    tables = list(dynamodb.tables.all())
    print(tables)
    return tables


@keyword("Get Item In Table")
def get_item_in_table(VehicleId, SortKey, table_name="AppSyncOneAppShadowState"):
    dynamodb = connect_to_dynamodb()
    print(dynamodb)

    table = dynamodb.Table(table_name)

    response = table.get_item(Key={"VehicleId": VehicleId, "SignalLabel": SortKey}).get("Item")

    print(response)
    return response


@keyword("Get Vehicle Shadow for vehicle lib")
def get_vehicle_shadow(VehicleId, table_name="AppSyncOneAppShadowState"):
    dynamodb = connect_to_dynamodb()
    print(dynamodb)
    table = dynamodb.Table(table_name)
    response = table.query(
        KeyConditionExpression=boto3.dynamodb.conditions.Key("VehicleId").eq(VehicleId), ProjectionExpression="SignalLabel, ValueSource, EventTime"
    )

    print(response)
    return response


@keyword("Save Item In Table")
def save_item_in_table(VehicleId, SignalLabel, DataProductId, Value, ValueAbstract, ValueDetailed, VehiclePermitterId, TableName=table_name):
    dynamodb = connect_to_dynamodb()
    table = dynamodb.Table(TableName)

    eventTime = int(time.time() * 1000)
    expiryTime = eventTime + 86400000

    payload = {
        "VehicleId": VehicleId,
        "SignalLabel": SignalLabel,
        "DataProductId": DataProductId,
        "EventTime": int(eventTime),
        "ExpiryTime": int(expiryTime),
        "LastUpdatedTime": int(eventTime),
        "ValueSource": str(Value),
        "VehiclePermitterId": VehiclePermitterId,
    }

    if ValueDetailed != "null":
        payload["ValueDetailed"] = ValueDetailed

    if ValueAbstract != "null":
        payload["ValueAbstract"] = ValueAbstract

    print(payload)

    response = table.put_item(Item=payload)

    print(response)
    return response
