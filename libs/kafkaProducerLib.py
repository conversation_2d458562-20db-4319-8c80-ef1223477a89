#!/usr/bin python3

from kafka import KafkaProducer
from kafka.errors import KafkaError
from robot.api.deco import keyword, not_keyword
import json
import time
from datetime import datetime, timezone
import uuid
from pyaml_env import parse_config, BaseConfig
from aws_msk_iam_sasl_signer import MSKAuthTokenProvider
import os
import boto3

__version__ = "0.0.5"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.000Z"
TOPIC = "Topic:: "
EXPIRY_DATE = "2099-12-03T15:26:04.424Z"


system_var_file = "../variables/system.yml"


class MSKTokenProvider:
    def token(self):
        account_id = boto3.client("sts").get_caller_identity()["Account"]
        print("aws account id::", account_id)

        try:
            if "GITLAB_CI" in os.environ:
                oauth2_token, _ = MSKAuthTokenProvider.generate_auth_token_from_role_arn(
                    "eu-west-2", "arn:aws:iam::" + str(account_id) + ":role/jlr-gdd-seit-iam-msk-role"
                )
            else:
                oauth2_token, _ = MSKAuthTokenProvider.generate_auth_token("eu-west-2", aws_debug_creds=True)

        except Exception as e:
            print("Error generating auth token: %s", e)
            raise
        return oauth2_token


tp = MSKTokenProvider()


@not_keyword
def connect_kafka_producer(timeout_s, brokers="kafka-bootstrap-servers"):
    system_vars = BaseConfig(parse_config(system_var_file))
    kafkaBootstrapServers = getattr(system_vars, brokers)
    kafkaBootstrapServers = kafkaBootstrapServers.replace("9094", "9098")
    print(kafkaBootstrapServers)
    timeout_ms = int(timeout_s) * 1000
    clientid = "e2e-automated-test-" + (str(uuid.uuid4().fields[-1])[:8])
    try:
        producer = KafkaProducer(
            client_id=clientid,
            bootstrap_servers=kafkaBootstrapServers,
            security_protocol="SASL_SSL",
            sasl_mechanism="OAUTHBEARER",
            sasl_oauth_token_provider=tp,
            request_timeout_ms=timeout_ms,
        )

    except KafkaError as ke:
        print("kafka.errors." + str(ke) + ":: " + str(ke))
        return -1

    except Exception as e:
        print("kafka FAILED")
        print(str(e))
        return -1

    print("Connected to Kafka")
    return producer


@keyword("Initiate Vehicle Config")
def init_veh_config(vin, topic, timeout_s):
    producer = connect_kafka_producer(timeout_s)
    message = {"vin": vin, "origin": "e2e test suite"}
    jsonPayload = json.dumps(message)
    print(jsonPayload)

    producer.send(topic, key=str.encode(vin), value=json.dumps(message).encode("utf-8"))
    producer.flush()


@keyword("Remove Vehicle From VCDP")
def remove_vehicle(vin):
    topic = "LIVE.sota-dt.incoming-events"
    producer = connect_kafka_producer(10)

    payload = {"action": "UNPROVISION_VIN"}

    producer.send(topic, key=str.encode(vin), value=json.dumps(payload).encode("utf-8"))
    producer.flush()


@not_keyword
def add_custom_vehicles(
    start_vin,
    number_of_vins,
    vin_pre_fix,
    custom_vehicle_json,
):
    """
    Used to add a new Custom vehicle defined in customVehicle.json into VCDP
    Define the start VIN number and the number of vehicles required.
    Publishes to the sota dt incoming msk topic

    """

    topic = "LIVE.sota-dt.incoming-events"
    producer = connect_kafka_producer(10)

    with open(custom_vehicle_json) as f:
        digital_veh = json.load(f)

    tcu_serial_prefix = "TSTVDC-"
    len_of_seq_vin = 17 - len(vin_pre_fix)
    print(len_of_seq_vin)

    for n in range(number_of_vins):
        next_vin = str(n + start_vin)
        vin = vin_pre_fix + next_vin.rjust(len_of_seq_vin, "0")
        tcuser = tcu_serial_prefix + next_vin.rjust(len_of_seq_vin, "0")
        print(vin)
        print(tcuser)
        digital_veh["data"]["vin"] = vin
        digital_veh["data"]["inventory"][0]["hardwareInventory"]["ecuSerialNumber"] = tcuser

        producer.send(topic, key=str.encode(vin), value=json.dumps(digital_veh).encode("utf-8"))
        producer.flush()


@keyword("Add a custom vehicle")
def add_custom_vehicle(custom_vehicle_json, vin="E2ETEST") -> str:
    """
    Used to add a new Custom vehicle defined in customVehicle.json into VCDP
    Define the start VIN number and the number of vehicles required.
    Publishes to the sota dt incoming msk topic

    Create VIN from unix timestamp, used for regression testing.

    """
    print(vin)
    if vin == "E2ETEST":
        vin_prefix = vin
        start_vin = int(time.time())
    else:
        vin_prefix, start_vin = vin[:11], vin[11:]

    print(vin_prefix)
    print(start_vin)
    add_custom_vehicles(
        int(start_vin),
        1,
        vin_prefix,
        custom_vehicle_json,
    )
    return vin_prefix + str(start_vin)


@keyword("Add Remove vehicle permission")
def add_remove_vehicle_consent(vin, give_permission, person_id="TEST-LIB-VEHICLE"):
    """
    Used to add or remove the permission for vehicle life cycle consent

    Examples:
    | Add Remove vehicle permission | TSTLRGBL663001234 | true |
    | Add Remove vehicle permission | [TSTLRGBL663001234] | false |

    """

    producer = connect_kafka_producer(10)

    now = datetime.now(timezone.utc).strftime(DATETIME_FORMAT)
    print(now)

    if type(vin) is list:
        vin = vin[0]

    print(type(vin), vin)

    system_vars = BaseConfig(parse_config(system_var_file))
    topic = getattr(system_vars, "kafkaDataProductPermissionTopic")
    print(TOPIC, topic)

    if give_permission == "singlePerm":
        permlist = [
            {
                "label": "L_App_Privacy",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "DpretryPerm":
        permlist = [
            {
                "label": "Dp_retry_Perm",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "multiplePerm" or give_permission == "eva2servicemode" or give_permission == "eva2transportmode":
        permlist = [
            {"label": "L_App_Privacy", "constraint": None, "expiry": EXPIRY_DATE},
            {"label": "A_App_Usage", "constraint": None, "expiry": EXPIRY_DATE},
        ]
    elif give_permission == "SmartCharging":
        permlist = [
            {
                "label": "A_3rdParty_SmartCharging",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "AAppUsage":
        permlist = [
            {
                "label": "A_App_Usage",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "geoTrip":
        permlist = [
            {"label": "A_JourneyLogging", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "C_R&D_Geo", "expiry": EXPIRY_DATE, "constraint": None},
        ]
    elif give_permission == "allPerm":
        permlist = [
            {"label": "L_App_Privacy", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "A_App_Usage", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "A_3rdParty_SmartCharging", "expiry": EXPIRY_DATE, "constraint": None},
        ]
    elif give_permission == "noPerm":
        permlist = []
    else:
        permlist = []

    payload = {
        "vehicle_id": vin,
        "vp_id": "MOCK-ASSOCIATION",
        "person_id": person_id,
        "exclusive_from": now,
        "exclusive_to": EXPIRY_DATE,
        "is_perm_list": permlist,
    }

    print(json.dumps(payload))
    producer.send(topic, key=str.encode(vin), value=json.dumps(payload).encode("utf-8"))
    producer.flush()


@keyword("Publish Vehicle Signal data to Kafka Product Factory Input")
def publish_vehicle_signaldata_to_kafka_product_factory_input(
    unique_id="",
    query_id="",
    signal_id="",
    value="",
    calc_end_time_ms=0,
    calc_duration_ms=0,
    calc_avg=0,
    calc_min_data_float=0,
    calc_max_data_float=0,
    calc_std_dev=0,
    calc_count=0,
):
    """
    Creates a singal messagedto be published kafka Product Factory Input
    to  for a vehicle float signal.
    Calculation is optional
    Example:

    | Publish Vehicle Signal data to Kafka Product Factory Inputl | Q9102010 | S00080 | 3 |
    | Publish Vehicle Signal data to Kafka Product Factory Input | Q9901009 | S00044 | 32 | 1680520130987 | 36432 | 32.769226 | 0 | 41 | 10.433551 | 7 |

    """  # noqa : E501

    try:
        value = float(value)
    except ValueError:
        print(str(signal_id) + " must be float got::" + str(value))
        return (-1, -1, -1)

    now = int(time.time() * 1000)

    system_vars = BaseConfig(parse_config(system_var_file))
    topic = getattr(system_vars, "kafkaDataTopic")
    print(TOPIC, topic)

    producer = connect_kafka_producer(10)

    json_kafka_input = {
        "unique_id": unique_id,
        "query_id": query_id,
        "data": [
            {
                "data_id": signal_id,
                "samples": [
                    {
                        "timestamp_ms": int(now),
                        "type": "DATA_FLOAT",
                        "value": int(value),
                    }
                ],
            }
        ],
        "event_timestamp_ms": int(now),
        "fleet_id": "",
    }

    print(json.dumps(json_kafka_input))
    producer.send(
        topic,
        key=str.encode(unique_id),
        value=json.dumps(json_kafka_input).encode("utf-8"),
    )
    producer.flush()

    return now


@keyword("Publish Initialisation message")
def publish_initialisation(vin, unique_id):
    producer = connect_kafka_producer(10)

    now = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S")
    print(now)

    topic = "LIVE.dv.vehicle-events"
    print(TOPIC, topic)

    payload = {
        "vin": vin,
        "unique_id": unique_id,
        "eventTypes": ["VEHICLE_CREATED"],
        "timestamp": now,
    }

    print(json.dumps(payload))

    producer.send(topic, key=str.encode(vin), value=json.dumps(payload).encode("utf-8"))

    producer.flush()


@keyword("Add Remove vehicle permission for Data Pipleline")
def add_remove_vehicle_consent_data_pipeline(vin, unique_id, give_permission, person_id="TEST-LIB-DP"):
    """
    Used to add or remove the permission for vehicle life cycle consent for Data Pipeline Team
    Examples:
    | Add Remove vehicle permission | TSTLRGBL663001234 | true |
    | Add Remove vehicle permission | TSTLRGBL663001234 | false |

    """

    producer = connect_kafka_producer(10)

    now = datetime.now(timezone.utc).strftime(DATETIME_FORMAT)
    print(now)

    system_vars = BaseConfig(parse_config(system_var_file))
    topic = getattr(system_vars, "kafkaPermissionTopic")
    print(TOPIC, topic)

    if give_permission == "singlePerm":
        permlist = [
            {
                "label": "A_App_Usage",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "LAppPrivacy":
        permlist = [
            {
                "label": "L_App_Privacy",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "SmartCharging":
        permlist = [
            {
                "label": "L_3rdParty_SmartCharging",
                "constraint": None,
                "expiry": EXPIRY_DATE,
            }
        ]
    elif give_permission == "multiplePerm":
        permlist = [
            {"label": "L_App_Privacy", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "A_App_Usage", "expiry": EXPIRY_DATE, "constraint": None},
        ]
    elif give_permission == "allPerm":
        permlist = [
            {"label": "L_App_Privacy", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "A_App_Usage", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "L_3rdParty_SmartCharging", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "C_R&D_Geo", "expiry": EXPIRY_DATE, "constraint": None},
        ]
    elif give_permission == "geoTrip":
        permlist = [
            {"label": "A_JourneyLogging", "expiry": EXPIRY_DATE, "constraint": None},
            {"label": "C_R&D_Geo", "expiry": EXPIRY_DATE, "constraint": None},
        ]
    else:
        permlist = []

    payload = {
        "vehicle_id": vin,
        "vp_id": "MOCK-ASSOCIATION",
        "person_id": person_id,
        "exclusive_from": now,
        "exclusive_to": EXPIRY_DATE,
        "is_perm_list": permlist,
        "vehicle_vuid": unique_id,
    }

    print(json.dumps(payload))
    producer.send(topic, key=str.encode(unique_id), value=json.dumps(payload).encode("utf-8"))
    producer.flush()


@keyword("Insert Json Data into Kafka Topic")
def insert_json_data_into_kafka_topic(topic, json_data, msg_title, brokers=""):
    """
    Insert the json content into the kafka topic from a file

    Example:
    Insert Json Data into Kafka Topic

    Example sequence of subscribing to a topic in a thread to stopiing and collecting data would be:
    Insert Json Data into Kafka Topic
    ...    LIVE.data-product-element.input
    ...    ../jsonschema/dataEnggDPQE.json
    ...    seit-6d119d85-a915-4ddb-bd63-28efc3978bb6
    """
    json_request_body = json.loads(json_data)

    print(json_request_body)

    producer = connect_kafka_producer(30, brokers)
    future = producer.send(topic, key=str.encode(msg_title), value=json.dumps(json_request_body).encode("utf-8"), partition=0)
    producer.flush()
    result = future.get(timeout=60)
    print("metadata is ", result)
    producer.close()
