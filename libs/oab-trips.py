import commonUtils
from robot.api.deco import keyword
import re
from urllib.parse import urlparse

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = commonUtils.get_url("oneapp-backend-url") + "/me/vehicles/"
DOWNLOAD_PDF_URL = commonUtils.get_url("download-trips-pdf-url")


# GET all trip data
@keyword("Get all trip data")
def get_trips(user_email, user_password, vehicle_id, from_date, to_date, approov=False, auth=True, language="en"):
    endpoint = ME_URI + f"{vehicle_id}/trips"
    queryParams = {"from_date": from_date, "to_date": to_date, "language": language}
    response = commonUtils.get_request_with_params(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE, params=queryParams)
    return response[0], response[1]


# GET a single trip
@keyword("Get a single trip")
def get_single_trip(user_email, user_password, vehicle_id, trip_id, approov=False, auth=True, language="en"):
    endpoint = ME_URI + f"{vehicle_id}/trips/{trip_id}"
    queryParams = {"language": language}
    response = commonUtils.get_request_with_params(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE, params=queryParams)
    return response[0], response[1]


# POST for download PDF of trips link in response
@keyword("Send request to Export trips for vehicle")
def export_trips_for_vehicle(user_email, user_password, vehicle_id, from_date, to_date, approov=False, auth=True, language="en"):
    endpoint = ME_URI + f"{vehicle_id}/trips/export"
    body = {"from_date": from_date, "to_date": to_date, "language": language}
    response = commonUtils.send_request_with_json_payload(user_email, user_password, body, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


@keyword("validate presignedUrl")
def validate_presignedurl(url):
    parsed_url = urlparse(url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
    if base_url != DOWNLOAD_PDF_URL:
        raise AssertionError(f"Base URL mismatch: expected '{DOWNLOAD_PDF_URL}', got '{base_url}'")

    # Validate path using regex
    path_regex = r"^/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/Trips.csv$"
    path_pattern = re.compile(path_regex)
    if not path_pattern.match(parsed_url.path):
        raise AssertionError(f"Url Path {parsed_url.path} does not match regex: {path_regex}")

    return True
