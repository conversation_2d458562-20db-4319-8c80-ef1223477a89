import json
from robot.api.deco import keyword


signalLabel_appsyncSignalName_Map = {
    "AlarmModeMS": "alarmMode",
    "CentralLockStatus": "lock",
    "CentralLockStatus3": "lock3",
    "EPBStatus_EUCD": "parkingBrake",
    "PowerMode": "powerMode",
    "DoorStatus#TailgateStatus": "tailgate",
    "DoorStatus#BonnetStatus": "bonnet",
    "DoorStatus#SideDoorStatus": "doors",
    "CabinCleaningActivStat": "cabinCleaning",
    "FrontSystemOnCmd": "frontSystemOnCmd",
    "SunroofOpen": "sunroof",
    "DriverRearWindowPos": "driverRearWindow",
    "DriverWindowPosition": "driverWindow",
    "PassRearWindowPos": "passengerRearWindow",
    "PassWindowPosition": "passengerWindow",
    "WiredConnectStatus": "evIsVehiclePluggedIn",
    "ChargingStatusDisp": "evCurrentChargingStatus",
    "BulkSoCTargetDisp": "evBatteryChargeTarget",
    "DistanceToVehicleService": "distanceToNextService",
    "TimeToVehicleService": "timeToNextService",
    "DPFAdditiveLevelLow": "dieselFilterAdditiveLevel",
    "PCMWarningUrea": "adBlueWarningLevel",
    "BrakePadWearLampReq": "brakePadWearIndicator",
    "BrakeFluidLevelWarning": "brakeFluidLevelWarning",
    "BrakeFluidLevelWarning2": "brakeFluidLevelWarning2",
    "WasherFluidLevelWarning": "washerFluidLevelWarning",
    "EngineOilLevelWarning": "engineOilLevelWarning",
    "ActualTyrePressureFL": "tyrePressureFrontLeft",
    "ActualTyrePressureFR": "tyrePressureFrontRight",
    "ActualTyrePressureRL": "tyrePressureRearLeft",
    "ActualTyrePressureRR": "tyrePressureRearRight",
    "TyrePressureWarning": "tyrePressureWarning",
    "ChargerACCurr2Actual": "evChargerAC2Current",
    "ChargerACCurrActual": "evChargerACCurrent",
    "ChargerDCCurrActual": "evChargerDCCurrent",
    "HVBattChargeRateKmDisp": "evHvBatteryChargeRateKmPerHour",
    "HVBattChrgeRateSOCDisp": "evHvBatteryChargeRatePercentagePerHour",
    "TimeToBulkSoCHoursDisp": "evHvBulkHoursRemainingTillCharged",
    "TimeToBulkSoCMinsDisp": "evHvBulkMinsRemainingTillCharged",
    "TimeToFullSoCHoursDisp": "evHvHoursRemainingTillCharged",
    "TimeToFullSoCMinsDisp": "evHvMinsRemainingTillCharged",
    "EVRangeDisp": "evRangeKmPerMin",
    "EVRangeDispPMZ": "evRangeKmPerMinPmz",
    "EVRangeSelectedSOC": "evRangeKm",
    "BSBattSOC": "evBatteryStateOfCharge",
    "HVBatteryUsableSOCDisp": "evHvBatteryUsableStateOfCharge",
    "FuelGaugePosition": "fuelGaugePosition",
    "FuelLevelIndicatedHS": "levelOfFuelInTank",
    "CombinedRangeDispPMZ": "combinedClusterVscPredictedRangeKmPmz",
    "CombinedRangeDisp": "combinedClusterVscPredictedRangeKm",
    "HVBattAvTempAtEvent": "evHvBatteryAvTemperature",
    "OdometerMasterValue": "currentMileage",
    "PM25Internal": "cabinAirQuality",
    "PreClimateStatus": "isPreConditioningCycleRunning",
    "InCarTemp": "currentCabinTemperature",
    "FrontTSetLeftCmd": "targetCabinTemperatureLeft",
    "FrontTSetRightCmd": "targetCabinTemperatureRight",
    "ChargePowerAvailable": "evChargePowerAvailable",
    "TimedChrgStartDateDisp_RAW": "evNextTimedChargingStartDayInMonth",
    "TimedCharge1Active": "evIs1stChargingOptionActive",
    "TimedCharge2Active": "evIs2ndChargingOptionActive",
    "TimedCharge3Active": "evIs3rdChargingOptionActive",
    "TimedCharge4Active": "evIs4thChargingOptionActive",
    "CarMode": "isTransportModeActive",
    "HiLoadOffsetFront": "heavyLoadRecommendedTyrePressureFront",
    "HiLoadOffsetRear": "heavyLoadRecommendedTyrePressureRear",
    "PlacardTyrePressureFr": "lightLoadRecommendedTyrePressureFront",
    "PlacardTyrePressureRe": "lightLoadRecommendedTyrePressureRear",
    "GPSLatitude": "lastKnownLatitude",
    "GPSLongitude": "lastKnownLongitude",
    "charging_status": "chargingStatus",
    "HeatedSeatFLModeRequest": "frontLeftSeatHeatRequest",
    "HeatedSeatFLRequest": "isFrontLeftSeatHeated",
    "HeatedSeatFRModeRequest": "frontRightSeatHeatRequest",
    "HeatedSeatFRRequest": "isFrontRightSeatHeated",
    "HeatedSteerWheelStatus": "isSteerWheelHeated",
    "DistanceToUreaService": "distanceToUreaService",
    "VolumeUreaRefill": "volumeUreaRefill",
    "HVBattChgCurrentLimit": "evHvBatteryChargeCurrentLimit",
}


@keyword("Get GraphQl Query Response Vehicle Data")
def get_value_abstract(jsonData, keyStr):
    print(jsonData)
    signalObject = json.loads(jsonData)
    print(signalObject)
    result = signalObject["data"]["getVehicleData"][keyStr]
    print(result)
    return result


@keyword("Get GraphQl Query Response Signal List Count")
def get_signal_list_count(jsonData):
    print(jsonData)
    signalObject = json.loads(jsonData)
    print(signalObject)
    result = len(signalObject["data"]["queryVehicleDataBySignalList"]["items"])
    print(result)
    return result


@keyword("Query Vehicle Data Count")
def query_vehicle_data(jsonData):
    print(jsonData)
    signalObject = json.loads(jsonData)
    print(signalObject)
    result = len(signalObject["data"]["queryVehicleData"]["items"])
    print(result)
    return result


@keyword("Query Vehicle Data Status")
def query_vehicle_data_status(jsonData, signalLabel):
    print(f"Query Vehicle Data Status jsonData: {jsonData}")
    signalObject = json.loads(jsonData)

    print(signalObject)

    appsync_name_for_signal = signalLabel_appsyncSignalName_Map[signalLabel]
    print(appsync_name_for_signal)

    result = signalObject["data"]["queryVehicleStatusData"][appsync_name_for_signal]

    if "VehicleId" in signalObject["data"]["queryVehicleStatusData"].keys():
        result["vehicleId"] = signalObject["data"]["queryVehicleStatusData"]["VehicleId"]

    print(result)
    return result
