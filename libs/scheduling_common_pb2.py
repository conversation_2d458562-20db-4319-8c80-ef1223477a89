# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: scheduling_common.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17scheduling_common.proto\x12\x11scheduling_common*\xe9\x01\n\x17\x45numVehicleWakeUpReason\x12#\n\x1f\x45NUM_WAKE_UP_REASON_UNSPECIFIED\x10\x00\x12%\n!ENUM_WAKE_UP_REASON_INLET_HEATING\x10\x01\x12 \n\x1c\x45NUM_WAKE_UP_REASON_CHARGING\x10\x02\x12/\n+ENUM_WAKE_UP_REASON_BATTERY_PRECONDITIONING\x10\x03\x12/\n+ENUM_WAKE_UP_REASON_CALCULATE_WAKE_UP_TIMES\x10\x04\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'scheduling_common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ENUMVEHICLEWAKEUPREASON']._serialized_start=47
  _globals['_ENUMVEHICLEWAKEUPREASON']._serialized_end=280
# @@protoc_insertion_point(module_scope)
