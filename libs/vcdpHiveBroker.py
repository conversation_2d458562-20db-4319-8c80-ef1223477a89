#!/usr/bin python3

import paho.mqtt.client as paho
import ssl
import time
import json
import requests
import os
from robot.api.deco import keyword, not_keyword
import threading
import queue
import kafkaConsumerLib
import vehicleProtobuf
import OBGdiscoveryService
from pyaml_env import parse_config, BaseConfig

__version__ = "0.0.2"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
PUBLISHED_LOG = "published!!"
PUBLISHED_RESULT_LOG = "publish result: "
SUBSCRIBING_TO_TOPIC_LOG = "Subscribing to topic:: "
PATH_TO_OBG_SUBSCRIBED_DATA_PAYLOAD = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-decoded-payload.json"
PATH_TO_OBG_SUBSCRIBED_DATA_FOR_VALIDATION = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-for-validation.json"
VEHICLE_URI = "vehicle/"
SOFTWARE_VERSION_KEY = "VA_APP_SOFTWARE_VERSION"


def setup_globals(calledAsBinaryFile):
    # binary doesn't like relative paths and have different paths to the robot tests
    global url, systemVarFile, systemVars, keycloak_url, broker, port, apigateway_url
    if calledAsBinaryFile:
        systemVarFile = os.path.abspath(os.path.join(os.path.dirname(__file__), "variables/system.yml"))
    else:
        systemVarFile = "../variables/system.yml"

    systemVars = BaseConfig(parse_config(systemVarFile))
    keycloak_url = getattr(systemVars, "keycloak_url")
    broker = getattr(systemVars, "hive_broker")
    port = getattr(systemVars, "hive_port")


@not_keyword
def get_token(brokerUser, brokerClientSecret, calledAsBinaryFile=False):
    payload = "client_id=" + brokerUser + "&client_secret=" + brokerClientSecret + "&grant_type=" + "client_credentials"
    kcHeader = {"Content-Type": "application/x-www-form-urlencoded"}
    response = requests.request("POST", keycloak_url, headers=kcHeader, data=payload)
    print("Keycloak response code Hive token:: ", response.status_code)
    count = 0
    while response.status_code != 200:
        count = count + 1
        response = requests.request("POST", keycloak_url, headers=kcHeader, data=payload)
        print("Keycloak response code Hive token:: ", response.status_code)
        if count > 3:
            print("Failed to get keycloak token code:", response.status_code)
            break

    y = json.loads(response.text)
    # print(y['access_token'])
    return y["access_token"]


@not_keyword
def on_connect(client, userdata, flags, rc):
    print(rc)
    if rc == 0:
        print("Connected to MQTT Broker!")
        client.connected_flag = True
        global SubsData
        global retain
        global SubsDataList
        SubsData = -1
        retain = -1
        SubsDataList = []
    else:
        print("Failed to connect, return code %d\n", rc)


@not_keyword
def on_message(client, userdata, msg):
    print(f"Received '{msg.payload}' from '{msg.topic}' topic qos '{msg.qos}' retained '{msg.retain}'")
    if msg.retain == 1:
        print("This is a retained message")
    global SubsData
    global retain
    SubsData = msg.payload
    retain = msg.retain


@not_keyword
def on_message_write_to_file(client, userdata, msg):
    print(f"Received '{msg.payload}' from '{msg.topic}' topic qos '{msg.qos}' retained '{msg.retain}'")
    now = int(time.time() * 1000)
    with open("./" + str(now), "wb") as fd:
        fd.write(msg.payload)


@not_keyword
def on_message_latency(client, userdata, msg):
    print(f"Received '{msg.payload}' from '{msg.topic}' topic qos '{msg.qos}' retained '{msg.retain}'")
    now = int(time.time() * 1000)
    vaJaon = vehicleProtobuf.deserialise(msg.payload)
    jsonObj = json.loads(vaJaon)
    eventTimestamp = jsonObj["eventTimestampMs"]
    latency = now - int(eventTimestamp)
    print("Latency va to hive:: ", latency, "ms")
    global SubsDataList
    SubsDataList.append(latency)


@not_keyword
def on_publish(client, userdata, mid):
    print("data published: " + str(mid))
    pass


@not_keyword
def on_disconnect(client, userdata, rc):
    print("disconnecting reason  " + str(rc))
    client.connected_flag = False
    if rc != 0:
        print("Unexpected disconnection.")
    else:
        print("Disconnected")


@not_keyword
def get_timestamp():
    return str(int(time.time() * 1000))


@not_keyword
def connect_to_broker(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    certfile,
    ca_certs,
    keyfile,
    auth=True,
    tls=True,
    client_id_suffix="-e2eTest-" + get_timestamp(),
    calledAsBinaryFile=False,
    frToken="",
    frAuth=False,
):
    setup_globals(calledAsBinaryFile)

    if auth is True:
        if frAuth is True:
            token = frToken
        else:
            token = get_token(brokerUser, brokerClientSecret)
    else:
        token = "abcdefg"
    if frAuth is True:
        client = paho.Client(client_id=uniqueId, clean_session=True)
    else:
        client = paho.Client(client_id=uniqueId + client_id_suffix, clean_session=True)
    client.on_disconnect = on_disconnect
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_publish = on_publish
    passphrase = os.environ.get("hive_cert_passphrase")

    if tls is True:
        client.tls_set(
            certfile=certfile,
            ca_certs=ca_certs,
            keyfile=keyfile,
            cert_reqs=ssl.CERT_REQUIRED,
            tls_version=ssl.PROTOCOL_TLSv1_2,
            ciphers=None,
            keyfile_password=passphrase,
        )
        client.tls_insecure_set(False)  # for aws

    client.username_pw_set(uniqueId, password=token)
    if (auth is False) or (tls is False):
        client.connected_flag = True
    else:
        client.connected_flag = False
    client.loop_start()
    client.connect(broker, port, 10)
    while not client.connected_flag:  # wait for connection
        time.sleep(0.1)
    client.loop_stop()
    print("connected to broker")
    return client


@not_keyword
def get_protobuf_list(protobuf):
    print(protobuf)
    print(type(protobuf))
    if type(protobuf) is not list:
        print("not list, create list")
        protobufList = [protobuf]
        print(type(protobufList))
    else:
        print("Is list")
        protobufList = protobuf
        print(type(protobufList))

    print(type(protobufList[0]))
    for x in protobufList:
        if type(x) is not bytes:
            print("Payload is not Protobuf!!!! Exit!\n", str(x))
            return -1
    return protobufList


@keyword("send va error")
def send_va_error(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    certfile,
    ca_certs,
    keyfile,
    timestamp,
    errorMetadata,
):
    if timestamp == "":
        print(timestamp)
        print("No time stamp supplied")
        return

    if errorMetadata == "":
        print(errorMetadata)
        print("No error meta data supllied")
        return

    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)

    if ("useOBG" in os.environ) and (os.environ["useOBG"] == "True"):
        topic_error = VEHICLE_URI + uniqueId + "/va/error/errorMsg"
    else:
        topic_error = "error/" + uniqueId + "/errorMsg"
    print(topic_error)

    error_data = '{"faultIdentifier": "0x12", "timestamp": "' + str(timestamp) + '", "severity": "ERROR", "errorMetadata": "' + errorMetadata + '"}'
    print(error_data)

    client.loop_start()
    client.publish(topic_error, payload=error_data, qos=1, retain=False)
    time.sleep(1)
    print(PUBLISHED_LOG)
    client.disconnect()
    client.loop_stop()
    return error_data


@keyword("Send VA manifest to Broker")
def send_va_manifest_to_broker(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    version,
    certfile,
    ca_certs,
    keyfile,
    justQuery=False,
    vaAppSwVersion="0.0.80",
):
    """
    Connects to VCDP test broker and sends a VA manifest with versions to topic:\n
    sts/vcdp-auto-test/VA

    Requires certs:\n
    ./mqtt-client-cert.pem\n
    ./server.pem\n
    ./mqtt-client-key-unecrypted-dev.pem

    Examples:
    | Send Payload To Broker | 1.2.3 |
    """

    if version == "":
        print(version)
        print("invalid va status requested!!")
        return
    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)

    if ("useOBG" in os.environ) and (os.environ["useOBG"] == "True"):
        topic_manifest = VEHICLE_URI + uniqueId + "/va/sts/manifest"
    else:
        topic_manifest = "sts/" + uniqueId + "/manifest"

    print(topic_manifest)
    if version == "null":
        va_data = (
            '{"'
            + SOFTWARE_VERSION_KEY
            + '": "'
            + str(vaAppSwVersion)
            + '", \
            "SIGNAL_DICTIONARY": null, \
            "SIGNAL_DICTIONARY_SCHEMA": "2.1.55", \
            "DIAGNOSTICS_DICTIONARY": null, \
            "DIAGNOSTICS_DICTIONARY_SCHEMA": "2.1.0", \
            "QUERIES": null, \
            "QUERIES_SCHEMA": "3.0.0", \
            "PROTOCOL_CHANNELS": null, \
            "PROTOCOL_CHANNELS_SCHEMA": "2.1.0"}'
        )

    elif justQuery is True:
        print("only query")
        va_data = (
            '{"'
            + SOFTWARE_VERSION_KEY
            + '": "'
            + str(vaAppSwVersion)
            + '","SIGNAL_DICTIONARY": "'
            + "4.1.1"
            + '","SIGNAL_DICTIONARY_SCHEMA": "2.1.55","DIAGNOSTICS_DICTIONARY": "'
            + "1.0.1"
            + '","DIAGNOSTICS_DICTIONARY_SCHEMA": "2.1.0","QUERIES": "'
            + str(version)
            + '","QUERIES_SCHEMA": "3.0.0","PROTOCOL_CHANNELS": "'
            + "2.1.1"
            + '","PROTOCOL_CHANNELS_SCHEMA": "2.1.0"}'
        )

    else:
        va_data = (
            '{"'
            + SOFTWARE_VERSION_KEY
            + '": "'
            + str(vaAppSwVersion)
            + '","SIGNAL_DICTIONARY": "'
            + str(version)
            + '","SIGNAL_DICTIONARY_SCHEMA": "2.1.55","DIAGNOSTICS_DICTIONARY": "'
            + str(version)
            + '","DIAGNOSTICS_DICTIONARY_SCHEMA": "2.1.0","QUERIES": "'
            + str(version)
            + '","QUERIES_SCHEMA": "3.0.0","PROTOCOL_CHANNELS": "'
            + str(version)
            + '","PROTOCOL_CHANNELS_SCHEMA": "2.1.0"}'
        )

    print(va_data)
    client.loop_start()
    result = client.publish(topic_manifest, payload=va_data, qos=1, retain=False)
    print(PUBLISHED_RESULT_LOG + str(result))

    time.sleep(1)
    print(PUBLISHED_LOG)
    client.disconnect()
    client.loop_stop()


@keyword("Send VA Status to Broker")
def send_va_status_to_broker(uniqueId, brokerUser, brokerClientSecret, vastatus, certfile, ca_certs, keyfile, missingStatus=False):
    """
    Connects to VCDP test broker and sends a VA status to topic:\n
    sts/vcdp-auto-test/VA

    vastatus is a string "false" or "true"
    """
    vastatus = vastatus.strip().lower()
    if vastatus not in ["false", "true"]:
        print(vastatus)
        print("invalid va status requested!!")
        return
    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)

    if ("useOBG" in os.environ) and (os.environ["useOBG"] == "True"):
        topic_va = VEHICLE_URI + uniqueId + "/va/sts/va"
        topic_obg = VEHICLE_URI + uniqueId + "/obg/sts/manifest"
    else:
        topic_va = "sts/" + uniqueId + "/VA"

    print(topic_va)

    if missingStatus is True:
        va_data = '[{"id": 1, "result": ' + vastatus + '},{"id": 2, "result": true},{"id": 3, "result": false}]'
    else:
        va_data = '[{"id": 1, "result": ' + vastatus + '},{"id": 2, "result": true},{"id": 3, "result": false},{"id": 4, "result": false}]'
    print(va_data)
    client.loop_start()
    result = client.publish(topic_va, payload=va_data, qos=1, retain=False)
    print(PUBLISHED_RESULT_LOG + str(result))

    if ("useOBG" in os.environ) and (os.environ["useOBG"] == "True"):
        obg_data = '{"ipAddress": "**************", "powerMode": 0, "connectionMedium": 1, "region": "UK"}'
        result = client.publish(topic_obg, payload=obg_data, qos=1, retain=False)
        print(PUBLISHED_RESULT_LOG + str(result))

    time.sleep(1)
    print(PUBLISHED_LOG)
    client.disconnect()
    client.loop_stop()


@keyword("Send Payload To Broker")
def send_payload_to_broker(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    payloadFile,
    certfile,
    ca_certs,
    keyfile,
    auth=True,
    tls=True,
    numberOfMessages=1,
):
    """
    Connects to VCDP test broker and send a payload to topic:\n
    dt/vcdp-auto-test/stream/eng

    Requires certs:\n
    ./mqtt-client-cert.pem\n
    ./server.pem\n
    ./mqtt-client-key-unecrypted-dev.pem

    Examples:
    | Send Payload To Broker |  |

    """
    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, auth, tls)

    topic = "dt/" + uniqueId + "/stream/diag"

    with open(payloadFile) as f:
        x = f.readline().strip().strip("[").strip("]").split(",")

    byteList = b""

    for n in x:
        asByte = (int(n)).to_bytes(1, "big", signed=True)
        byteList = byteList + asByte

    # print(byteList.hex())
    client.loop_start()
    for n in range(numberOfMessages):
        result = client.publish(topic, payload=byteList, qos=0, retain=False)
        print(PUBLISHED_RESULT_LOG + str(result))
        time.sleep(1)

    client.disconnect()
    client.loop_stop()


@keyword("Send Payload To Broker as serialised protobuf")
def send_payload_to_broker_serialised_protobuf(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    protobuf,
    certfile,
    ca_certs,
    keyfile,
    topicOw="",
    auth=True,
    tls=True,
    numberOfMessages=1,
    calledAsBinaryFile=False,
):
    """
    Connects to VCDP test broker and send a payload to topic:\n
    dt/vcdp-auto-test/stream/eng

    Requires certs:\n
    ./mqtt-client-cert.pem\n
    ./server.pem\n
    ./mqtt-client-key-unecrypted-dev.pem

    Examples:
    | Send Payload To Broker as serialised protobuf|  |

    """

    print(protobuf)
    print(type(protobuf))
    protobufList = get_protobuf_list(protobuf)
    client = connect_to_broker(
        uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, auth, tls, calledAsBinaryFile=calledAsBinaryFile
    )

    if topicOw == "":
        topic = "dt/" + uniqueId + "/stream/eng"
    else:
        topic = "vehicle/" + uniqueId + "/" + topicOw

    print("Hive Topic::", topic)

    client.loop_start()
    for n in range(numberOfMessages):
        for x in protobufList:
            result = client.publish(topic, payload=x, qos=0, retain=False)
            print(PUBLISHED_RESULT_LOG + str(result))
        time.sleep(1)

    client.disconnect()
    client.loop_stop()
    return 1


@keyword("Send data to hive whilst checking kafka")
def send_data_to_whilst_checking_kafka(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    vin,
    topic,
    timeout_s,
    payload,
    certfile,
    ca_certs,
    keyfile,
    auth=True,
    tls=True,
):
    """ """
    # start kafka consumer on a thread
    q = queue.Queue()
    thread_function = kafkaConsumerLib.subscribe_to_topic_as_thread
    x = threading.Thread(
        target=thread_function,
        args=(uniqueId, topic, timeout_s, q),
        daemon=True,
    )
    x.start()
    x.join(10)
    # then send data to hive
    send_payload_to_broker(
        uniqueId,
        brokerUser,
        brokerClientSecret,
        payload,
        certfile,
        ca_certs,
        keyfile,
        auth,
        tls,
        numberOfMessages=2,
    )
    x.join(0)
    result = q.get()
    print("thread result:: " + str(result))
    return result


@keyword("Subscribe to hive topic")
def subscribe_to_topic(
    uniqueId,
    topic,
    brokerUser,
    brokerClientSecret,
    certfile,
    ca_certs,
    keyfile,
    timeToSubs=0.5,
    writeToFile=False,
):
    """
    Subcribes to a topic and returns the payload
    """

    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)
    if writeToFile is False:
        client.on_message = on_message
    else:
        client.on_message = on_message_write_to_file
    client.loop_start()  # start the loop
    print(SUBSCRIBING_TO_TOPIC_LOG + topic)
    client.subscribe(topic, qos=0)
    # time.sleep(timeToSubs)  # wait
    timeout = 0
    print("Waiting for data......")
    while type(SubsData) is int:
        # print("In subs: " ,timeout)
        if timeout > timeToSubs:
            print("timmed out")
            break
        time.sleep(1)
        timeout = timeout + 1

    client.loop_stop()  # stop the loop
    client.disconnect()
    return (SubsData, retain)


@keyword("Subscribe to Topic for Latancy VA to Hive")
def latency_va_to_hive(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, OBG=False):
    """
    Subcribes to a topic and returns the payload
    """

    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)
    client.on_message = on_message_latency
    client.loop_start()  # start the loop
    if OBG:
        topic = VEHICLE_URI + uniqueId + "/va/data/stream/pre"  # OBG
    else:
        topic = "dt/" + uniqueId + "/stream/eng"  # OBA

    print(SUBSCRIBING_TO_TOPIC_LOG + topic)
    client.subscribe(topic, qos=0)
    time.sleep(int(timeToSubs))  # wait
    client.loop_stop()  # stop the loop
    client.disconnect()
    print(SubsDataList)
    return SubsDataList


@keyword("Clear Retained messages from topic")
def clear_retained_message(uniqueId, topic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile):
    """
    Connects to VCDP test broker and send am empty payload to topic:\n
    To remove all retained messages


    """

    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)

    client.loop_start()
    client.publish(topic, payload="", qos=0, retain=True)
    time.sleep(1)
    print(PUBLISHED_LOG)
    client.disconnect()
    client.loop_stop()


@keyword("Clear All Retained messages from ctrl topics")
def clear_retained_ctrl_message(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile):
    """
    Connects to VCDP test broker and send am empty payload to all ctrl topics:\n
    To remove all retained messages


    """

    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)

    client.loop_start()
    if ("useOBG" in os.environ) and (os.environ["useOBG"] == "True"):
        topic = "vcdp/" + uniqueId + "/va/ctrl/"
        ruleSet = "ruleset"
    else:
        topic = "ctrl/" + uniqueId + "/"
        ruleSet = "rulesetVersion"

    print(topic)
    client.publish(topic + "ch", payload="", qos=0, retain=True)
    client.publish(topic + "sigDict", payload="", qos=0, retain=True)
    client.publish(topic + ruleSet, payload="", qos=0, retain=True)
    client.publish(topic + "diagDict", payload="", qos=0, retain=True)
    client.publish(topic + "query", payload="", qos=0, retain=True)
    time.sleep(1)
    print(PUBLISHED_LOG)
    client.disconnect()
    client.loop_stop()


@not_keyword
def hive_pub_sub(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    certfile,
    ca_certs,
    keyfile,
    writeToFile,
    SubscribeTopic,
    PublishTopic,
    payloadFile,
    timeToSubs,
    subDataFile,
    has_file_transform=False,
):
    try:
        client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, True, True, "")
        if writeToFile is False:
            client.on_message = on_message
        else:
            client.on_message = on_message_write_to_file
        client.loop_start()  # start the loop
        print(SUBSCRIBING_TO_TOPIC_LOG + SubscribeTopic)
        client.subscribe(SubscribeTopic, qos=1)
        client.publish(PublishTopic, payload=payloadFile, qos=1, retain=False)
        time.sleep(timeToSubs)  # wait
        client.loop_stop()  # stop the loop
        client.disconnect()
        file = open(subDataFile, "w")
        responsestr = str(SubsData)
        res = responsestr.strip("b'")
        file.write(res)
        file.close()
        if has_file_transform:
            OBGdiscoveryService.file_transform(subDataFile)
    except Exception:
        print("Hive connection interrupted or an issue occured")
    return (SubsData, retain)


"""
Below method has feature to retry hive mq connection incase of an error or exception.
It will retry defined number (max_retry) of times.
"""


@not_keyword
def hive_connection_with_retries(
    uniqueId,
    brokerUser,
    brokerClientSecret,
    certfile,
    ca_certs,
    keyfile,
    writeToFile,
    SubscribeTopic,
    PublishTopic,
    payloadFile,
    timeToSubs,
    subDataFile,
    has_file_transform,
    max_retry=3,
):
    for _ in range(max_retry):
        SubsData, retain = hive_pub_sub(
            uniqueId,
            brokerUser,
            brokerClientSecret,
            certfile,
            ca_certs,
            keyfile,
            writeToFile,
            SubscribeTopic,
            PublishTopic,
            payloadFile,
            timeToSubs,
            subDataFile,
            has_file_transform,
        )
        if SubsData != -1:
            break
        else:
            time.sleep(10)
            print("Hive pub Sub failed, attempting retry")
    return (SubsData, retain)


@not_keyword
def send_serialised_protobuf_to_broker_fr_auth(
    protobuf, frToken, mqtt_client_id, certfile, ca_certs, keyfile, numberOfMessages=1, calledAsBinaryFile=False
):
    protobufList = get_protobuf_list(protobuf)
    client = connect_to_broker(
        uniqueId=mqtt_client_id,
        certfile=certfile,
        ca_certs=ca_certs,
        keyfile=keyfile,
        auth=True,
        tls=True,
        frAuth=True,
        frToken=frToken,
        brokerUser="",
        brokerClientSecret="",
        calledAsBinaryFile=calledAsBinaryFile,
    )

    topic = VEHICLE_URI + mqtt_client_id + "/va/data/stream/pre"

    client.loop_start()
    for n in range(numberOfMessages):
        for x in protobufList:
            result = client.publish(topic, payload=x, qos=1, retain=False)
            print(PUBLISHED_RESULT_LOG + str(result))
        time.sleep(1)

    client.disconnect()
    client.loop_stop()
    return 1


@keyword("Vehicle sends VA OBG discovery to broker")
def send_va_obg_discovery(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile):
    """ """
    client = connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile)

    topic = VEHICLE_URI + uniqueId + "/obg/data/discovery"
    print(topic)

    obg_disco_data = '{"application":"VA","channel_ids":["Ch1","Ch5","Ch7"]}'
    print(obg_disco_data)
    client.loop_start()
    result = client.publish(topic, payload=obg_disco_data, qos=1, retain=False)
    print(PUBLISHED_RESULT_LOG + str(result))

    time.sleep(1)
    print(PUBLISHED_LOG)
    client.disconnect()
    client.loop_stop()
