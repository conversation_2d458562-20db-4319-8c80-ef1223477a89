# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: DepartureSchedulesProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'DepartureSchedulesProtobufMessage.proto\x12\x17jlr.protobuf.departures\x1a\x10\x45numStatus.proto\"\xaa\x02\n\x1c\x44\x65partureTaskPreconditioning\x12,\n\x1fprecondition_target_temperature\x18\x01 \x01(\x02H\x00\x88\x01\x01\x12H\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32-.jlr.protobuf.departures.SeatClimateZoneState\x12T\n\x15hsw_temperature_level\x18\x03 \x01(\x0e\x32\x30.jlr.protobuf.departures.EnumHSWTemperatureLevelH\x01\x88\x01\x01\x42\"\n _precondition_target_temperatureB\x18\n\x16_hsw_temperature_level\"\x19\n\x17\x44\x65partureTaskCabinClean\"\xb3\x01\n\rDepartureTask\x12P\n\x0fpreconditioning\x18\x01 \x01(\x0b\x32\x35.jlr.protobuf.departures.DepartureTaskPreconditioningH\x00\x12G\n\x0b\x63\x61\x62in_clean\x18\x02 \x01(\x0b\x32\x30.jlr.protobuf.departures.DepartureTaskCabinCleanH\x00\x42\x07\n\x05value\"\x81\x01\n\x0e\x44\x65partureEvent\x12\x1b\n\x0e\x64\x65parture_time\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12?\n\x0f\x64\x65parture_tasks\x18\x02 \x03(\x0b\x32&.jlr.protobuf.departures.DepartureTaskB\x11\n\x0f_departure_time\"\x85\x02\n\x14WakeUpRequestDetails\x12\x43\n\x0f\x65vent_time_type\x18\x01 \x01(\x0e\x32*.jlr.protobuf.departures.EnumEventTimeType\x12\x17\n\nevent_time\x18\x02 \x01(\x04H\x00\x88\x01\x01\x12#\n\x16\x65vent_duration_seconds\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12@\n\rwakeup_reason\x18\x04 \x01(\x0e\x32).jlr.protobuf.departures.EnumWakeUpReasonB\r\n\x0b_event_timeB\x19\n\x17_event_duration_seconds\"\x98\x02\n\x15WakeUpResponseDetails\x12/\n\x06result\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12\x1b\n\x0estart_time_utc\x18\x02 \x01(\x04H\x00\x88\x01\x01\x12!\n\x14start_time_localtime\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x19\n\x0c\x65nd_time_utc\x18\x04 \x01(\x04H\x02\x88\x01\x01\x12\x1f\n\x12\x65nd_time_localtime\x18\x05 \x01(\x04H\x03\x88\x01\x01\x42\x11\n\x0f_start_time_utcB\x17\n\x15_start_time_localtimeB\x0f\n\r_end_time_utcB\x15\n\x13_end_time_localtime\"`\n\x1bSetDepartureScheduleRequest\x12\x41\n\x10\x64\x65parture_events\x18\x01 \x03(\x0b\x32\'.jlr.protobuf.departures.DepartureEvent\"O\n\x1cSetDepartureScheduleResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\"^\n\x15SetWakeUpTimesRequest\x12\x45\n\x0ewakeup_details\x18\x01 \x03(\x0b\x32-.jlr.protobuf.departures.WakeUpRequestDetails\"\x96\x01\n\x16SetWakeUpTimesResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12K\n\x13wakeup_resp_details\x18\x02 \x03(\x0b\x32..jlr.protobuf.departures.WakeUpResponseDetails\"!\n\x1f\x43learChargingWakeUpTimesRequest\"S\n ClearChargingWakeUpTimesResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\"\x1e\n\x1cGetNextDepartureEventRequest\"\x92\x01\n\x1dGetNextDepartureEventResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12@\n\x0f\x64\x65parture_event\x18\x02 \x01(\x0b\x32\'.jlr.protobuf.departures.DepartureEvent\"\xed\x01\n\x14SeatClimateOperation\x12?\n\tseat_area\x18\x01 \x01(\x0e\x32,.jlr.protobuf.departures.EnumSeatClimateArea\x12\x41\n\nseat_state\x18\x02 \x01(\x0e\x32-.jlr.protobuf.departures.EnumSeatClimateState\x12Q\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32\x31.jlr.protobuf.departures.EnumSeatClimateIntensity\"\x94\x02\n\x14SeatClimateZoneState\x12\x42\n\x0eseat_selection\x18\x01 \x01(\x0e\x32*.jlr.protobuf.departures.EnumSeatSelection\x12\x45\n\x0eseat_operation\x18\x02 \x03(\x0b\x32-.jlr.protobuf.departures.SeatClimateOperation\x12V\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32\x31.jlr.protobuf.departures.EnumSeatClimateIntensityH\x00\x88\x01\x01\x42\x19\n\x17_seat_climate_intensity*w\n\x11\x45numEventTimeType\x12$\n ENUM_EVENT_TIME_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_EVENT_TIME_TYPE_BEGIN\x10\x01\x12\x1c\n\x18\x45NUM_EVENT_TIME_TYPE_END\x10\x02*\xb8\x04\n\x18\x45numSeatClimateIntensity\x12+\n\'ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_SEAT_CLIMATE_INTENSITY_OFF\x10\x01\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1\x10\x02\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1\x10\x03\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2\x10\x04\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2\x10\x05\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3\x10\x06\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3\x10\x07\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4\x10\x08\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4\x10\t\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5\x10\n\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5\x10\x0b*\xa3\x01\n\x13\x45numSeatClimateArea\x12&\n\"ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_AREA_ALL\x10\x01\x12\"\n\x1e\x45NUM_SEAT_CLIMATE_AREA_CUSHION\x10\x02\x12 \n\x1c\x45NUM_SEAT_CLIMATE_AREA_SQUAB\x10\x03*\xa5\x01\n\x14\x45numSeatClimateState\x12\'\n#ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_STATE_ON\x10\x01\x12\x1f\n\x1b\x45NUM_SEAT_CLIMATE_STATE_OFF\x10\x02\x12#\n\x1f\x45NUM_SEAT_CLIMATE_STATE_INHIBIT\x10\x03*\xad\x01\n\x17\x45numHSWTemperatureLevel\x12*\n&ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED\x10\x00\x12\"\n\x1e\x45NUM_HSW_TEMPERATURE_LEVEL_OFF\x10\x01\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_1\x10\x02\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_2\x10\x03*\xb1\x01\n\x10\x45numWakeUpReason\x12#\n\x1f\x45NUM_WAKE_UP_REASON_UNSPECIFIED\x10\x00\x12%\n!ENUM_WAKE_UP_REASON_INLET_HEATING\x10\x01\x12 \n\x1c\x45NUM_WAKE_UP_REASON_CHARGING\x10\x02\x12/\n+ENUM_WAKE_UP_REASON_BATTERY_PRECONDITIONING\x10\x03*\xe1\x04\n\x11\x45numSeatSelection\x12#\n\x1f\x45NUM_SEAT_SELECTION_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x45NUM_SEAT_SELECTION_ALL\x10\x01\x12&\n\"ENUM_SEAT_SELECTION_FIRST_ROW_LEFT\x10\x02\x12\'\n#ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT\x10\x03\x12(\n$ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE\x10\x04\x12%\n!ENUM_SEAT_SELECTION_FIRST_ROW_ALL\x10\x05\x12\'\n#ENUM_SEAT_SELECTION_SECOND_ROW_LEFT\x10\x06\x12(\n$ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT\x10\x07\x12)\n%ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE\x10\x08\x12&\n\"ENUM_SEAT_SELECTION_SECOND_ROW_ALL\x10\t\x12&\n\"ENUM_SEAT_SELECTION_THIRD_ROW_LEFT\x10\n\x12\'\n#ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT\x10\x0b\x12(\n$ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE\x10\x0c\x12%\n!ENUM_SEAT_SELECTION_THIRD_ROW_ALL\x10\r\x12 \n\x1c\x45NUM_SEAT_SELECTION_REAR_ALL\x10\x0e\x42\x43\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.departuresP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'DepartureSchedulesProtobufMessage_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.departuresP\001'
  _globals['_ENUMEVENTTIMETYPE']._serialized_start=2523
  _globals['_ENUMEVENTTIMETYPE']._serialized_end=2642
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_start=2645
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_end=3213
  _globals['_ENUMSEATCLIMATEAREA']._serialized_start=3216
  _globals['_ENUMSEATCLIMATEAREA']._serialized_end=3379
  _globals['_ENUMSEATCLIMATESTATE']._serialized_start=3382
  _globals['_ENUMSEATCLIMATESTATE']._serialized_end=3547
  _globals['_ENUMHSWTEMPERATURELEVEL']._serialized_start=3550
  _globals['_ENUMHSWTEMPERATURELEVEL']._serialized_end=3723
  _globals['_ENUMWAKEUPREASON']._serialized_start=3726
  _globals['_ENUMWAKEUPREASON']._serialized_end=3903
  _globals['_ENUMSEATSELECTION']._serialized_start=3906
  _globals['_ENUMSEATSELECTION']._serialized_end=4515
  _globals['_DEPARTURETASKPRECONDITIONING']._serialized_start=87
  _globals['_DEPARTURETASKPRECONDITIONING']._serialized_end=385
  _globals['_DEPARTURETASKCABINCLEAN']._serialized_start=387
  _globals['_DEPARTURETASKCABINCLEAN']._serialized_end=412
  _globals['_DEPARTURETASK']._serialized_start=415
  _globals['_DEPARTURETASK']._serialized_end=594
  _globals['_DEPARTUREEVENT']._serialized_start=597
  _globals['_DEPARTUREEVENT']._serialized_end=726
  _globals['_WAKEUPREQUESTDETAILS']._serialized_start=729
  _globals['_WAKEUPREQUESTDETAILS']._serialized_end=990
  _globals['_WAKEUPRESPONSEDETAILS']._serialized_start=993
  _globals['_WAKEUPRESPONSEDETAILS']._serialized_end=1273
  _globals['_SETDEPARTURESCHEDULEREQUEST']._serialized_start=1275
  _globals['_SETDEPARTURESCHEDULEREQUEST']._serialized_end=1371
  _globals['_SETDEPARTURESCHEDULERESPONSE']._serialized_start=1373
  _globals['_SETDEPARTURESCHEDULERESPONSE']._serialized_end=1452
  _globals['_SETWAKEUPTIMESREQUEST']._serialized_start=1454
  _globals['_SETWAKEUPTIMESREQUEST']._serialized_end=1548
  _globals['_SETWAKEUPTIMESRESPONSE']._serialized_start=1551
  _globals['_SETWAKEUPTIMESRESPONSE']._serialized_end=1701
  _globals['_CLEARCHARGINGWAKEUPTIMESREQUEST']._serialized_start=1703
  _globals['_CLEARCHARGINGWAKEUPTIMESREQUEST']._serialized_end=1736
  _globals['_CLEARCHARGINGWAKEUPTIMESRESPONSE']._serialized_start=1738
  _globals['_CLEARCHARGINGWAKEUPTIMESRESPONSE']._serialized_end=1821
  _globals['_GETNEXTDEPARTUREEVENTREQUEST']._serialized_start=1823
  _globals['_GETNEXTDEPARTUREEVENTREQUEST']._serialized_end=1853
  _globals['_GETNEXTDEPARTUREEVENTRESPONSE']._serialized_start=1856
  _globals['_GETNEXTDEPARTUREEVENTRESPONSE']._serialized_end=2002
  _globals['_SEATCLIMATEOPERATION']._serialized_start=2005
  _globals['_SEATCLIMATEOPERATION']._serialized_end=2242
  _globals['_SEATCLIMATEZONESTATE']._serialized_start=2245
  _globals['_SEATCLIMATEZONESTATE']._serialized_end=2521
# @@protoc_insertion_point(module_scope)
