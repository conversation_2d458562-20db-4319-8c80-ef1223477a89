#!/usr/bin python3

import requests
import json
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig

from commonUtils import get_keycloak_token
import uuid
import random
from datetime import datetime, timedelta, timezone

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
CONTENTTYPE = "application/json"
AUTHTYPE = "Bearer "
STATUS = "Status: "
TOKEN = "token:"
ARRAYOFVINS = "array of vins: "
EMPTYBODY = "No response body returned"
V2_VEHICLES_VIN_URI = "/v2/vehicles?identifierType=VIN"
V2_VEHICLES_STATUS_URI = "/applications/vehicleAnalytics/status"
V2_VEHICLES_URI = "/v2/vehicles/"
V2_MANIFEST_URI = "/applications/vehicleAnalytics/manifest"
UPDATE_VEHICLE_URI = "/vehicles/updateVehicle/"
VEHICLE_CREATE_URI = "/vehicles/create"
V2_VEHICLES_FLEET = "/v2/vehicles/assets/fleetId"
V2_VEHICLES_TAGS = "/v2/vehicles/tags"
VEHICLES = "/vehicles/"

systemVarFile = "../variables/system.yml"
json_response_file = "../json-request-data/vehicle-reponse-config/get-response-vehicle.json"

headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot"}

vin = "TESTVIN" + str(random.randint(1000000000, 9999999999))
DecimalValue = random.uniform(1, 100)
UUID = uuid.uuid4()
unique_id = str(UUID)

UUID2 = uuid.uuid4()
unique_id2 = str(UUID2)


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "dv-state-handler")


def get_var_from_file(var_file, var_name):
    """
    get a variable from a yml file
    """
    sys_vars = BaseConfig(parse_config(var_file))
    return getattr(sys_vars, var_name)


@not_keyword
def call_policy_check_endpoint(vin, user, pw, uri, data):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    vin_string = str(vin)
    print(type(vin_string))
    print(vin_string)

    vin_striped = vin_string.strip("['']")

    print(vin)
    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)
    json_request_body["identifiers"][0] = vin_striped

    payload = json.dumps(json_request_body)
    url = get_url()
    service_url = url + uri
    print(service_url)
    print("this is payload", payload)
    response = requests.request("POST", service_url, headers=header, data=payload)
    print(type(response.text))
    print("this is response :", response.text)
    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return EMPTYBODY, response.status_code


@keyword("get attribute from response body")
def get_attribute_from_response_body(response_body, attribute):
    """
    This function returns an attribute from the body response of
    a digital vehicle

    Examples:
    | get_attribute_from_response_body | Json-data | allowWifi |

    """
    return response_body[attribute]


@keyword("load json file")
def load_json_file(json_file_location):
    """
    This function loads a json (schema) file and returns a
    json object data

    Examples:
    | load_json_file | ...location/schema.json |"""
    with open(json_file_location, encoding="utf-8") as file:
        json_file = json.load(file)

        print("\n", "json file from: ", json_file_location, "  \n")
    return json_file


@keyword("vehicle status")
def vehicle_status(user, pw, vin, attribute, auth=True):
    """
    Get the vehice status from VCDP stauts monitor.\n
    \n
    Returns the value for the attribute key

    Examples:
    | vehicle status | SALEA6AN0L2000534 | queries | True |
    """
    url = get_url()
    token = get_keycloak_token(user, pw, auth)

    send = url + V2_VEHICLES_VIN_URI
    headers = {"Authorization": AUTHTYPE + token, "Content-Type": CONTENTTYPE}
    payload = {"identifiers": [vin]}

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "POST",
        send,
        headers=headers,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(response.status_code)
    y = json.loads(response.text)
    print(json.dumps(y))
    return y["data"][0][attribute]


@keyword("Get Vehicle Diagnostic Dictonary")
def get_vehicle_diag_dict(user, pw, vin):
    """
    Returns the vehicle specific diahgnostic dictonary


    Examples:

    """

    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    url = get_url()
    send = url + "/diagnostics/filteredConfig?vin=" + vin
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", send, headers=header)
    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get Vehicle Digital Vehicle")
def get_vehicle_dv(user, pw, vin):
    """
    Returns the vehicles Digital vehicle.

    Examples:

    """

    url = get_url()
    token = get_keycloak_token(user, pw)
    payload = {"identifiers": vin}
    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    send = url + V2_VEHICLES_VIN_URI
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Update vehicle pending polices")
def update_pending_policies(user, pw, vin, hash):
    token = get_keycloak_token(user, pw)
    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    url = get_url() + "/v2/vehicles/policies/dataProduct/pending?identifierType=VIN"

    payload = json.dumps({"identifier": vin, "pendingConfig": {"timestamp": "2024-02-14T17:01:30.320Z", "hash": hash, "configs": []}})

    response = requests.request("PUT", url, headers=headers, data=payload)
    print(response.text)
    return response.status_code


@keyword("delete vehicle by vin")
def delete_vehicle_by_vin(username, password, vin, auth=True):
    """
    Deletes a vehicles Digital vehicle by vin.

    Examples:
    | delete_vehicle_by_vin | username | password | vin |

    """

    token = get_keycloak_token(username, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + VEHICLES + vin
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "DELETE",
        send,
        headers=header,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    # print(response_body)
    return response.text, response.status_code


@keyword("get vehicles with active configurations")
def get_vehicle_active_configs(user, password, product_id, auth=True):
    """
    Get the vehice's active configs settings using data product IDs
    Returns VIN numbers of vehicles with active config senttings

    Examples:
    | get_vehicle_active_configs | username | password | Dp00_03 | True |
    """
    url = get_url()
    active_configurations = url + "/dv/state/activeConfigs" + "?dataProductId=" + product_id

    print("\n", "Endpoint: ", active_configurations)

    if auth is not True:
        token = "abcdef12345"
    else:
        token = get_keycloak_token(user, password)

    print("token")
    print(token)
    vs_headers = {
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "GET",
        active_configurations,
        headers=vs_headers,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(response.text, "*********************************", response.status_code, "\n\n")
    response_body = json.loads(response.text)
    print(STATUS, response.status_code)
    print(response.text, "\n\n")
    return response_body, response.status_code


@keyword("get lottery vin from active vehicle configs response body")
def get_vin(response_body, get_vin_from_active_configs=False):
    """
    this function returns a vin number to use for testing
    There is an option to pick a vin from list of vins
    with active configurations or to use the vin from
    the system.yml file

    Examples:
    | get_vin | json-data | True |

    """

    if get_vin_from_active_configs:
        number_of_vins = len(response_body["vins"])
        print("number of vin numbers :", number_of_vins)
        lottery_vin = random.randint(0, (number_of_vins - 1))
        print("random vin slot selected:", lottery_vin)
        return str(response_body["vins"][lottery_vin])
    return get_var_from_file(systemVarFile, "vin")


@keyword("get error message of vehicle by vin")
def get_vehicle_message_error(user, password, vin, auth=True):
    """
        This function returns the error mesasge of a vehicle by vin

    Examples:
    | get_vehicle_message_error| username | password | MRCTESTVIN001 | True |
    """
    url = get_url()
    error_meessages_url = url + "/messages/error/" + vin

    if auth is not True:
        token = "abcdef12345"
    else:
        token = get_keycloak_token(user, password)

    print("token")
    print(token)
    err_headers = {
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "GET",
        error_meessages_url,
        headers=err_headers,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    response_body = json.loads(response.text)

    print(error_meessages_url)
    print(STATUS, response.status_code)
    print(response_body, "\n\n")
    return response_body, response.status_code


@keyword("Get Vehicle Assets by VINs or IDs")
def get_assets_by_vins_or_ids(user, password, identifiers):
    """
    Returns the vehicles Assets in DV.

    Examples:
    | get_assets_by_vins_or_ids | username | password | [vins] or [IDs] |

    """

    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()

    if len(identifiers[0]) <= 17:
        send = url + "/v2/vehicles/assets?identifierType=VIN"
    else:
        send = url + "/v2/vehicles/assets?identifierType=UNIQUE_ID"

    payload = {"identifiers": identifiers}

    print(payload)

    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get vehicle inventory by VINs or IDs")
def get_inventory_by_vins_or_ids(user, password, identifiers):
    """
    Returns the vehicles inventory in DV.

    Examples:
    | get_inventory_by_vins_or_ids | username | password | [vins] or [identifiers] |

    """

    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()

    if len(identifiers[0]) <= 17:
        send = url + "/v2/vehicles/inventory?identifierType=VIN"
    else:
        send = url + "/v2/vehicles/inventory?identifierType=UNIQUE_ID"

    payload = {"identifiers": identifiers}

    print(payload)

    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get vehicle identity by VINs or IDs")
def get_identity_by_vins_or_ids(user, password, identifiers):
    """
    Returns the vehicles identity in DV.

    Examples:
    | get_identity_by_vins_or_ids | username | password | [vins] or [identifiers] |

    """

    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()

    if len(identifiers[0]) <= 17:
        send = url + "/v2/vehicles/identity?identifierType=VIN"
    else:
        send = url + "/v2/vehicles/identity?identifierType=UNIQUE_ID"

    payload = {"identifiers": identifiers}
    print(payload)

    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get vehicle policies by VINs or IDs")
def get_policies_by_vins_or_ids(user, password, identifiers):
    """
    Returns the vehicles policies in DV.

    Examples:
    | get_policies_by_vins_or_ids | username | password | [vins] or [identifiers] |

    """

    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()

    if len(identifiers[0]) <= 17:
        send = url + "/v2/vehicles/policies/dataProduct?identifierType=VIN"
    else:
        send = url + "/v2/vehicles/policies/dataProduct?identifierType=UNIQUE_ID"

    payload = {"identifiers": identifiers}
    print(payload)

    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Update Existing VA Status")
def update_va_status(user, pw, unique_id, vastatus):
    """
    Returns the vehicles Digital vehicle.

    Examples:

    """

    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + V2_VEHICLES_URI + unique_id + V2_VEHICLES_STATUS_URI
    print(send)

    payload = '[{"id": 1, "result": ' + vastatus + '},{"id": 2, "result": true},{"id": 3, "result": false}]'

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("PUT", send, headers=header, data=payload)
    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Update Existing Manifest")
def update_manifest(user, pw, unique_id, version, va_app_sw_version="0.0.80"):
    """
    Returns the vehicles Digital vehicle.

    Examples:

    """

    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + V2_VEHICLES_URI + unique_id + V2_MANIFEST_URI
    print(send)

    if version == "null":
        payload = json.dumps(
            {
                "VA_APP_SOFTWARE_VERSION": va_app_sw_version,
                "SIGNAL_DICTIONARY": None,
                "SIGNAL_DICTIONARY_SCHEMA": "2.1.0",
                "DIAGNOSTICS_DICTIONARY": None,
                "DIAGNOSTICS_DICTIONARY_SCHEMA": "2.1.0",
                "QUERIES": None,
                "QUERIES_SCHEMA": "3.1.0",
                "PROTOCOL_CHANNELS": None,
                "PROTOCOL_CHANNELS_SCHEMA": "2.1.7",
            }
        )
    else:
        payload = json.dumps(
            {
                "VA_APP_SOFTWARE_VERSION": va_app_sw_version,
                "SIGNAL_DICTIONARY": version,
                "SIGNAL_DICTIONARY_SCHEMA": "2.1.0",
                "DIAGNOSTICS_DICTIONARY": version,
                "DIAGNOSTICS_DICTIONARY_SCHEMA": "2.1.0",
                "QUERIES": version,
                "QUERIES_SCHEMA": "3.1.0",
                "PROTOCOL_CHANNELS": version,
                "PROTOCOL_CHANNELS_SCHEMA": "2.1.7",
            }
        )

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("PUT", send, headers=header, data=payload)
    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Update Vehicle")
def update_vehicle(user, pw, vin):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + UPDATE_VEHICLE_URI + vin
    print(send)

    payload = json.dumps(
        {
            "brand": "JAG",
            "modelRange": "X590",
            "targetMarket": "UKTest",
            "soldIntoMarket": "UK",
            "modelYear": "MY18",
            "modelName": "I Pace",
            "trim": "AWD",
            "bodyStyle": "5 DOOR STATION WAGON",
            "driver": "RHD",
            "transmission": "Automatic",
            "engine": "Battery Electric Vehicle (BEV)",
            "fuelType": ["BEV"],
        }
    )

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("PATCH", send, headers=header, data=payload)
    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Compare digital vehicle dictionary to diagnostics dictionary")
def dict_ok(digital_vehicle, diag_dict):
    # compare vehicle diag dict with digital vehilce
    """
    compares the digital vehicle against diagnostics dictionary for duplicates
    and returns true (if everything is okay) or false if not

    Examples:
    | dict_ok | json digital vehicle data | json diagnostics dictionary |
    """

    jdv = json.loads(digital_vehicle)
    print("\nDiag Dict version:: " + jdv["diagnosticsDictionary"])
    dv_ecus = []
    for x in jdv["modules"]:
        dv_ecus.append("0x" + x["nodeAddress"])
    print(dv_ecus)

    j_dd = json.loads(diag_dict)
    ecu_log_addrs = []
    for x in j_dd["dataDefinition"]:
        ecu_log_addrs.append(x["EcuLogicalAddress"])
    print(ecu_log_addrs)

    if ecu_log_addrs != dv_ecus:
        print(STATUS, "nok")
        return False

    print(STATUS, "Everything is Okay")
    return True


@keyword("Check for duplicates in diagnostics dictionary")
def check_for_duplictes_in_diag_dict(diag_dict):
    """
    function checks for duplicate entries in diagnostics dictionary
    and returns an array of duplicates if present or an empty array if not

    Examples:
    | check_for_duplicates_in diag_dict | json diagnostics dictionary |
    """
    j_dd = json.loads(diag_dict)
    data_ids = []
    # get all data id's
    for x in j_dd["dataDefinition"]:
        for y in x["data"]:
            data_ids.append(y["id"])
    print(data_ids)

    # check for dups
    # dataIds = ['D00227', 'D00228', 'D00233', 'D00230', 'D00231', 'D00232', 'D00233']
    dups = [x for x in data_ids if data_ids.count(x) >= 2]
    print("Duplicates: ", dups)
    if dups != []:
        print("Duplicate Data Ids found in diagnostic dictonary")
    return dups


@keyword("Update vehicle all vehicle modules")
def update_vehicle_modules(user, password, vin, payload):
    """
    The aim of the function is to update the modules of a vehicle

    Examples:
    | update_vehicle_modules | user | password | MCRTESTVIN001 | json payload |

    """
    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + "/vehicles/updateModules/" + vin
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "PUT",
        send,
        headers=header,
        data=payload,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print("Status while updating vehicle modules: ", response.status_code)
    print(payload)
    return response.status_code


@keyword("update vehicle attributes")
def update_vehicle_attributes(user, password, vin, payload):
    """
    The aim of the function is to update the modules of a vehicle

    Examples:
    | update_vehicle_modules | user | password | MCRTESTVIN001 | json payload |

    """
    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + UPDATE_VEHICLE_URI + vin
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "PATCH",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print("Status while updating vehicle modules: ", response.status_code)
    print(payload)
    return response.status_code


@keyword("Change VA controls of vehicle in dv")
def change_va_controls_of_vehicle(username, password, va_id, value, unique_id, auth=True):
    """
    The aim of the function is to change the VA controls of Vehicle

    Examples:
    | change_vehicleinsync_attribute | username | password | 1 | True | True  |

    """
    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + V2_VEHICLES_URI + unique_id + V2_VEHICLES_STATUS_URI

    payload = json.dumps([{"id": va_id, "result": value}])

    print("\n", send)
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "PUT",
        send,
        headers=header,
        data=payload,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    return response.status_code


@keyword("Get unique ids of list of vins")
def get_unique_ids_by_vin(username, password, array_of_vins, auth=True):
    """
    The aim of the function is to update the enable sota attribute of a vehicle

    Examples:
    | change_enablesota_status | username | password | json.dumps(['MCRTESTVIN001']) | True |
    """

    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + "/dv/state/"
    print("\n", send)
    print(ARRAYOFVINS, array_of_vins)
    response = requests.request(
        "POST",
        send,
        headers=header,
        data=array_of_vins,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get vehicle applications by VINs or IDs")
def get_applications_by_vins_or_ids(user, password, list_of_vins_or_ids):
    """
    Returns the vehicles inventory in DV.

    Examples:
    | get_applications_by_vins_or_ids | username | password | [vins] or [identifiers] |

    """

    token = get_keycloak_token(user, password)
    print("token")
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()

    if len(list_of_vins_or_ids[0]) <= 17:
        send = url + "/v2/vehicles/applications?identifierType=VIN"
    else:
        send = url + "/v2/vehicles/applications?identifierType=UNIQUE_ID"

    payload = {"identifiers": list_of_vins_or_ids}

    print(payload)

    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("get vehicle count")
def get_vehicle_count(
    username,
    password,
    dv_data="../jsonschema/vehicle-count-search.json",
    auth=True,
):
    """
    The aim of the function is get a vehicle count by passing a json search criteria

    Examples:
    | get vehicle count | username | password |  get-vehicle-count-search.json  |True |
    """
    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    new_digital_vehicle = load_json_file(dv_data)
    url = get_url()
    send = url + "/v2/vehicles/count"
    print("\n", send)
    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(new_digital_vehicle),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("get vehicle by vin or unique id")
def get_vehicle_by_vin_or_unique_id(
    username,
    password,
    vins_or_ids: [str],
    auth=True,
):
    """
    The aim of the function is to get the dv of a vehicle by vin or unique id

    Examples:
    | get vehicle by vin or unique id | username | password |  [vin] or [id]  |True |
    """
    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    url = get_url()

    if len(vins_or_ids[0]) <= 17:
        send = url + V2_VEHICLES_VIN_URI
    else:
        send = url + "/v2/vehicles?identifierType=UNIQUE_ID"

    print("vin/uuid:", vins_or_ids)
    vins_or_ids_str = str(vins_or_ids)
    vins_or_ids_updated = vins_or_ids_str.strip("['']")
    payload = {"identifiers": [vins_or_ids_updated], "inclusions": ["IDENTITY", "POLICIES", "APPLICATIONS", "ASSETS", "INVENTORY"]}

    print("\n", send)
    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get VA of list of vins")
def get_va_controlls_by_vin(username, password, array_of_vins, auth=True):
    """
    The aim of the function is get the VA controls for a list of VINS

    Examples:
    | get_va_controlls_by_vin | username | password | json.dumps(['MCRTESTVIN001']) | True |
    """

    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + "/v2/vehicles/policies/vaControls?identifierType=VIN"

    print("\n", send)
    print(ARRAYOFVINS, array_of_vins)
    payload = {"identifiers": [array_of_vins]}

    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Get query state objects for list of vins")
def get_query_state_objects_for_vins(username, password, array_of_vins, auth=True):
    """
    The aim of the function is get a list of query state objects for a list of VINS

    Examples:
    | get_query_state_objects_for_vins | username | password | json.dumps(['MCRTESTVIN001']) | True |
    """

    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }
    url = get_url()
    send = url + "/dv/state/query-state"
    print("\n", send)
    print(ARRAYOFVINS, array_of_vins)
    response = requests.request(
        "POST",
        send,
        headers=header,
        data=array_of_vins,
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("Create new digital vehicle for vehicle")
def create_new_digital_vehicle(
    username,
    password,
    dv_data="../jsonschema/new-digital-vehicle-creation.json",
    auth=True,
):
    """
    The aim of the function is to create a new digital vehicle for a vehicle

    Examples:
    | create_new_digital_vehicle | username | password | True |
    """
    token = get_keycloak_token(username, password, auth)
    print(TOKEN)
    print(token)

    header = {
        "accept": "*/*",
        "Requester": "Robot",
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    new_digital_vehicle = load_json_file(dv_data)
    # print('vehicle Digital Data to create: \n', new_digital_vehicle)
    url = get_url()
    send = url + VEHICLE_CREATE_URI
    print("\n", send)
    response = requests.request(
        "POST",
        send,
        headers=header,
        data=json.dumps(new_digital_vehicle),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print(STATUS, response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("update obg application by unique id")
def update_obg_application_by_unique_id(user, password, unique_id: str, is_enabled: bool, auth=True):
    """
    Update the OBG of a vehicle by Uniqie ID

    Examples:
    | update obg application by unique id | username | password | unique_id | True
    """
    url = get_url()
    obg_update = url + V2_VEHICLES_URI + unique_id + "/applications/obg"

    token = get_keycloak_token(user, password, auth)

    print("token")
    print(token)
    vs_headers = {
        "Authorization": AUTHTYPE + token,
        "Content-Type": CONTENTTYPE,
    }

    payload = {"obgApplication": {"isEnabled": is_enabled}}

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(
        "PUT",
        obg_update,
        headers=vs_headers,
        data=json.dumps(payload),
        timeout=get_var_from_file(systemVarFile, "timeout"),
    )
    print("\n", obg_update)
    print(STATUS, response.status_code)
    return response.status_code


@keyword("Create vehicle")
def create_vehicle(data, user, pw, va):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    vin = "TESTVIN" + str(random.randint(1000000000, 9999999999))
    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)
    json_request_body["vin"] = vin
    json_request_body["uniqueId"] = unique_id
    json_request_body["hvin"] = vin + unique_id

    if va == "FALSE":
        json_request_body["enableVA"] = "false"
    else:
        json_request_body["enableVA"] = "true"

    payload = json.dumps(json_request_body)

    url = get_url()
    create_vehicle = url + VEHICLE_CREATE_URI
    print(create_vehicle)
    print(payload)
    response = requests.request("POST", create_vehicle, headers=header, data=payload)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Create vehicle with Different Config")
def create_vehicle_diff_config(data, user, pw, para, va, vin_type):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    if va == "FALSE":
        json_request_body["enableVA"] = "false"
    else:
        json_request_body["enableVA"] = "true"

    if vin_type == "VALID":
        vin2 = "TESTVIN" + str(random.randint(1000000000, 9999999999))
        json_request_body["vin"] = vin2
        json_request_body["uniqueId"] = unique_id2
    else:
        vin2 = "INVALIDVIN" + str(random.randint(10, 99))
        json_request_body["vin"] = vin2
        json_request_body["uniqueId"] = unique_id2

    if para == "signalDictionary":
        json_request_body["signalDictionary"] = DecimalValue
    elif para == "diagnosticsDictionary":
        json_request_body["diagnosticsDictionary"] = DecimalValue
    elif para == "queries":
        json_request_body["queries"] = DecimalValue
    elif para == "protocolChannels":
        json_request_body["protocolChannels"] = DecimalValue

    payload = json.dumps(json_request_body)

    url = get_url()
    create_vehicle = url + VEHICLE_CREATE_URI
    print(create_vehicle)
    print(payload)
    response = requests.request("POST", create_vehicle, headers=header, data=payload)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Policies controller from DV")
def policies_controller(data, user, pw, vin):
    uri = "/v2/vehicles/policies/dataProduct?identifierType=VIN"
    response = call_policy_check_endpoint(vin, user, pw, uri, data)
    return response[0], response[1]


@keyword("Delete Vehicle for VCI")
def delete_vci_vehicle(user, pw, vin):
    text, status_code = delete_vehicle(user, pw, vin)
    return text, status_code


@keyword("Delete Vehicle")
def delete_vehicle(user, pw, testVin):
    token = get_keycloak_token(user, pw)
    global vin
    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    if testVin == "":
        tcVin = vin
    else:
        tcVin = testVin
    url = get_url()
    vin_string = str(tcVin)
    print(type(vin_string))
    print(vin_string)

    vin_striped = vin_string.strip("['']")

    delete_vehicle = url + VEHICLES + vin_striped

    print(delete_vehicle)
    response = requests.request("DELETE", delete_vehicle, headers=headers)

    if response.status_code != 403:
        return response.text, response.status_code


@keyword("Fix vehicle config")
def fix_vehicle_config(data, user, pw, unique_id):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    payload = json.dumps(json_request_body)

    url = get_url()

    unique_id_string = str(unique_id)
    print(type(unique_id_string))
    print(unique_id_string)

    unique_id_striped = unique_id_string.strip("['']")

    fix_vehicle_config = url + V2_VEHICLES_URI + unique_id_striped + V2_VEHICLES_STATUS_URI

    print(fix_vehicle_config)
    response = requests.request("PUT", fix_vehicle_config, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("obg vehicle discovery")
def obg_discovery(data, user, pw, unique_id):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    unique_id_string = str(unique_id)
    print(type(unique_id_string))
    print(unique_id_string)

    unique_id_striped = unique_id_string.strip("['']")

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    payload = json.dumps(json_request_body)
    obg_discovery = url + V2_VEHICLES_URI + unique_id_striped + "/applications/obg"

    print(obg_discovery)
    response = requests.request("PUT", obg_discovery, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Activating configs")
def activate_config(data, user, pw, unique_id):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    unique_id_string = str(unique_id)
    print(type(unique_id_string))
    print(unique_id_string)

    unique_id_striped = unique_id_string.strip("['']")

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    payload = json.dumps(json_request_body)
    activate_config = url + V2_VEHICLES_URI + unique_id_striped + V2_MANIFEST_URI

    print(activate_config)
    response = requests.request("PUT", activate_config, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("changing pending configs date")
def date_change(data, user, pw, day, unique_id):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)
    unique_id_string = str(unique_id)
    print(type(unique_id_string))
    print(unique_id_string)

    unique_id_striped = unique_id_string.strip("['']")
    json_request_body["identifier"] = unique_id_striped

    int_day = int(day)
    now = datetime.now(timezone.utc) - timedelta(days=int_day)
    nowchanged = now.strftime("%Y-%m-%dT%H:%M:%S.000Z")

    json_request_body["pendingConfig"]["timestamp"] = nowchanged

    payload = json.dumps(json_request_body)

    activate_config = url + "/v2/vehicles/policies/dataProduct/pending?identifierType=VIN"
    print(payload)
    print(activate_config)
    response = requests.request("PUT", activate_config, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Get Vehicle config")
def vehicle_config(data, user, pw, vin):
    token = get_keycloak_token(user, pw)

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)
    vin_str = str(vin)
    print(type(vin_str))
    print(vin_str)
    vin_striped = vin_str.strip("['']")

    json_request_body["identifiers"][0] = vin_striped

    payload = json.dumps(json_request_body)

    header = {"accept": "*/*", "Requester": "Robot", "Authorization": AUTHTYPE + token, "Content-Type": CONTENTTYPE}
    url = get_url()
    send = url + V2_VEHICLES_VIN_URI
    print(send)
    print(payload)
    response = requests.request("POST", send, headers=header, data=payload)
    print(response.status_code)
    print(response.text)

    return response.text, response.status_code


@keyword("Check data product on Vehicle")
def check_data_product(data, user, pw, vin, dp_id):
    response_text, response_status_code = vehicle_config(data, user, pw, vin)

    file = open(json_response_file, "w")
    responsestr = str(response_text)
    file.write(responsestr)
    file.close()

    open_json_file = open(json_response_file, "r")
    read_json_data = open_json_file.read()
    actual_file = json.loads(read_json_data)

    str_json = str(actual_file)

    data_product = "'" + dp_id + "'"
    print(data_product)
    print(str_json)
    if data_product in str_json:
        response = "DATA PRODUCT DELIVERED"
    else:
        response = "DATA PRODUCT NOT DELIVERED"

    print(response)
    return response


@keyword("Data Product policy date")
def policy_date_change(data, user, pw, vin):
    response_text, response_code = vehicle_config(data, user, pw, vin)

    file = open(json_response_file, "w")
    responsestr = str(response_text)
    file.write(responsestr)
    file.close()

    open_json_data = open(json_response_file, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)
    datetime_str = json_request_body["data"][0]["policies"]["dataProduct"]["pending"]["timestamp"]

    date = datetime_str.split("T")
    past_date_changed = date[0]
    now = datetime.now(timezone.utc)
    nowchanged = now.strftime("%Y-%m-%d")

    print(past_date_changed)
    print(nowchanged)

    if past_date_changed == nowchanged:
        response = "DATA PRODUCT DELIVERY RETRIED SUCESSFUL"
    else:
        response = "DATA PRODUCT DELIVERY RETRIED FAILED"

    print(response)
    return response


@keyword("updating network configs")
def update_network_config(data, user, pw, unique_id, para):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    unique_id_string = str(unique_id)
    print(type(unique_id_string))
    print(unique_id_string)

    unique_id_striped = unique_id_string.strip("['']")

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    if para == "signalDictionary":
        json_request_body["SIGNAL_DICTIONARY"] = DecimalValue
    elif para == "diagnosticsDictionary":
        json_request_body["DIAGNOSTICS_DICTIONARY"] = DecimalValue
    elif para == "queries":
        json_request_body["QUERIES"] = DecimalValue
    elif para == "protocolChannels":
        json_request_body["PROTOCOL_CHANNELS"] = DecimalValue

    payload = json.dumps(json_request_body)
    update_config = url + V2_VEHICLES_URI + unique_id_striped + V2_MANIFEST_URI

    response = requests.request("PUT", update_config, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Get eligibility Criteria for VIN")
def get_eligibility_criteria_for_vin(user, password, vin: list):
    asserts = get_assets_by_vins_or_ids(user, password, vin)
    asserts_json = json.loads(asserts[0])

    eligibility_criteria = {
        "eligibilityCriteria": {
            "assets": {
                "brand": [asserts_json["data"][0]["brand"]],
                "modelRange": [asserts_json["data"][0]["modelRange"]],
                "modelYear": [asserts_json["data"][0]["modelYear"]],
                "fuelType": asserts_json["data"][0]["fuelType"],
                "fleetId": [asserts_json["data"][0]["fleetId"]],
            }
        },
        "eventType": "APPLY_DATA_PRODUCTS",
    }

    print("\neligibilityCriteria::", str(eligibility_criteria))
    return eligibility_criteria


@keyword("Update Fleet ID for VIN Lib")
def update_fleet_id_for_vin(user, pw, vin: list, fleetid):
    token = get_keycloak_token(user, pw)
    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    url = get_url() + V2_VEHICLES_FLEET
    print(url)
    if fleetid == "null":
        payload = json.dumps({"vins": vin, "fleetId": None})
    else:
        payload = json.dumps({"vins": vin, "fleetId": fleetid})

    response = requests.request("PUT", url, headers=headers, data=payload)

    print(response.text)
    print(response.status_code)
    return response.status_code


@keyword("Get FleetId for Vin")
def get_fleet_id_for_vin(user, pw, vin: list):
    asserts = get_assets_by_vins_or_ids(user, pw, vin)
    asserts_json = json.loads(asserts[0])
    print("FleetID::", [asserts_json["data"][0]["fleetId"]])
    return [asserts_json["data"][0]["fleetId"]]


@keyword("Update Vin config")
def update_vin_config(data, user, pw, vin, fleet):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    vin_string = str(vin)
    print(type(vin_string))
    print(vin_string)

    vin_striped = vin_string.strip("['']")

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    json_request_body["fleetId"] = fleet

    payload = json.dumps(json_request_body)
    update_vin_config = url + UPDATE_VEHICLE_URI + vin_striped

    print(update_vin_config)
    print(payload)
    response = requests.request("PATCH", update_vin_config, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Update fleetid")
def update_fleetid(data, user, pw, vin, fleet):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    vin_string = str(vin)
    print(type(vin_string))
    print(vin_string)

    vin_striped = vin_string.strip("['']")

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    json_request_body["vins"][0] = vin_striped
    json_request_body["fleetId"] = fleet

    payload = json.dumps(json_request_body)
    update_fleet_id = url + V2_VEHICLES_FLEET

    print(update_fleet_id)
    print(payload)
    response = requests.request("PUT", update_fleet_id, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Delete fleetid")
def delete_fleetid(data, user, pw, vin):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}

    url = get_url()

    vin_string = str(vin)
    print(type(vin_string))
    print(vin_string)

    vin_striped = vin_string.strip("['']")

    open_json_data = open(data, "r")
    read_json_data = open_json_data.read()
    json_request_body = json.loads(read_json_data)

    json_request_body["vins"][0] = vin_striped

    payload = json.dumps(json_request_body)
    delete_fleet_id = url + V2_VEHICLES_FLEET

    print(delete_fleet_id)
    print(payload)
    response = requests.request("DELETE", delete_fleet_id, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTYBODY, response.status_code


@keyword("Dictionary policies from DV")
def dictionary_policies(data, user, pw, vin):
    uri = "/v2/vehicles/policies/dictionary?identifierType=VIN"

    response = call_policy_check_endpoint(vin, user, pw, uri, data)
    return response[0], response[1]


@keyword("Update vehicle data product tag")
def update_dp_tags(vin, dpTag, user, pw):
    token = get_keycloak_token(user, pw)
    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    url = get_url() + V2_VEHICLES_TAGS
    payload = json.dumps({"vins": [vin], "tag": dpTag})

    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)
    if response.status_code != 200:
        print("FAILED to update tag")

    return response.text


@keyword("Delete vehicle data product tag")
def delete_dp_tag(vin, dpTag, user, pw):
    token = get_keycloak_token(user, pw)
    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token}
    url = get_url() + V2_VEHICLES_TAGS
    payload = json.dumps({"vins": [vin], "tag": dpTag})

    response = requests.request("DELETE", url, headers=headers, data=payload)
    print(response.text)
    if response.status_code != 200:
        print("FAILED to delete tag")

    return response.text


@keyword("Get vehicle data product tag")
def get_dp_tag(vin, user, pw):
    token = get_keycloak_token(user, pw)
    headers = {"accept": "*/*", "Content-Type": CONTENTTYPE, "Requester": "Robot", "Authorization": AUTHTYPE + token, "vins": vin}
    url = get_url() + V2_VEHICLES_TAGS

    response = requests.request("GET", url, headers=headers, data="")
    print(response.text)
    if response.status_code != 200:
        print("FAILED to get tag")

    return response.text
