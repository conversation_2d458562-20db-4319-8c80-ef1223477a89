import EnumStatus_pb2 as _EnumStatus_pb2
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnumChargeControlOperation(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED: _ClassVar[EnumChargeControlOperation]
    ENUM_CHARGE_CONTROL_OPERATION_START: _ClassVar[EnumChargeControlOperation]
    ENUM_CHARGE_CONTROL_OPERATION_STOP: _ClassVar[EnumChargeControlOperation]

class EnumChargeState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CHARGE_STATE_UNSPECIFIED: _ClassVar[EnumChargeState]
    ENUM_CHARGE_STATE_DISCHARGING: _ClassVar[EnumChargeState]
    ENUM_CHARGE_STATE_WAITING_TO_CHARGE: _ClassVar[EnumChargeState]
    ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS: _ClassVar[EnumChargeState]
    ENUM_CHARGE_STATE_FULLY_CHARGED: _ClassVar[EnumChargeState]
    ENUM_CHARGE_STATE_CHARGE_ERROR: _ClassVar[EnumChargeState]

class EnumChargeType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CHARGE_TYPE_UNSPECIFIED: _ClassVar[EnumChargeType]
    ENUM_CHARGE_TYPE_IMMEDIATE: _ClassVar[EnumChargeType]
    ENUM_CHARGE_TYPE_FIXED_SCHEDULE: _ClassVar[EnumChargeType]
    ENUM_CHARGE_TYPE_SCHEDULE_PLUS: _ClassVar[EnumChargeType]
    ENUM_CHARGE_TYPE_SMART_SCHEDULE: _ClassVar[EnumChargeType]
    ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE: _ClassVar[EnumChargeType]

ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED: EnumChargeControlOperation
ENUM_CHARGE_CONTROL_OPERATION_START: EnumChargeControlOperation
ENUM_CHARGE_CONTROL_OPERATION_STOP: EnumChargeControlOperation
ENUM_CHARGE_STATE_UNSPECIFIED: EnumChargeState
ENUM_CHARGE_STATE_DISCHARGING: EnumChargeState
ENUM_CHARGE_STATE_WAITING_TO_CHARGE: EnumChargeState
ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS: EnumChargeState
ENUM_CHARGE_STATE_FULLY_CHARGED: EnumChargeState
ENUM_CHARGE_STATE_CHARGE_ERROR: EnumChargeState
ENUM_CHARGE_TYPE_UNSPECIFIED: EnumChargeType
ENUM_CHARGE_TYPE_IMMEDIATE: EnumChargeType
ENUM_CHARGE_TYPE_FIXED_SCHEDULE: EnumChargeType
ENUM_CHARGE_TYPE_SCHEDULE_PLUS: EnumChargeType
ENUM_CHARGE_TYPE_SMART_SCHEDULE: EnumChargeType
ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE: EnumChargeType

class SetChargeControlRequest(_message.Message):
    __slots__ = ["charge_control_request"]
    CHARGE_CONTROL_REQUEST_FIELD_NUMBER: _ClassVar[int]
    charge_control_request: EnumChargeControlOperation
    def __init__(self, charge_control_request: _Optional[_Union[EnumChargeControlOperation, str]] = ...) -> None: ...

class SetChargeControlResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _EnumStatus_pb2.EnumStatus
    def __init__(self, status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...) -> None: ...

class GetChargeStateRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetChargeStateResponse(_message.Message):
    __slots__ = ["charging_status", "status"]
    CHARGING_STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    charging_status: EnumChargeState
    status: _EnumStatus_pb2.EnumStatus
    def __init__(
        self, charging_status: _Optional[_Union[EnumChargeState, str]] = ..., status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...
    ) -> None: ...

class SetChargeSettingsRequest(_message.Message):
    __slots__ = ["charge_type", "off_peak_start_time", "off_peak_stop_time"]
    CHARGE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OFF_PEAK_START_TIME_FIELD_NUMBER: _ClassVar[int]
    OFF_PEAK_STOP_TIME_FIELD_NUMBER: _ClassVar[int]
    charge_type: EnumChargeType
    off_peak_start_time: int
    off_peak_stop_time: int
    def __init__(
        self,
        charge_type: _Optional[_Union[EnumChargeType, str]] = ...,
        off_peak_start_time: _Optional[int] = ...,
        off_peak_stop_time: _Optional[int] = ...,
    ) -> None: ...

class SetChargeSettingsResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _EnumStatus_pb2.EnumStatus
    def __init__(self, status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...) -> None: ...

class GetChargeSettingsRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetChargeSettingsResponse(_message.Message):
    __slots__ = ["charge_type", "off_peak_start_time", "off_peak_stop_time", "status"]
    CHARGE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OFF_PEAK_START_TIME_FIELD_NUMBER: _ClassVar[int]
    OFF_PEAK_STOP_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    charge_type: EnumChargeType
    off_peak_start_time: int
    off_peak_stop_time: int
    status: _EnumStatus_pb2.EnumStatus
    def __init__(
        self,
        charge_type: _Optional[_Union[EnumChargeType, str]] = ...,
        off_peak_start_time: _Optional[int] = ...,
        off_peak_stop_time: _Optional[int] = ...,
        status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...,
    ) -> None: ...

class NotifyChargeState(_message.Message):
    __slots__ = ["charging_status", "status"]
    CHARGING_STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    charging_status: EnumChargeState
    status: _EnumStatus_pb2.EnumStatus
    def __init__(
        self, charging_status: _Optional[_Union[EnumChargeState, str]] = ..., status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...
    ) -> None: ...

class NotifyChargeSettings(_message.Message):
    __slots__ = ["charge_type", "off_peak_start_time", "off_peak_stop_time", "status"]
    CHARGE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OFF_PEAK_START_TIME_FIELD_NUMBER: _ClassVar[int]
    OFF_PEAK_STOP_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    charge_type: EnumChargeType
    off_peak_start_time: int
    off_peak_stop_time: int
    status: _EnumStatus_pb2.EnumStatus
    def __init__(
        self,
        charge_type: _Optional[_Union[EnumChargeType, str]] = ...,
        off_peak_start_time: _Optional[int] = ...,
        off_peak_stop_time: _Optional[int] = ...,
        status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...,
    ) -> None: ...
