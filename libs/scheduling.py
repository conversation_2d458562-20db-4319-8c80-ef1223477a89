#!/usr/bin python3
"""
this script contains functions to aid in
the testing of the scheduling service
"""
import base64
import json
import os
import ssl
import time
import datetime
import requests
from pyaml_env import parse_config, BaseConfig
import numpy as np
from robot.api.deco import keyword, not_keyword
import paho.mqtt.client as paho
import pymongo
import boto3
from collections import deque
import EVChargingProtobufMessage_pb2
import forgeRock
import uuid

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"


# SYSTEM_VAR_FILE = "../variables/system-dev.yml"
SYSTEM_VAR_FILE = "../variables/system-pre-prod.yml"
e2e_var_file = "../variables/system-pre-prod.yml"

cert = os.getenv("scheduled_charging_chain_cert.pem")
cert_path = True

OFF_PEAK_SCHEDULE_JSON_FILE = "../jsonfiles/offPeakSchedule.json"
SMART_SCHEDULE_JSON_FILE = "../jsonfiles/smartSchedule.json"
DEPARTURE_SCHEDULE_JSON_FILE = "../jsonfiles/DepartureSchedule.json"
UPDATE_DEPARTURE_SCHEDULE_JSON_FILE = "../jsonfiles/updateDepartureSchedule.json"

byte_message_queue = deque()
deserialised_messages = deque()

random_generator = np.random.default_rng()


def get_url():
    """
    Get URL from a yaml file
    """
    system_vars = BaseConfig(parse_config(SYSTEM_VAR_FILE))
    return getattr(system_vars, "scheduled-charging")


def get_var_from_file(var_file, var_name):
    """
    get a variable from a yml file
    """
    sys_vars = BaseConfig(parse_config(var_file))
    return getattr(sys_vars, var_name)


@keyword("load json file")
def load_json_file(json_file_location):
    """
    This function loads a json file and returns a
    json data object

    Examples:
    | load_json_file | ...location/file.json |"""
    with open(json_file_location, encoding="utf-8") as file:
        json_object = json.load(file)
        # print("\n", "json file from: ", json_file_location,
        #     "  \n", json_file, "\n")
        print("\n", "json file from: ", json_file_location, "  \n")
    return json_object


@not_keyword
def generate_random_off_peak_schedule():
    # first we need to create a random start and stop time and update the document
    with open(OFF_PEAK_SCHEDULE_JSON_FILE, "r") as file:
        off_peak_request = json.load(file)

    start_number = str(random_generator.integers(17, 22))
    random_start_time = start_number + ":00"
    end_number = str(random_generator.integers(5, 9))
    random_end_time = "0" + end_number + ":00"
    off_peak_request["start_time"] = random_start_time
    off_peak_request["end_time"] = random_end_time

    with open(OFF_PEAK_SCHEDULE_JSON_FILE, "w") as file:
        json.dump(off_peak_request, file, indent=2)


@not_keyword
def generate_random_smart_schedule():
    # first we need to create a random start and stop time and update the document
    with open(SMART_SCHEDULE_JSON_FILE, "r") as file:
        smart_request = json.load(file)

    departure_time = str(random_generator.integers(5, 9))
    random_departure_time = "0" + departure_time + ":00"
    smart_request["weekly_routine"]["mon"]["departure_time"] = random_departure_time
    smart_request["weekly_routine"]["wed"]["departure_time"] = random_departure_time

    with open(SMART_SCHEDULE_JSON_FILE, "w") as file:
        json.dump(smart_request, file, indent=2)


@not_keyword
def generate_random_departure_schedule():
    # first we need to create a random departure time and date and update the document
    with open(DEPARTURE_SCHEDULE_JSON_FILE, "r") as file:
        departure_schedule = json.load(file)

    start_number = random_generator.integers(5, 22)
    random_start_time = "0" + str(start_number) + ":00" if start_number < 10 else str(start_number) + ":00"
    departure_schedule["routines"][0]["departure_time"] = random_start_time

    days_of_the_week = [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
    ]
    random_day_int = random_generator.integers(0, 6)
    random_day = days_of_the_week[random_day_int]
    departure_schedule["routines"][0]["day_of_week"] = random_day

    with open(DEPARTURE_SCHEDULE_JSON_FILE, "w") as file:
        json.dump(departure_schedule, file, indent=2)
    print("random day & start_time :: ", random_day, random_start_time)
    return random_day, random_start_time


@not_keyword
def generate_random_update_departure_schedule(routine_id):
    # first we need to create a random departure time and date and update the document
    with open(UPDATE_DEPARTURE_SCHEDULE_JSON_FILE, "r") as file:
        departure_schedule_update = json.load(file)

    random_day, random_start_time = generate_random_departure_schedule()
    # update first routine
    departure_schedule_update["routines"][0]["id"] = routine_id
    departure_schedule_update["routines"][0]["day_of_week"] = random_day
    departure_schedule_update["routines"][0]["departure_time"] = random_start_time

    current_cabin_clean_status = departure_schedule_update["routines"][0]["tasks"]["cabin_deep_clean"]["enabled"]
    departure_schedule_update["routines"][0]["tasks"]["cabin_deep_clean"]["enabled"] = True if (current_cabin_clean_status is False) else False

    departure_schedule_update["routines"][0]["tasks"]["climate_preconditioning"]["target_temp_celsius"] = round(random_generator.uniform(16, 28))
    # (round(random.uniform(16, 28) / 0.5) * 0.5)

    with open(UPDATE_DEPARTURE_SCHEDULE_JSON_FILE, "w") as file:
        json.dump(departure_schedule_update, file, indent=2)


@not_keyword
def schedule_get_token(broker_user, broker_client_secret, private_key=""):
    """
    This function gets and returns a token from keycloak
    using
    """
    system_vars = BaseConfig(parse_config(SYSTEM_VAR_FILE))
    keycloak_url = getattr(system_vars, "keycloak_url")

    if len(private_key) <= 5:
        payload = "client_id=" + broker_user + "&client_secret=" + broker_client_secret + "&grant_type=" + "client_credentials"
        print("***Keycloak WITHOUT private key")
    else:
        payload = (
            "client_id="
            + broker_user
            + "&client_secret="
            + broker_client_secret
            + "&grant_type="
            + "client_credentials"
            + "&client_assertion_type="
            + "urn%3Aietf%3Aparams%3Aoauth%3Aclient-assertion-type%3Ajwt-bearer"
            + "&client_assertion="
            + private_key
        )
        print("***Keycloak WITH private key")
    kc_header = {"Content-Type": "application/x-www-form-urlencoded"}

    count = 0
    while count < 5:
        response = requests.request("POST", keycloak_url, headers=kc_header, data=payload)
        if response.status_code in [500, 503, 504]:
            count = count + 1
        else:
            break

    print("Got Token:: ")
    y = json.loads(response.text)
    # print(y['access_token'])
    return y["access_token"]


@not_keyword
def serialize_datetime(obj):
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    raise TypeError("Type not serializable")


@not_keyword
def schedule_on_connect(client, userdata, flags, rc):
    """
    This function connects to HIVE MQ
    """
    print(rc)
    if rc == 0:
        print("Connected to MQTT Broker!")
        client.connected_flag = True
        global SubsData
        global retain
        SubsData = -1
        retain = -1
    else:
        print("Failed to connect, return code %d\n", rc)


@not_keyword
def schedule_on_message(client, userdata, msg):
    """
    Once connected to hive this function will get the messages from hive and print them.
    The data is deserialized using the EVChargingProtobufMessage_pb2.pyi
    and the deserialized messages are added to a deque collection for manipulation.
    """
    print("")
    print("")
    print("")
    print(f"Received '{msg.payload}' from '{msg.topic}' topic qos '{msg.qos}' retained '{msg.retain}'")
    if msg.retain == 1:
        print("This is a retained message")
    global SubsData
    global retain
    # print("\n\n Type of message recieved:", type(msg.payload), "\n")
    decoded_payload = decrypt_hive_data_using_kms(os.getenv("schedules_kms_key_id"), msg.payload)
    SubsData = decoded_payload
    # SubsData = msg.payload
    retain = msg.retain
    byte_message_queue.append(SubsData)
    my_request = EVChargingProtobufMessage_pb2.SetChargeSettingsRequest()
    my_request.ParseFromString(SubsData)
    deserialised_messages.append(my_request)
    print("***Deserialized***")
    print(f"Received '{my_request}' from '{msg.topic}' topic qos '{msg.qos}' retained '{msg.retain}'")


@not_keyword
def schedule_on_message_write_to_file(client, userdata, msg):
    """
    Once connected to hive this function will get the messages from hive.
    The data is deserialized using the EVChargingProtobufMessage_pb2.pyi
    and the deserialized messages are added to a deque collection for manipulation
    and then printed onto a file
    """
    print(f"Received '{msg.payload}' from '{msg.topic}' topic qos '{msg.qos}' retained '{msg.retain}'")

    my_request = EVChargingProtobufMessage_pb2.SetChargeSettingsRequest()
    my_request.ParseFromString(msg.payload)
    deserialised_messages.append(my_request)
    now = int(time.time() * 1000)
    with open("./" + str(now), "wb") as fd:
        fd.write(my_request)


@not_keyword
def schedule_on_publish(client, userdata, mid):
    print("data published: " + str(mid))


@not_keyword
def schedule_on_disconnect(client, userdata, rc):
    print("disconnecting reason  " + str(rc))
    client.connected_flag = False
    if rc != 0:
        print("Unexpected disconnection.")
    else:
        print("Disconnected")


@not_keyword
def schedule_connect_to_broker(
    unique_id,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    private_key="",
    auth=True,
    tls=True,
):
    """
    The function connects to a Hive Broker using user credentials and cert files
    """
    system_vars = BaseConfig(parse_config(SYSTEM_VAR_FILE))
    broker = getattr(system_vars, "hive_broker")
    port = getattr(system_vars, "hive_port")

    if auth is True:
        token = schedule_get_token(broker_user, broker_client_secret, private_key)
        print(token)
    else:
        token = getattr(system_vars, "default_token")

    client = paho.Client(client_id=unique_id, clean_session=True)
    client.schedule_on_disconnect = schedule_on_disconnect
    client.schedule_on_connect = schedule_on_connect
    client.schedule_on_message = schedule_on_message
    client.schedule_on_publish = schedule_on_publish

    if tls is True:
        client.tls_set(
            certfile=certfile,
            ca_certs=ca_certs,
            keyfile=keyfile,
            cert_reqs=ssl.CERT_REQUIRED,
            tls_version=ssl.PROTOCOL_TLSv1_2,
            ciphers=None,
        )
        client.tls_insecure_set(False)  # for aws

    client.username_pw_set(unique_id, password=token)
    if (auth is False) or (tls is False):
        client.connected_flag = True
    else:
        client.connected_flag = False
    client.loop_start()
    client.connect(broker, port, 10)
    while not client.connected_flag:  # wait for connection
        time.sleep(0.1)
    client.loop_stop()
    print("connected to broker")
    return client


@keyword("Subscribe to scheduling topic in hive")
def schedule_subscribe_to_topic(
    unique_id,
    topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    private_key="",
    time_to_subs=1,
    write_to_file=False,
):
    """
    Subcribes to a topic and returns the payload
    """

    client = schedule_connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        certfile,
        ca_certs,
        keyfile,
        private_key,
    )
    client.loop_start()  # start the loop
    client.subscribe(topic, qos=0)
    print("Subscribing to topic:: " + topic)
    if write_to_file is False:
        client.schedule_on_message = schedule_on_message
    else:
        client.schedule_on_message = schedule_on_message_write_to_file  # Need to get the mssages and add to queue here.
    time.sleep(time_to_subs)  # wait

    client.loop_stop()  # stop the loop
    client.disconnect()

    if deserialised_messages:
        return list(deserialised_messages.queue)
    else:
        return "no messages"


@keyword("send off peak api request get request in hive")
def send_off_peak_api_request_get_request_in_hive(
    unique_id,
    topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    private_key="",
    time_to_subs=2,
    write_to_file=False,
):
    """
    This function first Subcribes to a scheduled charging hive topic.
    The function then sends an API request to create an off peak charging schedule with randomly generated start and end times.
    Messages in the hive topic are read. The latest message request in the hive topic is returned
    """
    generate_random_off_peak_schedule()

    # connect to broker
    client = schedule_connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        certfile,
        ca_certs,
        keyfile,
        private_key,
    )
    # start the loop
    client.loop_start()
    # subscribe to topic
    client.subscribe(topic, qos=0)
    print("Subscribing to topic:: " + topic)

    # Now we send api request while we are subscribed to Topic
    response, status = scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "off-peak",
        unique_id,
        "private",
        OFF_PEAK_SCHEDULE_JSON_FILE,
        True,
    )

    if write_to_file is False:
        client.schedule_on_message = schedule_on_message
    else:
        client.schedule_on_message = schedule_on_message_write_to_file  # Need to get the mssages and add to queue here.
    time.sleep(time_to_subs)  # wait
    client.loop_stop()  # stop the loop
    client.disconnect()

    latest_off_peak_request = EVChargingProtobufMessage_pb2.SetChargeSettingsRequest()

    if deserialised_messages:
        latest_off_peak_request.MergeFrom(deserialised_messages.pop())
        print("the latest request is:", latest_off_peak_request)
        start_time_int = int(latest_off_peak_request.off_peak_start_time) / 60
        start_time = (str(start_time_int)[0:2]) + ":00"
        end_time_int = int(latest_off_peak_request.off_peak_stop_time) / 60
        end_time = "0" + (str(end_time_int)[0]) + ":00"

    else:
        start_time = "00:00"
        end_time = "00:00"

    return (
        response,
        status,
        str(latest_off_peak_request.charge_type),
        start_time,
        end_time,
    )


@keyword("send smart api request get request in hive")
def send_smart_charging_api_request_get_request_in_hive(
    unique_id,
    topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    private_key="",
    time_to_subs=2,
    write_to_file=False,
):
    """
    This function first Subcribes to a scheduled charging hive topic.
    The function then sends an API request to create a smart charging schedule with randomly generated departure times.
    Messages in the hive topic are read. The latest message request in the hive topic is returned
    """
    generate_random_smart_schedule()

    # connect to broker
    client = schedule_connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        certfile,
        ca_certs,
        keyfile,
        private_key,
    )
    # start the loop
    client.loop_start()
    # subscribe to topic
    client.subscribe(topic, qos=0)
    print("Subscribing to topic:: " + topic)

    # Now we send api request while we are subscribed to Topic
    response, status = scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "off-peak",
        unique_id,
        "private",
        SMART_SCHEDULE_JSON_FILE,
        True,
    )

    if write_to_file is False:
        client.schedule_on_message = schedule_on_message
    else:
        client.schedule_on_message = schedule_on_message_write_to_file  # Need to get the mssages and add to queue here.
    time.sleep(time_to_subs)  # wait
    client.loop_stop()  # stop the loop
    client.disconnect()

    latest_off_peak_request = EVChargingProtobufMessage_pb2.SetChargeSettingsRequest()

    if deserialised_messages:
        latest_off_peak_request.MergeFrom(deserialised_messages.pop())
        # print("the deserialized messages:", deserialised_hive_message_list)
        print("the latest request is:", latest_off_peak_request)
        hive_message = str(latest_off_peak_request.charge_type)

    else:
        hive_message = "Unable to get hive message"

    return response, status, hive_message


#######################################################################################


@keyword("create or update departure schedule api request get request in hive")
def create_or_update_departure_schedule_api_request_get_request_in_hive(
    unique_id,
    topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    private_key="",
    new_departure=True,
    routine_id=[],
    time_to_subs=2,
    write_to_file=False,
):
    """
    This function first Subcribes to a scheduled charging hive topic.
    The function then sends an API request to create a smart charging schedule with randomly generated departure times.
    Messages in the hive topic are read. The latest message request in the hive topic is returned
    """
    client = schedule_connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        certfile,
        ca_certs,
        keyfile,
        private_key,
    )
    # start the loop
    client.loop_start()
    # subscribe to topic
    client.subscribe(topic, qos=0)
    print("Subscribing to topic:: " + topic)
    # Now we send api request while we are subscribed to Topic
    if new_departure:
        response, status = scheduled_departure_request(
            os.getenv("forgeRockEmail"),
            os.getenv("forgeRockPassword"),
            "create",
            unique_id,
            "private",
            None,
            DEPARTURE_SCHEDULE_JSON_FILE,
            True,
        )
    else:
        response, status = scheduled_departure_request(
            os.getenv("forgeRockEmail"),
            os.getenv("forgeRockPassword"),
            "update",
            unique_id,
            "private",
            routine_id,
            UPDATE_DEPARTURE_SCHEDULE_JSON_FILE,
            True,
        )

    if write_to_file is False:
        client.schedule_on_message = schedule_on_message
    else:
        client.schedule_on_message = schedule_on_message_write_to_file  # Need to get the mssages and add to queue here.
    time.sleep(time_to_subs)  # wait
    client.loop_stop()  # stop the loop
    client.disconnect()

    if deserialised_messages:
        # latest_departure_schedule = scheduling_service_pb2.DepartureEvent()
        # latest_departure_schedule.MergeFrom(deserialised_messages.pop())
        # departure_time_int = int(latest_departure_schedule.departure_time) / 60
        # departure_time = (str(departure_time_int)[0:2]) + ":00"
        # print("the latest departure request time is:", departure_time)
        # hive_message = str(departure_time, latest_departure_schedule.departure_tasks)
        hive_message = "still unable to decode message "

    else:
        hive_message = "Unable to get message"

    return response, status, hive_message


@keyword("delete departure schedule api request and check request in hive")
def delete_departure_schedule_api_request_check_request_in_hive(
    unique_id,
    topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    routine_id,
    private_key="",
    time_to_subs=2,
    write_to_file=False,
):
    client = schedule_connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        certfile,
        ca_certs,
        keyfile,
        private_key,
    )
    # start the loop
    client.loop_start()
    # subscribe to topic
    client.subscribe(topic, qos=0)
    print("Subscribing to topic:: " + topic)
    response, status = scheduled_departure_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "delete",
        unique_id,
        "private",
        routine_id,
        None,
        True,
    )

    if write_to_file is False:
        client.schedule_on_message = schedule_on_message
    else:
        client.schedule_on_message = schedule_on_message_write_to_file  # Need to get the mssages and add to queue here.
    time.sleep(time_to_subs)  # wait
    client.loop_stop()  # stop the loop
    client.disconnect()

    if deserialised_messages:
        hive_message = "Still working on this part"
    else:
        hive_message = "Unable to get hive message"

    return response, status, hive_message


@keyword("send message to response topic in hive")
def send_message_to_topic_of_hive_broker(
    unique_id,
    topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    private_key="",
    time_to_subs=2,
    write_to_file=False,
):
    """
    This function first Subcribes to a scheduled charging hive topic.
    The function then send a message to a hive broker topic
    Messages in the hive topic are read. The latest message request in the hive topic is returned
    """

    # connect to broker
    client = schedule_connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        certfile,
        ca_certs,
        keyfile,
        private_key,
    )
    # start the loop
    client.loop_start()
    # subscribe to topic
    client.subscribe(topic, qos=0)
    print("Subscribing to topic:: " + topic)
    # Now we send message to hive topic


@keyword("get attribute from schedule response body")
def get_attribute_from_response_body(response_body, attribute):
    """
    This function returns an attribute from the body response of
    a digital vehicle

    Examples:
    | get_attribute_from_response_body | Json-data | allowWifi |

    """
    return response_body[attribute]


C2_SCOPE = get_var_from_file(SYSTEM_VAR_FILE, "SCOPE_SCHEDULE")


@keyword("make a charging request")
def scheduled_charging_request(
    username,
    password,
    request_type,
    vehicle_uuid,
    vehicle_arch,
    gateway_access="private",
    schedule_file_location=None,
    auth=True,
):
    """
    This function makes a charging request, which could be either be a
    get, delete, smart or off-peak charge request.
    The function can make this request via the scheduling private api gateway
    or via the one app public gateway.

    Examples:
    | make a charging request
    | FR username
    | FR password
    | request_type= "get" or "delete" or "smart" or "off-peak"
    | vehicle_uuid_string
    | gateway_acess = "private" or "public"
    | schedule_file_location = "../jsonfiles/smartSchedule.json"
    | auth=True
    """

    # auth
    if auth is not True:
        token = "abcdef12345"
    elif vehicle_arch == "EVA2":
        print("EVA2")
        print("scheduleScope :: ", C2_SCOPE)
        print("eva2 username:: ", username)
        print("eva2 password:: ", password)
        token = forgeRock.get_token(username, password, ALT_SCOPE=C2_SCOPE)
    elif vehicle_arch == "EVA25":
        print("EVA25")
        username = os.environ.get("EVA25_FR_EMAIL_PREPROD")
        password = os.environ.get("EVA25_FR_PASSWORD_PREPROD")
        print("scheduleScope :: ", C2_SCOPE)
        print("eva25 username:: ", username)
        print("eva25 password:: ", password)
        token = forgeRock.get_token(username, password, ALT_SCOPE=C2_SCOPE)

    print("token")
    print(token)

    approvKey = os.getenv("ApproovToken")

    # header
    vs_headers = {
        "accept": "*/*",
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token,
        "Correlation-Id": str(uuid.uuid4()),
        "attestation": approvKey,
    }
    print(vs_headers)

    url = get_var_from_file(SYSTEM_VAR_FILE, "scheduled-charging")
    print(url)
    schedules = url + "/" + vehicle_uuid + "/schedules"
    delete_schedules = url + "/" + vehicle_uuid + "/schedules/charging"
    off_peak_schedule = url + "/" + vehicle_uuid + "/schedules/charging/off-peak"
    smart_schedule = url + "/" + vehicle_uuid + "/schedules/charging/smart"

    if gateway_access.lower() == "public":
        url = get_var_from_file(e2e_var_file, "one_app_backend_url")
        mid_string = "/api/v1/schedules/"
        schedules = url + mid_string + vehicle_uuid
        delete_schedules = url + mid_string + vehicle_uuid + "/charging"
        smart_schedule = url + mid_string + vehicle_uuid + "/charging/smart"
        off_peak_schedule = url + mid_string + vehicle_uuid + "/charging/off-peak"

    count = 0
    while count < 5:
        match request_type.lower():
            case "get":
                response = requests.request("GET", schedules, headers=vs_headers, verify=cert_path, cert=cert)
                print(schedules)
            case "delete":
                response = requests.request("DELETE", delete_schedules, headers=vs_headers, verify=cert_path, cert=cert)
                print(delete_schedules)
            case "smart":
                schedule = load_json_file(schedule_file_location)
                response = requests.request("POST", smart_schedule, headers=vs_headers, data=json.dumps(schedule), verify=cert_path, cert=cert)
                print(smart_schedule)
            case "off-peak":
                schedule = load_json_file(schedule_file_location)
                response = requests.request("POST", off_peak_schedule, headers=vs_headers, data=json.dumps(schedule), verify=cert_path, cert=cert)
                print(off_peak_schedule)
        if response.status_code in [500, 503, 504]:
            count = count + 1
        else:
            break

    print("Status: ", response.status_code)
    response_body = json.loads(response.text)
    print(response.text)
    return response_body, response.status_code


@keyword("make a departure request")
def scheduled_departure_request(
    username,
    password,
    request_type,
    vehicle_uuid,
    vehicle_arch,
    gateway_access="private",
    departure_id=None,
    schedule_file_location=None,
    auth=True,
):
    """
    This function makes a departure request. Which could be either to create, update or delete a departure.
    The function can make this request via the scheduling private api gateway or via the one app public gateway.

    Examples:
    | make a departure request
    | FR username
    | FR password
    | request_type= "create", "update" or "delete"
    | vehicle_uuid_string
    | gateway_acess = "private" or "public"
    | depature_id_string = (if making an update or delete request)
    | schedule_file_location = "./jsonfiles/DepartureSchedule.json"
    | auth=True
    """

    # auth
    if auth is not True:
        token = "abcdef12345"
    elif vehicle_arch == "EVA2":
        print("EVA2")
        print("scheduleScope :: ", C2_SCOPE)
        print("eva2 username:: ", username)
        print("eva2 password:: ", password)
        token = forgeRock.get_token(username, password, ALT_SCOPE=C2_SCOPE)
    elif vehicle_arch == "EVA25":
        print("EVA25")
        print("scheduleScope :: ", C2_SCOPE)
        print("eva2 username:: ", username)
        print("eva2 password:: ", password)
        username = os.environ.get("EVA25_FR_EMAIL_PREPROD")
        password = os.environ.get("EVA25_FR_PASSWORD_PREPROD")
        token = forgeRock.get_token(username, password, ALT_SCOPE=C2_SCOPE)

    print("token")
    print(token)

    approvKey = os.getenv("ApproovToken")

    # header
    vs_headers = {
        "accept": "*/*",
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token,
        "Correlation-Id": str(uuid.uuid4()),
        "attestation": approvKey,
    }

    url = get_var_from_file(SYSTEM_VAR_FILE, "scheduled-charging")
    departure_url = url + "/" + vehicle_uuid + "/schedules/departures/routines"

    if gateway_access.lower() == "public":
        url = get_var_from_file(e2e_var_file, "one_app_backend_url")
        departure_url = url + "/api/v1/schedules/" + vehicle_uuid + "/departures/routines"

    count = 0
    while count < 5:
        match request_type.lower():
            case "create":
                generate_random_departure_schedule()
                schedule = load_json_file(schedule_file_location)
                response = requests.request(
                    "POST",
                    departure_url,
                    headers=vs_headers,
                    data=json.dumps(schedule),
                    verify=cert_path,
                    cert=cert,
                )
            case "update":
                generate_random_update_departure_schedule(departure_id)
                schedule = load_json_file(schedule_file_location)
                response = requests.request(
                    "PUT",
                    departure_url,
                    headers=vs_headers,
                    data=json.dumps(schedule),
                    verify=cert_path,
                    cert=cert,
                )
            case "delete":
                payload = {"routine_ids": [departure_id]}
                response = requests.request(
                    "DELETE",
                    departure_url,
                    headers=vs_headers,
                    data=json.dumps(payload),
                    verify=cert_path,
                    cert=cert,
                )
        if response.status_code in [500, 503, 504]:
            count = count + 1
        else:
            break

    print("Status: ", response.status_code)

    response_body = json.loads(response.text)
    print(response.text)
    return response_body, response.status_code


@keyword("get charging schedule from database")
def get_user_charging_schedule_from_database(db_username, db_password, db_host, tls_cert, collections_name, query):
    col, client = connect_to_schedules_database_collection(db_username, db_password, db_host, tls_cert, collections_name)

    # #Find the document that was previously written
    x = col.find_one(query)

    if x is None:
        x = {}

    client.close()
    return x


@keyword("delete vehicle departure schedules from database")
def delete_vehicle_schedule_departure_info_from_database(db_username, db_password, db_host, tls_cert, collections_name, query):
    col, client = connect_to_schedules_database_collection(db_username, db_password, db_host, tls_cert, collections_name)

    y = col.delete_one(query)
    client.close()
    return y.deleted_count


@keyword("connect to shedules database and collection")
def connect_to_schedules_database_collection(db_username, db_password, db_host, tls_cert, collections_name):
    """

    This function connect to a database and gets the charging schedule of a vehicle
    Examples:
    | get_user_charging_schedule_from_database | username | password | host_address | query_dictionary

    """
    mongo_port = get_var_from_file(SYSTEM_VAR_FILE, "mongo_port")

    db_url = (
        "mongodb://"
        + db_username
        + ":"
        + db_password
        + "@"
        + db_host
        + ":"
        + mongo_port
        + "/?authMechanism=SCRAM-SHA-1"
        + "&"
        + "tls=true&tlsCAFile="
        + tls_cert
        + "&"
        + "retryWrites=false"
    )

    print(db_url)

    # Create a MongoDB client, open a connection to Amazon DocumentDB as
    # a replica set and specify the read preference as secondary preferred
    client = pymongo.MongoClient(db_url)

    # #the database to be used
    db = client.get_database("user-schedules")
    print("Connected to database: ", db)

    # #the collection to be used
    col = db.get_collection(collections_name)
    print("Connected to collections: ", col)
    return col, client


@keyword("get last scheduled departure routineId")
def get_last_scheduled_departure_routine_id(response):
    number_of_routines = len(response["departures"]["routines"])

    if number_of_routines >= 1:
        departure_id = str(response["departures"]["routines"][-1]["id"])
    else:
        departure_id = str("no departure schedules")
    print(departure_id)
    return departure_id


@keyword("get a routine from a list of routines in a schedule")
def get_schedule_of_routine_id(response, routine_id):
    for index, routine in enumerate(response["departures"]["routines"]):
        if routine["id"] == routine_id:
            return response["departures"]["routines"][index]
    return {}


@keyword("delete all departure routines")
def delete_all_departure_routines(schedule_profile, vehicle_uuid, vehicle_arch):
    for index, routine in enumerate(schedule_profile["departures"]["routines"]):
        response, status = scheduled_departure_request(
            os.getenv("forgeRockEmail"),
            os.getenv("forgeRockPassword"),
            "delete",
            vehicle_uuid,
            vehicle_arch,
            "private",
            routine,
            None,
            True,
        )
        return response, status


@not_keyword
def check_and_modify_topic(mqtt_topic):
    need_client_id = mqtt_topic.find("${mqtt-clientid}")
    if need_client_id:
        topic = mqtt_topic.replace("${mqtt-clientid}", str(os.getenv("brokerUserRead")))
    else:
        topic = mqtt_topic
    return topic


@not_keyword
def decrypt_hive_data_using_kms(key_id, encrypted_data):
    kms_key_arn = "arn:aws:kms:eu-west-2:************:key" + "/" + key_id

    session = boto3.Session(profile_name="vcdp-developers", region_name="eu-west-2")
    kms_client = session.client("kms")
    ciphertext_blob = base64.b64decode(encrypted_data)
    response = kms_client.decrypt(CiphertextBlob=ciphertext_blob, KeyId=kms_key_arn)

    plaintext = response["Plaintext"]
    return plaintext


if __name__ == "__main__":
    test_unique_id = get_var_from_file(SYSTEM_VAR_FILE, "test_uuid")
    print(f"using {test_unique_id} as unique id for this test\n")

    # first is to delete the deparure schedule database before tests start
    query = {"_id": str(test_unique_id)}
    results = delete_vehicle_schedule_departure_info_from_database(
        os.getenv("DB_USER"),
        os.getenv("DB_PASSWORD"),
        os.getenv("DB_HOST"),
        os.getenv("tls_cert"),
        "departure-schedules",
        query,
    )
    print("number of documents deleted from database: ", results)

    mqtt_topic = get_var_from_file(SYSTEM_VAR_FILE, "scheduledChargingRequest_pub_vcdp")
    topic = check_and_modify_topic(mqtt_topic)

    print("\ntopic to be subsrcipting to is: ", topic)

    # # ################## api's ################################################
    # # create_off_peak_charging_schedule
    print("\n\n\n\n****************** Create of peak charging schedule **************")
    scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "off-peak",
        test_unique_id,
        "private",
        OFF_PEAK_SCHEDULE_JSON_FILE,
        True,
    )
    time.sleep(2)
    # print("****************** get charging schedule *******************")
    scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "get",
        test_unique_id,
        "private",
        None,
        True,
    )

    # # ###################### Database ##################################
    print("\n****************** get schedule from database **************")
    query = {"_id": str(test_unique_id)}
    db_response = get_user_charging_schedule_from_database(
        os.getenv("DB_USER"),
        os.getenv("DB_PASSWORD"),
        os.getenv("DB_HOST"),
        os.getenv("tls_cert"),
        "charging-schedules",
        query,
    )
    print("database response: ", db_response, "\n\n\n")

    # create_smart_charging_schedule
    print("\n\n****************** create smart charging schedule ***********")
    scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "smart",
        test_unique_id,
        "private",
        SMART_SCHEDULE_JSON_FILE,
        True,
    )
    time.sleep(2)
    print("****************** get charging schedule ********************")
    scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "get",
        test_unique_id,
        "private",
        None,
        True,
    )

    # ###################### Database ##################################
    print("\n****************** get schedule from database **************")
    query = {"_id": str(test_unique_id)}
    db_response = get_user_charging_schedule_from_database(
        os.getenv("DB_USER"),
        os.getenv("DB_PASSWORD"),
        os.getenv("DB_HOST"),
        os.getenv("tls_cert"),
        "charging-schedules",
        query,
    )
    print("database response: ", db_response, "\n\n\n")

    # create departure schedule
    scheduled_departure_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "create",
        test_unique_id,
        "private",
        None,
        DEPARTURE_SCHEDULE_JSON_FILE,
        True,
    )

    print("\n\n****************** create departure schedule *****************")
    list_of_routines, status = scheduled_charging_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "get",
        test_unique_id,
        "private",
        None,
        True,
    )

    print("/n/nlist of routines:", list_of_routines, "/n/n")

    if status != 500:
        array_of_routines = get_last_scheduled_departure_routine_id(list_of_routines)
        # upddate departure schedule
        print("\n\n************* update departure schedule *****************")
        scheduled_departure_request(
            os.getenv("forgeRockEmail"),
            os.getenv("forgeRockPassword"),
            "update",
            test_unique_id,
            "private",
            array_of_routines,
            UPDATE_DEPARTURE_SCHEDULE_JSON_FILE,
            True,
        )

        # delete departure schedule
        print("\n\n************** delete departure schedule *************")
        scheduled_departure_request(
            os.getenv("forgeRockEmail"),
            os.getenv("forgeRockPassword"),
            "delete",
            test_unique_id,
            "private",
            array_of_routines,
            None,
            True,
        )
        print("\n\n\n")

    else:
        print(
            "\n\n------Skipped updating and Deleting functions----------\n",
            "Received a 500:internal error recieved when creating departure schedule\n\n\n",
        )

    # ############## Choose to listen to a topic indefinitely ################
    mqtt_topic = get_var_from_file(SYSTEM_VAR_FILE, "scheduledChargingResponse_sub_vcdp")
    topic = check_and_modify_topic(mqtt_topic)
    print("topic to be subsrcipting to is: ", topic)

    # subscribe to topic and listen indefinitely
    print("\n\n********* subscribe to topic ********")
    message = schedule_subscribe_to_topic(
        test_unique_id,
        topic,
        os.getenv("brokerUserRead"),
        os.getenv("brokerClientSecretRead"),
        os.getenv("HIVEMQ_CLIENT_CERTIFICATE"),
        os.getenv("HIVEMQ_CA"),
        os.getenv("HIVEMQ_PRIVATE_KEY"),
        os.getenv("KEYCLOAK_PRIVATE_KEY_BASE64"),
        200,
        False,
    )
