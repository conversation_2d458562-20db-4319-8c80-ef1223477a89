from robot.api.deco import keyword
import commonUtils


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"


serviceUrl = "oneapp-backend-url"


# GET preferences
@keyword("Get preferences")
def get_prefs(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me/preferences"

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# PATCH preferences
@keyword("Patch preferences")
def patch_prefs(user_email, user_password, prefs, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me/preferences"

    queryParams = prefs

    response = commonUtils.send_request_with_params(
        user_email, user_password, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE, params=queryParams
    )
    return response[0], response[1]
