from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig
from commonUtils import create_retry_request_session
import uuid
import time
import requests

client_id = "octopus-uk"
JOURNEY_LOGGING_PERMISSION_LABEL = "A_App_Journey"
THIRD_PARTY_SMART_CHARGING_PERMISSION_LABEL = "L_3rdParty_SmartCharging"


class PermitterPayload:
    def __init__(self, user_id, user_id_type, vin, label, client_id, expiry=None, vuid=None):
        self.user_id = user_id
        self.user_id_type = user_id_type
        self.vin = vin
        self.label = label
        self.client_id = client_id
        self.expiry = expiry
        self.vuid = vuid

    def to_json(self):
        return {
            "userId": self.user_id,
            "userIdType": self.user_id_type,
            "vin": self.vin,
            "label": self.label,
            "clientId": self.client_id,
            "expiry": self.expiry,
            "vuid": self.vuid,
        }


@keyword("POST permissions enable endpoint")
def post_permissions_enable_endpoint(vin, user_id, permitter, keycloak_token, var_file, expiry=None):
    system_vars = BaseConfig(parse_config(var_file))
    correlation_id = str(uuid.uuid4())[3:]
    permissions_endpoint = getattr(system_vars, "permissions_service_endpoint")
    headers = {"Content-Type": "application/json", "Correlation-ID": correlation_id.encode("utf-8"), "Authorization": keycloak_token}
    user_id_type = "SAP_CDC"

    permitter_payload = PermitterPayload(user_id, user_id_type, vin, label=None, client_id=None, expiry=None).to_json()
    if expiry:
        permitter_payload["expiry"] = expiry
    if permitter == THIRD_PARTY_SMART_CHARGING_PERMISSION_LABEL:
        permitter_payload["clientId"] = client_id
        permitter_payload["label"] = permitter
        permitter_payload["vin"] = vin
        return call_post_endpoint(url=permissions_endpoint + "permitter/permission/enable", json=permitter_payload, headers=headers)

    elif permitter == JOURNEY_LOGGING_PERMISSION_LABEL:
        permitter_payload["label"] = permitter
        permitter_payload["vin"] = vin
        del permitter_payload["clientId"]
        return call_post_endpoint(url=permissions_endpoint + "permitter/permission/enable", json=permitter_payload, headers=headers)

    else:
        permitter_payload["label"] = permitter
        del permitter_payload["vin"]
        del permitter_payload["clientId"]
        return call_post_endpoint(url=permissions_endpoint + "permitter/permission/enable", json=permitter_payload, headers=headers)


@keyword("call permissions revoke endpoint")
def call_permissions_revoke_endpoint(vin, user_id, user_id_type, permitter, var_file, keycloak_token, vuid=None):
    system_vars = BaseConfig(parse_config(var_file))
    correlation_id = str(uuid.uuid4())[3:]
    permissions_endpoint = getattr(system_vars, "permissions_service_endpoint")
    headers = {"Content-Type": "application/json", "Correlation-ID": correlation_id.encode("utf-8"), "Authorization": keycloak_token}
    params = {}

    permitter_payload = PermitterPayload(user_id, user_id_type, vin, label=None, client_id=None).to_json()

    if vuid:
        del permitter_payload["vin"]
        permitter_payload["vuid"] = vuid
        params = {"vehicleIdentifier": "vuid"}

    if permitter == THIRD_PARTY_SMART_CHARGING_PERMISSION_LABEL:
        permitter_payload["clientId"] = client_id
        permitter_payload["label"] = permitter
        return call_post_endpoint(url=permissions_endpoint + "permitter/permission/revoke", json=permitter_payload, headers=headers, params=params)

    elif permitter == JOURNEY_LOGGING_PERMISSION_LABEL:
        permitter_payload["label"] = permitter
        permitter_payload["vin"] = vin
        return call_post_endpoint(url=permissions_endpoint + "permitter/permission/revoke", json=permitter_payload, headers=headers, params=params)

    else:
        permitter_payload["label"] = permitter
        del permitter_payload["vin"]
        return call_post_endpoint(url=permissions_endpoint + "permitter/permission/revoke", json=permitter_payload, headers=headers, params=params)


@not_keyword
def call_post_endpoint(url, json_payload, headers, params=None):
    try:
        retry_request = create_retry_request_session()
        response = retry_request.post(url, data=json_payload, headers=headers, params=params)
    except requests.exceptions.ConnectionError as ce:
        print("ConnectionError occured: ", ce, "\nretrying request")
        time.sleep(1)
        response = retry_request.post(url, data=json_payload, headers=headers, params=params)
    return response, response.content


@not_keyword
def call_get_endpoint(url, headers, params=None):
    try:
        retry_request = create_retry_request_session()
        response = retry_request.get(url, headers=headers, params=params)
    except requests.exceptions.ConnectionError as ce:
        print("ConnectionError occured: ", ce, "\nretrying request")
        time.sleep(1)
        response = retry_request.get(url, headers=headers, params=params)
    return response, response.content
