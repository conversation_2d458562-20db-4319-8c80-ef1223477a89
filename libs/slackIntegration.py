import defusedxml.ElementTree as ET
from bs4 import BeautifulSoup
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
import os
import re

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"


def sanitise_file_path(filepath):
    filepath = re.sub(r"[^.a-zA-Z0-9]+", "_", filepath)
    return filepath


def extract_test_run_data():
    """
    Method extracts test run data from the project and creates a text file in repo to use as a payload
    """
    url_project = str(os.environ.get("url_project"))
    tree = ET.parse("robot/testsuites/output.xml")
    root = tree.getroot()
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    for suite in root.findall(".//suite"):
        first_stat = suite.find(".//stat")
        if first_stat is not None:
            passed = int(first_stat.attrib.get("pass", 0))
            failed = int(first_stat.attrib.get("fail", 0))
            total_tests += passed + failed
            passed_tests += passed
            failed_tests += failed
    with open("robot/testsuites/output.xml", "r") as f:
        data = f.read()
    soup = BeautifulSoup(data, "xml")
    fail_msgs = soup.find_all("msg", {"level": "FAIL"})
    error_messages = [msg.get_text(strip=True) for msg in fail_msgs]
    if failed_tests == 0:
        icon = ":white_check_mark: Tests Passed"
    else:
        icon = ":crossbox: Tests Failed"
    RunID = str(os.environ.get("CI_JOB_ID"))
    Xray_ID = str(os.environ.get("XRAY_TEST_SUITE_ID"))
    temp_url = f"{url_project}{RunID}/artifacts/robot/testsuites/report.html"
    with open("test_summary.txt", "w") as file:
        file.write(f"*{icon} Test Report for Test Plan: {Xray_ID}* [<{temp_url}|Report>] \n")
        file.write("```-----------------------------\n")
        file.write(f"Total Tests: {total_tests}   \n")
        file.write(f"Passed Tests: {passed_tests} \n")
        file.write(f"Failed Tests: {failed_tests} \n")
        file.write(" ----------------------------\n")
        file.write("Error Messages:              \n")
        file.write(" ----------------------------\n")
        for msg in error_messages:
            file.write(f"{msg}\n")
        file.write("```")


def send_slack_message():
    """
    Method uses a filepath as a payload to send a slack message to a particular channel
    """
    file_path = str(os.environ.get("file_path"))
    channel_id = str(os.environ.get("channel_id"))
    slackToken = str(os.environ.get("SLACK_BOT_TOKEN"))

    try:
        with open(sanitise_file_path(file_path), "r") as file:
            file_contents = file.read()
    except FileNotFoundError:
        print(f"The file at {file_path} was not found.")
        exit()
    client = WebClient(token=slackToken)
    try:
        client.chat_postMessage(channel=channel_id, text=file_contents)
        print("Message sent successfully!")
    except SlackApiError as e:
        assert e.response["ok"] is False
        assert e.response["error"]
        print(f"Got an error: {e.response['error']}")


if __name__ == "__main__":
    extract_test_run_data()
    send_slack_message()
