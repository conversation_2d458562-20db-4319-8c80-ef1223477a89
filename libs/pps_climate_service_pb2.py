# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pps_climate_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import seats_common_pb2 as seats__common__pb2
import climate_common_pb2 as climate__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19pps_climate_service.proto\x12\x13pps_climate_service\x1a\x12seats_common.proto\x1a\x14\x63limate_common.proto\"X\n\x15SetSeatClimateRequest\x12?\n\x11seat_climate_zone\x18\x01 \x03(\x0b\x32$.climate_common.SeatClimateZoneState\"\x82\x01\n\x16SetSeatClimateResponse\x12\x37\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x1f.seats_common.EnumSeatSelection\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"V\n\x1bGetSeatClimateStatusRequest\x12\x37\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x1f.seats_common.EnumSeatSelection\"\x90\x01\n\x1cGetSeatClimateStatusResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12?\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32$.climate_common.SeatClimateZoneState\"*\n(GetFragranceDispenserSystemStatusRequest\"\xae\x01\n)GetFragranceDispenserSystemStatusResponse\x12P\n\x15\x66rag_dispenser_status\x18\x01 \x01(\x0e\x32\x31.pps_climate_service.EnumFragranceDispenserStatus\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"v\n\"SetFragranceDispenserSystemRequest\x12P\n\x15\x66rag_dispenser_status\x18\x01 \x01(\x0e\x32\x31.pps_climate_service.EnumFragranceDispenserStatus\"V\n#SetFragranceDispenserSystemResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\")\n\'GetFragranceDispenserBoostStatusRequest\"\xab\x01\n(GetFragranceDispenserBoostStatusResponse\x12N\n\x14\x66rag_dispenser_boost\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceDispenserBoost\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"s\n!SetFragranceDispenserBoostRequest\x12N\n\x14\x66rag_dispenser_boost\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceDispenserBoost\"U\n\"SetFragranceDispenserBoostResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"-\n+GetFragranceDispenserCartridgeStatusRequest\"\xb7\x01\n,GetFragranceDispenserCartridgeStatusResponse\x12V\n\x18\x66rag_dispenser_cartridge\x18\x01 \x01(\x0e\x32\x34.pps_climate_service.EnumFragranceDispenserCartridge\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x7f\n%SetFragranceDispenserCartridgeRequest\x12V\n\x18\x66rag_dispenser_cartridge\x18\x01 \x01(\x0e\x32\x34.pps_climate_service.EnumFragranceDispenserCartridge\"Y\n&SetFragranceDispenserCartridgeResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"-\n+GetFragranceDispenserIntensityStatusRequest\"\xb7\x01\n,GetFragranceDispenserIntensityStatusResponse\x12V\n\x18\x66rag_dispenser_intensity\x18\x01 \x01(\x0e\x32\x34.pps_climate_service.EnumFragranceDispenserIntensity\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x7f\n%SetFragranceDispenserIntensityRequest\x12V\n\x18\x66rag_dispenser_intensity\x18\x01 \x01(\x0e\x32\x34.pps_climate_service.EnumFragranceDispenserIntensity\"Y\n&SetFragranceDispenserIntensityResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"(\n&GetFragranceDispenserTrayStatusRequest\"\xa8\x01\n\'GetFragranceDispenserTrayStatusResponse\x12L\n\x13\x66rag_dispenser_tray\x18\x01 \x01(\x0e\x32/.pps_climate_service.EnumFragranceDispenserTray\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"p\n SetFragranceDispenserTrayRequest\x12L\n\x13\x66rag_dispenser_tray\x18\x01 \x01(\x0e\x32/.pps_climate_service.EnumFragranceDispenserTray\"T\n!SetFragranceDispenserTrayResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"0\n.GetFragranceDispenserAvailabilityStatusRequest\"\xc0\x01\n/GetFragranceDispenserAvailabilityStatusResponse\x12\\\n\x1b\x66rag_dispenser_availability\x18\x01 \x01(\x0e\x32\x37.pps_climate_service.EnumFragranceDispenserAvailability\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"2\n0GetFragranceDispenserCartridge1InfoStatusRequest\"\x9c\x02\n1GetFragranceDispenserCartridge1InfoStatusResponse\x12Y\n\x1f\x66rag_dispenser_cartridge1_level\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceCartridgeLevel\x12[\n\x1e\x66rag_dispenser_cartridge1_type\x18\x02 \x01(\x0e\x32\x33.pps_climate_service.EnumFragranceCartridgeTypeInfo\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"2\n0GetFragranceDispenserCartridge2InfoStatusRequest\"\x9c\x02\n1GetFragranceDispenserCartridge2InfoStatusResponse\x12Y\n\x1f\x66rag_dispenser_cartridge2_level\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceCartridgeLevel\x12[\n\x1e\x66rag_dispenser_cartridge2_type\x18\x02 \x01(\x0e\x32\x33.pps_climate_service.EnumFragranceCartridgeTypeInfo\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"2\n0GetFragranceDispenserCartridge3InfoStatusRequest\"\x9c\x02\n1GetFragranceDispenserCartridge3InfoStatusResponse\x12Y\n\x1f\x66rag_dispenser_cartridge3_level\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceCartridgeLevel\x12[\n\x1e\x66rag_dispenser_cartridge3_type\x18\x02 \x01(\x0e\x32\x33.pps_climate_service.EnumFragranceCartridgeTypeInfo\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x8e\x01\n\x16SetFridgeSystemRequest\x12:\n\x0c\x66ridge_state\x18\x01 \x01(\x0e\x32$.pps_climate_service.EnumFridgeState\x12\x38\n\x0b\x66ridge_mode\x18\x02 \x01(\x0e\x32#.pps_climate_service.EnumFridgeMode\"J\n\x17SetFridgeSystemResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x1e\n\x1cGetFridgeSystemStatusRequest\"\xc6\x01\n\x1dGetFridgeSystemStatusResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12:\n\x0c\x66ridge_state\x18\x02 \x01(\x0e\x32$.pps_climate_service.EnumFridgeState\x12\x38\n\x0b\x66ridge_mode\x18\x03 \x01(\x0e\x32#.pps_climate_service.EnumFridgeMode\"u\n\x1eSetVehicleCabinAirCleanRequest\x12S\n\x1a\x63\x61\x62in_air_cleaning_request\x18\x01 \x01(\x0e\x32/.pps_climate_service.EnumCabinAirCleanOperation\"R\n\x1fSetVehicleCabinAirCleanResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"%\n#GetVehicleCabinAirCleanStateRequest\"\xd8\x04\n$GetVehicleCabinAirCleanStateResponse\x12O\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32,.pps_climate_service.EnumCabinAirCleanStatus\x12\x19\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\r\x12J\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12\x1c\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\r\x12M\n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12/\n\x06status\x18\x06 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12(\n cabin_air_clean_cycles_remaining\x18\x07 \x01(\r\x12W\n\x1erow1_cabin_ionizer_error_state\x18\x08 \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\x12W\n\x1erow2_cabin_ionizer_error_state\x18\t \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\"\x96\x01\n\x1dSetVehiclePreconditionRequest\x12L\n\x14precondition_request\x18\x01 \x01(\x0e\x32..pps_climate_service.EnumPreconditionOperation\x12\'\n\x1fprecondition_target_temperature\x18\x02 \x01(\x02\"Q\n\x1eSetVehiclePreconditionResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"$\n\"GetVehiclePreconditionStateRequest\"\xa8\x03\n#GetVehiclePreconditionStateResponse\x12K\n\x11precondition_mode\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumPreConditionCurrentMode\x12H\n\x13precondition_status\x18\x02 \x01(\x0e\x32+.pps_climate_service.EnumPreConditionStatus\x12\x13\n\x0bprecon_time\x18\x03 \x01(\r\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12I\n\x1aprecondition_control_state\x18\x05 \x01(\x0e\x32%.pps_climate_service.EnumControlState\x12Y\n\x1cprecondition_indicator_state\x18\x06 \x01(\x0e\x32\x33.pps_climate_service.EnumPreconditionIndicatorState\"%\n#GetPM25AirQualityIndexStatusRequest\"\xad\x02\n$GetPM25AirQualityIndexStatusResponse\x12K\n\x11\x65xternal_aqi_band\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12\x1c\n\x14pm25_external_status\x18\x02 \x01(\r\x12K\n\x11internal_aqi_band\x18\x03 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12\x1c\n\x14pm25_internal_status\x18\x04 \x01(\r\x12/\n\x06status\x18\x05 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x1e\n\x1cGetCabinIoniserStatusRequest\"\xcb\x02\n\x1dGetCabinIoniserStatusResponse\x12G\n\x13\x63\x61\x62in_ionizer_state\x18\x01 \x01(\x0e\x32*.pps_climate_service.EnumCabinIonizerState\x12W\n\x1erow1_cabin_ionizer_error_state\x18\x02 \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\x12W\n\x1erow2_cabin_ionizer_error_state\x18\x03 \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"a\n\x16SetCabinIoniserRequest\x12G\n\x13\x63\x61\x62in_ionizer_state\x18\x01 \x01(\x0e\x32*.pps_climate_service.EnumCabinIonizerState\"J\n\x17SetCabinIoniserResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"g\n\x1dSetHeatedSteeringWheelRequest\x12\x46\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32\'.climate_common.EnumHSWTemperatureLevel\"Q\n\x1eSetHeatedSteeringWheelResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"%\n#GetHeatedSteeringWheelStatusRequest\"\xe1\x01\n$GetHeatedSteeringWheelStatusResponse\x12\x46\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32\'.climate_common.EnumHSWTemperatureLevel\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12@\n\x11hsw_control_state\x18\x03 \x01(\x0e\x32%.pps_climate_service.EnumControlState\"\x8b\x01\n\x17NotifySeatClimateStatus\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12?\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32$.climate_common.SeatClimateZoneState\"\xa9\x01\n$NotifyFragranceDispenserSystemStatus\x12P\n\x15\x66rag_dispenser_status\x18\x01 \x01(\x0e\x32\x31.pps_climate_service.EnumFragranceDispenserStatus\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xa6\x01\n#NotifyFragranceDispenserBoostStatus\x12N\n\x14\x66rag_dispenser_boost\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceDispenserBoost\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xb2\x01\n\'NotifyFragranceDispenserCartridgeStatus\x12V\n\x18\x66rag_dispenser_cartridge\x18\x01 \x01(\x0e\x32\x34.pps_climate_service.EnumFragranceDispenserCartridge\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xb2\x01\n\'NotifyFragranceDispenserIntensityStatus\x12V\n\x18\x66rag_dispenser_intensity\x18\x01 \x01(\x0e\x32\x34.pps_climate_service.EnumFragranceDispenserIntensity\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xa3\x01\n\"NotifyFragranceDispenserTrayStatus\x12L\n\x13\x66rag_dispenser_tray\x18\x01 \x01(\x0e\x32/.pps_climate_service.EnumFragranceDispenserTray\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xbb\x01\n*NotifyFragranceDispenserAvailabilityStatus\x12\\\n\x1b\x66rag_dispenser_availability\x18\x01 \x01(\x0e\x32\x37.pps_climate_service.EnumFragranceDispenserAvailability\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x97\x02\n,NotifyFragranceDispenserCartridge1InfoStatus\x12Y\n\x1f\x66rag_dispenser_cartridge1_level\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceCartridgeLevel\x12[\n\x1e\x66rag_dispenser_cartridge1_type\x18\x02 \x01(\x0e\x32\x33.pps_climate_service.EnumFragranceCartridgeTypeInfo\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x97\x02\n,NotifyFragranceDispenserCartridge2InfoStatus\x12Y\n\x1f\x66rag_dispenser_cartridge2_level\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceCartridgeLevel\x12[\n\x1e\x66rag_dispenser_cartridge2_type\x18\x02 \x01(\x0e\x32\x33.pps_climate_service.EnumFragranceCartridgeTypeInfo\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\x97\x02\n,NotifyFragranceDispenserCartridge3InfoStatus\x12Y\n\x1f\x66rag_dispenser_cartridge3_level\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumFragranceCartridgeLevel\x12[\n\x1e\x66rag_dispenser_cartridge3_type\x18\x02 \x01(\x0e\x32\x33.pps_climate_service.EnumFragranceCartridgeTypeInfo\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xc1\x01\n\x18NotifyFridgeSystemStatus\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12:\n\x0c\x66ridge_state\x18\x02 \x01(\x0e\x32$.pps_climate_service.EnumFridgeState\x12\x38\n\x0b\x66ridge_mode\x18\x03 \x01(\x0e\x32#.pps_climate_service.EnumFridgeMode\"\xd3\x04\n\x1fNotifyVehicleCabinAirCleanState\x12O\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32,.pps_climate_service.EnumCabinAirCleanStatus\x12\x19\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\r\x12J\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12\x1c\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\r\x12M\n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12/\n\x06status\x18\x06 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12(\n cabin_air_clean_cycles_remaining\x18\x07 \x01(\r\x12W\n\x1erow1_cabin_ionizer_error_state\x18\x08 \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\x12W\n\x1erow2_cabin_ionizer_error_state\x18\t \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\"\xa3\x03\n\x1eNotifyVehiclePreconditionState\x12K\n\x11precondition_mode\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumPreConditionCurrentMode\x12H\n\x13precondition_status\x18\x02 \x01(\x0e\x32+.pps_climate_service.EnumPreConditionStatus\x12\x13\n\x0bprecon_time\x18\x03 \x01(\r\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12I\n\x1aprecondition_control_state\x18\x05 \x01(\x0e\x32%.pps_climate_service.EnumControlState\x12Y\n\x1cprecondition_indicator_state\x18\x06 \x01(\x0e\x32\x33.pps_climate_service.EnumPreconditionIndicatorState\"\xa8\x02\n\x1fNotifyPM25AirQualityIndexStatus\x12K\n\x11\x65xternal_aqi_band\x18\x01 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12\x1c\n\x14pm25_external_status\x18\x02 \x01(\r\x12K\n\x11internal_aqi_band\x18\x03 \x01(\x0e\x32\x30.pps_climate_service.EnumPM25AirQualityIndexBand\x12\x1c\n\x14pm25_internal_status\x18\x04 \x01(\r\x12/\n\x06status\x18\x05 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xc6\x02\n\x18NotifyCabinIoniserStatus\x12G\n\x13\x63\x61\x62in_ionizer_state\x18\x01 \x01(\x0e\x32*.pps_climate_service.EnumCabinIonizerState\x12W\n\x1erow1_cabin_ionizer_error_state\x18\x02 \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\x12W\n\x1erow2_cabin_ionizer_error_state\x18\x03 \x01(\x0e\x32/.pps_climate_service.EnumCabinIonizerErrorState\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\"\xdc\x01\n\x1fNotifyHeatedSteeringWheelStatus\x12\x46\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32\'.climate_common.EnumHSWTemperatureLevel\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.pps_climate_service.EnumStatus\x12@\n\x11hsw_control_state\x18\x03 \x01(\x0e\x32%.pps_climate_service.EnumControlState*\x98\x03\n\nEnumStatus\x12\x1b\n\x17\x45NUM_STATUS_UNSPECIFIED\x10\x00\x12\x12\n\x0e\x45NUM_STATUS_OK\x10\x01\x12\x1d\n\x19\x45NUM_STATUS_DATA_DEGRADED\x10\x02\x12\x1f\n\x1b\x45NUM_STATUS_DATA_UNRELIABLE\x10\x03\x12 \n\x1c\x45NUM_STATUS_DATA_UNAVAILABLE\x10\x04\x12+\n\'ENUM_STATUS_ERROR_INVALID_SERVICE_STATE\x10\x05\x12+\n\'ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE\x10\x06\x12/\n+ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION\x10\x07\x12)\n%ENUM_STATUS_ERROR_MISSING_INPUT_FIELD\x10\x08\x12)\n%ENUM_STATUS_ERROR_INVALID_INPUT_FIELD\x10\t\x12\x16\n\x12\x45NUM_STATUS_NOT_OK\x10\n*\xa0\x01\n\x1c\x45numFragranceDispenserStatus\x12/\n+ENUM_FRAGRANCE_DISPENSER_STATUS_UNSPECIFIED\x10\x00\x12\'\n#ENUM_FRAGRANCE_DISPENSER_STATUS_OFF\x10\x01\x12&\n\"ENUM_FRAGRANCE_DISPENSER_STATUS_ON\x10\x02*\xcc\x01\n\x1b\x45numFragranceDispenserBoost\x12.\n*ENUM_FRAGRANCE_DISPENSER_BOOST_UNSPECIFIED\x10\x00\x12&\n\"ENUM_FRAGRANCE_DISPENSER_BOOST_OFF\x10\x01\x12%\n!ENUM_FRAGRANCE_DISPENSER_BOOST_ON\x10\x02\x12.\n*ENUM_FRAGRANCE_DISPENSER_BOOST_UNAVAILABLE\x10\x03*\x84\x02\n\x1f\x45numFragranceDispenserCartridge\x12\x32\n.ENUM_FRAGRANCE_DISPENSER_CARTRIDGE_UNSPECIFIED\x10\x00\x12\'\n#ENUM_FRAGRANCE_DISPENSER_CARTRIDGE1\x10\x01\x12\'\n#ENUM_FRAGRANCE_DISPENSER_CARTRIDGE2\x10\x02\x12\'\n#ENUM_FRAGRANCE_DISPENSER_CARTRIDGE3\x10\x03\x12\x32\n.ENUM_FRAGRANCE_DISPENSER_CARTRIDGE_UNAVAILABLE\x10\x04*\xdd\x01\n\x1f\x45numFragranceDispenserIntensity\x12\x32\n.ENUM_FRAGRANCE_DISPENSER_INTENSITY_UNSPECIFIED\x10\x00\x12*\n&ENUM_FRAGRANCE_DISPENSER_INTENSITY_LOW\x10\x01\x12-\n)ENUM_FRAGRANCE_DISPENSER_INTENSITY_MEDIUM\x10\x02\x12+\n\'ENUM_FRAGRANCE_DISPENSER_INTENSITY_HIGH\x10\x03*\xcb\x01\n\x1a\x45numFragranceDispenserTray\x12-\n)ENUM_FRAGRANCE_DISPENSER_TRAY_UNSPECIFIED\x10\x00\x12\'\n#ENUM_FRAGRANCE_DISPENSER_TRAY_CLOSE\x10\x01\x12&\n\"ENUM_FRAGRANCE_DISPENSER_TRAY_OPEN\x10\x02\x12-\n)ENUM_FRAGRANCE_DISPENSER_TRAY_UNAVAILABLE\x10\x03*\xa2\x01\n\"EnumFragranceDispenserAvailability\x12(\n$ENUM_FRAGRANCE_DISPENSER_UNSPECIFIED\x10\x00\x12&\n\"ENUM_FRAGRANCE_DISPENSER_AVAILABLE\x10\x01\x12*\n&ENUM_FRAGRANCE_DISPENSER_NOT_AVAILABLE\x10\x02*\xfb\x01\n\x1b\x45numFragranceCartridgeLevel\x12.\n*ENUM_FRAGRANCE_CARTRIDGE_LEVEL_UNSPECIFIED\x10\x00\x12\'\n#ENUM_FRAGRANCE_CARTRIDGE_LEVEL_HIGH\x10\x01\x12)\n%ENUM_FRAGRANCE_CARTRIDGE_LEVEL_MEDIUM\x10\x02\x12&\n\"ENUM_FRAGRANCE_CARTRIDGE_LEVEL_LOW\x10\x03\x12\x30\n,ENUM_FRAGRANCE_CARTRIDGE_LEVEL_NOT_AVAILABLE\x10\x04*\xeb\t\n\x1e\x45numFragranceCartridgeTypeInfo\x12(\n$ENUM_FRAGRANCE_CARTRIDGE_UNSPECIFIED\x10\x00\x12)\n%ENUM_FRAGRANCE_CARTRIDGE_NOT_DETECTED\x10\x01\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE1\x10\x02\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE2\x10\x03\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE3\x10\x04\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE4\x10\x05\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE5\x10\x06\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE6\x10\x07\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE7\x10\x08\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE8\x10\t\x12\"\n\x1e\x45NUM_FRAGRANCE_CARTRIDGE_TYPE9\x10\n\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE10\x10\x0b\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE11\x10\x0c\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE12\x10\r\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE13\x10\x0e\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE14\x10\x0f\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE15\x10\x10\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE16\x10\x11\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE17\x10\x12\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE18\x10\x13\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE19\x10\x14\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE20\x10\x15\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE21\x10\x16\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE22\x10\x17\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE23\x10\x18\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE24\x10\x19\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE25\x10\x1a\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE26\x10\x1b\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE27\x10\x1c\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE28\x10\x1d\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE29\x10\x1e\x12#\n\x1f\x45NUM_FRAGRANCE_CARTRIDGE_TYPE30\x10\x1f\x12\'\n#ENUM_FRAGRANCE_CARTRIDGE_READ_ERROR\x10 *i\n\x0f\x45numFridgeState\x12!\n\x1d\x45NUM_FRIDGE_STATE_UNSPECIFIED\x10\x00\x12\x18\n\x14\x45NUM_FRIDGE_STATE_ON\x10\x01\x12\x19\n\x15\x45NUM_FRIDGE_STATE_OFF\x10\x02*\x85\x01\n\x0e\x45numFridgeMode\x12 \n\x1c\x45NUM_FRIDGE_MODE_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x45NUM_FRIDGE_MODE_FREEZE\x10\x01\x12\x19\n\x15\x45NUM_FRIDGE_MODE_COOL\x10\x02\x12\x19\n\x15\x45NUM_FRIDGE_MODE_HEAT\x10\x03*\x86\x02\n\x1a\x45numCabinAirCleanOperation\x12.\n*ENUM_CABIN_AIR_CLEAN_OPERATION_UNSPECIFIED\x10\x00\x12(\n$ENUM_CABIN_AIR_CLEAN_OPERATION_START\x10\x01\x12\'\n#ENUM_CABIN_AIR_CLEAN_OPERATION_STOP\x10\x02\x12\x32\n.ENUM_CABIN_AIR_CLEAN_OPERATION_SCHEDULED_START\x10\x03\x12\x31\n-ENUM_CABIN_AIR_CLEAN_OPERATION_SCHEDULED_STOP\x10\x04*\xf7\x05\n\x17\x45numCabinAirCleanStatus\x12(\n$ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED\x10\x00\x12%\n!ENUM_CABIN_AIR_CLEAN_STS_INACTIVE\x10\x01\x12%\n!ENUM_CABIN_AIR_CLEAN_STS_COMPLETE\x10\x02\x12(\n$ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS\x10\x03\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED\x10\x04\x12,\n(ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY\x10\x05\x12\x33\n/ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE\x10\x06\x12\x30\n,ENUM_CABIN_AIR_CLEAN_STS_ERR_CYCLE_EXHAUSTED\x10\x07\x12-\n)ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT\x10\x08\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_ERR_VEH_POWER_TRANSITION\x10\t\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE\x10\n\x12/\n+ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED\x10\x0b\x12\x31\n-ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE\x10\x0c\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERV_ACTIVE\x10\r\x12\x36\n2ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS\x10\x0e*\xe5\x02\n\x19\x45numPreconditionOperation\x12+\n\'ENUM_PRECONDITION_OPERATION_UNSPECIFIED\x10\x00\x12.\n*ENUM_PRECONDITION_OPERATION_OFFBOARD_START\x10\x01\x12-\n)ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP\x10\x02\x12-\n)ENUM_PRECONDITION_OPERATION_ONBOARD_START\x10\x03\x12,\n(ENUM_PRECONDITION_OPERATION_ONBOARD_STOP\x10\x04\x12/\n+ENUM_PRECONDITION_OPERATION_SCHEDULED_START\x10\x05\x12.\n*ENUM_PRECONDITION_OPERATION_SCHEDULED_STOP\x10\x06*\xd1\x01\n\x1b\x45numPreConditionCurrentMode\x12.\n*ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED\x10\x00\x12+\n\'ENUM_PRECONDITION_CURRENT_MODE_INACTIVE\x10\x01\x12+\n\'ENUM_PRECONDITION_CURRENT_MODE_ONDEMAND\x10\x02\x12(\n$ENUM_PRECONDITION_CURRENT_MODE_TIMED\x10\x03*\xd0\x02\n\x16\x45numPreConditionStatus\x12%\n!ENUM_PRECONDITION_STS_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_PRECONDITION_STS_OFF\x10\x01\x12\"\n\x1e\x45NUM_PRECONDITION_STS_START_UP\x10\x02\x12%\n!ENUM_PRECONDITION_STS_IN_PROGRESS\x10\x03\x12\"\n\x1e\x45NUM_PRECONDITION_STS_COMPLETE\x10\x04\x12*\n&ENUM_PRECONDITION_STS_PARTIAL_COMPLETE\x10\x05\x12)\n%ENUM_PRECONDITION_STS_ERR_LOW_BATTERY\x10\x06\x12*\n&ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT\x10\x07*z\n\x10\x45numControlState\x12\"\n\x1e\x45NUM_CONTROL_STATE_UNSPECIFIED\x10\x00\x12 \n\x1c\x45NUM_CONTROL_STATE_INHIBITED\x10\x01\x12 \n\x1c\x45NUM_CONTROL_STATE_AVAILABLE\x10\x02*\x86\x03\n\x1b\x45numPM25AirQualityIndexBand\x12+\n\'ENUM_AIR_QUALITY_INDEX_BAND_UNSPECIFIED\x10\x00\x12)\n%ENUM_AIR_QUALITY_INDEX_BAND_UNDEFINED\x10\x01\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_BAND1\x10\x02\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_BAND2\x10\x03\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_BAND3\x10\x04\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_BAND4\x10\x05\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_BAND5\x10\x06\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_BAND6\x10\x07\x12%\n!ENUM_AIR_QUALITY_INDEX_BAND_ERROR\x10\x08*\x8c\x01\n\x15\x45numCabinIonizerState\x12(\n$ENUM_CABIN_IONIZER_STATE_UNSPECIFIED\x10\x00\x12$\n ENUM_CABIN_IONIZER_STATE_DISABLE\x10\x01\x12#\n\x1f\x45NUM_CABIN_IONIZER_STATE_ENABLE\x10\x02*\x9b\x01\n\x1a\x45numCabinIonizerErrorState\x12.\n*ENUM_CABIN_IONIZER_ERROR_STATE_UNSPECIFIED\x10\x00\x12%\n!ENUM_CABIN_IONIZER_ERROR_STATE_NO\x10\x01\x12&\n\"ENUM_CABIN_IONIZER_ERROR_STATE_YES\x10\x02*\xe1\x01\n\x1e\x45numPreconditionIndicatorState\x12+\n\'ENUM_PRECON_INDICATOR_STATE_UNSPECIFIED\x10\x00\x12(\n$ENUM_PRECON_INDICATOR_STATE_INACTIVE\x10\x01\x12\x34\n0ENUM_PRECON_INDICATOR_STATE_ACTIVE_CABIN_VENTING\x10\x02\x12\x32\n.ENUM_PRECON_INDICATOR_STATE_ACTIVE_WITHOUT_FOH\x10\x03\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'pps_climate_service_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ENUMSTATUS']._serialized_start=11435
  _globals['_ENUMSTATUS']._serialized_end=11843
  _globals['_ENUMFRAGRANCEDISPENSERSTATUS']._serialized_start=11846
  _globals['_ENUMFRAGRANCEDISPENSERSTATUS']._serialized_end=12006
  _globals['_ENUMFRAGRANCEDISPENSERBOOST']._serialized_start=12009
  _globals['_ENUMFRAGRANCEDISPENSERBOOST']._serialized_end=12213
  _globals['_ENUMFRAGRANCEDISPENSERCARTRIDGE']._serialized_start=12216
  _globals['_ENUMFRAGRANCEDISPENSERCARTRIDGE']._serialized_end=12476
  _globals['_ENUMFRAGRANCEDISPENSERINTENSITY']._serialized_start=12479
  _globals['_ENUMFRAGRANCEDISPENSERINTENSITY']._serialized_end=12700
  _globals['_ENUMFRAGRANCEDISPENSERTRAY']._serialized_start=12703
  _globals['_ENUMFRAGRANCEDISPENSERTRAY']._serialized_end=12906
  _globals['_ENUMFRAGRANCEDISPENSERAVAILABILITY']._serialized_start=12909
  _globals['_ENUMFRAGRANCEDISPENSERAVAILABILITY']._serialized_end=13071
  _globals['_ENUMFRAGRANCECARTRIDGELEVEL']._serialized_start=13074
  _globals['_ENUMFRAGRANCECARTRIDGELEVEL']._serialized_end=13325
  _globals['_ENUMFRAGRANCECARTRIDGETYPEINFO']._serialized_start=13328
  _globals['_ENUMFRAGRANCECARTRIDGETYPEINFO']._serialized_end=14587
  _globals['_ENUMFRIDGESTATE']._serialized_start=14589
  _globals['_ENUMFRIDGESTATE']._serialized_end=14694
  _globals['_ENUMFRIDGEMODE']._serialized_start=14697
  _globals['_ENUMFRIDGEMODE']._serialized_end=14830
  _globals['_ENUMCABINAIRCLEANOPERATION']._serialized_start=14833
  _globals['_ENUMCABINAIRCLEANOPERATION']._serialized_end=15095
  _globals['_ENUMCABINAIRCLEANSTATUS']._serialized_start=15098
  _globals['_ENUMCABINAIRCLEANSTATUS']._serialized_end=15857
  _globals['_ENUMPRECONDITIONOPERATION']._serialized_start=15860
  _globals['_ENUMPRECONDITIONOPERATION']._serialized_end=16217
  _globals['_ENUMPRECONDITIONCURRENTMODE']._serialized_start=16220
  _globals['_ENUMPRECONDITIONCURRENTMODE']._serialized_end=16429
  _globals['_ENUMPRECONDITIONSTATUS']._serialized_start=16432
  _globals['_ENUMPRECONDITIONSTATUS']._serialized_end=16768
  _globals['_ENUMCONTROLSTATE']._serialized_start=16770
  _globals['_ENUMCONTROLSTATE']._serialized_end=16892
  _globals['_ENUMPM25AIRQUALITYINDEXBAND']._serialized_start=16895
  _globals['_ENUMPM25AIRQUALITYINDEXBAND']._serialized_end=17285
  _globals['_ENUMCABINIONIZERSTATE']._serialized_start=17288
  _globals['_ENUMCABINIONIZERSTATE']._serialized_end=17428
  _globals['_ENUMCABINIONIZERERRORSTATE']._serialized_start=17431
  _globals['_ENUMCABINIONIZERERRORSTATE']._serialized_end=17586
  _globals['_ENUMPRECONDITIONINDICATORSTATE']._serialized_start=17589
  _globals['_ENUMPRECONDITIONINDICATORSTATE']._serialized_end=17814
  _globals['_SETSEATCLIMATEREQUEST']._serialized_start=92
  _globals['_SETSEATCLIMATEREQUEST']._serialized_end=180
  _globals['_SETSEATCLIMATERESPONSE']._serialized_start=183
  _globals['_SETSEATCLIMATERESPONSE']._serialized_end=313
  _globals['_GETSEATCLIMATESTATUSREQUEST']._serialized_start=315
  _globals['_GETSEATCLIMATESTATUSREQUEST']._serialized_end=401
  _globals['_GETSEATCLIMATESTATUSRESPONSE']._serialized_start=404
  _globals['_GETSEATCLIMATESTATUSRESPONSE']._serialized_end=548
  _globals['_GETFRAGRANCEDISPENSERSYSTEMSTATUSREQUEST']._serialized_start=550
  _globals['_GETFRAGRANCEDISPENSERSYSTEMSTATUSREQUEST']._serialized_end=592
  _globals['_GETFRAGRANCEDISPENSERSYSTEMSTATUSRESPONSE']._serialized_start=595
  _globals['_GETFRAGRANCEDISPENSERSYSTEMSTATUSRESPONSE']._serialized_end=769
  _globals['_SETFRAGRANCEDISPENSERSYSTEMREQUEST']._serialized_start=771
  _globals['_SETFRAGRANCEDISPENSERSYSTEMREQUEST']._serialized_end=889
  _globals['_SETFRAGRANCEDISPENSERSYSTEMRESPONSE']._serialized_start=891
  _globals['_SETFRAGRANCEDISPENSERSYSTEMRESPONSE']._serialized_end=977
  _globals['_GETFRAGRANCEDISPENSERBOOSTSTATUSREQUEST']._serialized_start=979
  _globals['_GETFRAGRANCEDISPENSERBOOSTSTATUSREQUEST']._serialized_end=1020
  _globals['_GETFRAGRANCEDISPENSERBOOSTSTATUSRESPONSE']._serialized_start=1023
  _globals['_GETFRAGRANCEDISPENSERBOOSTSTATUSRESPONSE']._serialized_end=1194
  _globals['_SETFRAGRANCEDISPENSERBOOSTREQUEST']._serialized_start=1196
  _globals['_SETFRAGRANCEDISPENSERBOOSTREQUEST']._serialized_end=1311
  _globals['_SETFRAGRANCEDISPENSERBOOSTRESPONSE']._serialized_start=1313
  _globals['_SETFRAGRANCEDISPENSERBOOSTRESPONSE']._serialized_end=1398
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGESTATUSREQUEST']._serialized_start=1400
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGESTATUSREQUEST']._serialized_end=1445
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGESTATUSRESPONSE']._serialized_start=1448
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGESTATUSRESPONSE']._serialized_end=1631
  _globals['_SETFRAGRANCEDISPENSERCARTRIDGEREQUEST']._serialized_start=1633
  _globals['_SETFRAGRANCEDISPENSERCARTRIDGEREQUEST']._serialized_end=1760
  _globals['_SETFRAGRANCEDISPENSERCARTRIDGERESPONSE']._serialized_start=1762
  _globals['_SETFRAGRANCEDISPENSERCARTRIDGERESPONSE']._serialized_end=1851
  _globals['_GETFRAGRANCEDISPENSERINTENSITYSTATUSREQUEST']._serialized_start=1853
  _globals['_GETFRAGRANCEDISPENSERINTENSITYSTATUSREQUEST']._serialized_end=1898
  _globals['_GETFRAGRANCEDISPENSERINTENSITYSTATUSRESPONSE']._serialized_start=1901
  _globals['_GETFRAGRANCEDISPENSERINTENSITYSTATUSRESPONSE']._serialized_end=2084
  _globals['_SETFRAGRANCEDISPENSERINTENSITYREQUEST']._serialized_start=2086
  _globals['_SETFRAGRANCEDISPENSERINTENSITYREQUEST']._serialized_end=2213
  _globals['_SETFRAGRANCEDISPENSERINTENSITYRESPONSE']._serialized_start=2215
  _globals['_SETFRAGRANCEDISPENSERINTENSITYRESPONSE']._serialized_end=2304
  _globals['_GETFRAGRANCEDISPENSERTRAYSTATUSREQUEST']._serialized_start=2306
  _globals['_GETFRAGRANCEDISPENSERTRAYSTATUSREQUEST']._serialized_end=2346
  _globals['_GETFRAGRANCEDISPENSERTRAYSTATUSRESPONSE']._serialized_start=2349
  _globals['_GETFRAGRANCEDISPENSERTRAYSTATUSRESPONSE']._serialized_end=2517
  _globals['_SETFRAGRANCEDISPENSERTRAYREQUEST']._serialized_start=2519
  _globals['_SETFRAGRANCEDISPENSERTRAYREQUEST']._serialized_end=2631
  _globals['_SETFRAGRANCEDISPENSERTRAYRESPONSE']._serialized_start=2633
  _globals['_SETFRAGRANCEDISPENSERTRAYRESPONSE']._serialized_end=2717
  _globals['_GETFRAGRANCEDISPENSERAVAILABILITYSTATUSREQUEST']._serialized_start=2719
  _globals['_GETFRAGRANCEDISPENSERAVAILABILITYSTATUSREQUEST']._serialized_end=2767
  _globals['_GETFRAGRANCEDISPENSERAVAILABILITYSTATUSRESPONSE']._serialized_start=2770
  _globals['_GETFRAGRANCEDISPENSERAVAILABILITYSTATUSRESPONSE']._serialized_end=2962
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE1INFOSTATUSREQUEST']._serialized_start=2964
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE1INFOSTATUSREQUEST']._serialized_end=3014
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE1INFOSTATUSRESPONSE']._serialized_start=3017
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE1INFOSTATUSRESPONSE']._serialized_end=3301
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE2INFOSTATUSREQUEST']._serialized_start=3303
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE2INFOSTATUSREQUEST']._serialized_end=3353
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE2INFOSTATUSRESPONSE']._serialized_start=3356
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE2INFOSTATUSRESPONSE']._serialized_end=3640
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE3INFOSTATUSREQUEST']._serialized_start=3642
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE3INFOSTATUSREQUEST']._serialized_end=3692
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE3INFOSTATUSRESPONSE']._serialized_start=3695
  _globals['_GETFRAGRANCEDISPENSERCARTRIDGE3INFOSTATUSRESPONSE']._serialized_end=3979
  _globals['_SETFRIDGESYSTEMREQUEST']._serialized_start=3982
  _globals['_SETFRIDGESYSTEMREQUEST']._serialized_end=4124
  _globals['_SETFRIDGESYSTEMRESPONSE']._serialized_start=4126
  _globals['_SETFRIDGESYSTEMRESPONSE']._serialized_end=4200
  _globals['_GETFRIDGESYSTEMSTATUSREQUEST']._serialized_start=4202
  _globals['_GETFRIDGESYSTEMSTATUSREQUEST']._serialized_end=4232
  _globals['_GETFRIDGESYSTEMSTATUSRESPONSE']._serialized_start=4235
  _globals['_GETFRIDGESYSTEMSTATUSRESPONSE']._serialized_end=4433
  _globals['_SETVEHICLECABINAIRCLEANREQUEST']._serialized_start=4435
  _globals['_SETVEHICLECABINAIRCLEANREQUEST']._serialized_end=4552
  _globals['_SETVEHICLECABINAIRCLEANRESPONSE']._serialized_start=4554
  _globals['_SETVEHICLECABINAIRCLEANRESPONSE']._serialized_end=4636
  _globals['_GETVEHICLECABINAIRCLEANSTATEREQUEST']._serialized_start=4638
  _globals['_GETVEHICLECABINAIRCLEANSTATEREQUEST']._serialized_end=4675
  _globals['_GETVEHICLECABINAIRCLEANSTATERESPONSE']._serialized_start=4678
  _globals['_GETVEHICLECABINAIRCLEANSTATERESPONSE']._serialized_end=5278
  _globals['_SETVEHICLEPRECONDITIONREQUEST']._serialized_start=5281
  _globals['_SETVEHICLEPRECONDITIONREQUEST']._serialized_end=5431
  _globals['_SETVEHICLEPRECONDITIONRESPONSE']._serialized_start=5433
  _globals['_SETVEHICLEPRECONDITIONRESPONSE']._serialized_end=5514
  _globals['_GETVEHICLEPRECONDITIONSTATEREQUEST']._serialized_start=5516
  _globals['_GETVEHICLEPRECONDITIONSTATEREQUEST']._serialized_end=5552
  _globals['_GETVEHICLEPRECONDITIONSTATERESPONSE']._serialized_start=5555
  _globals['_GETVEHICLEPRECONDITIONSTATERESPONSE']._serialized_end=5979
  _globals['_GETPM25AIRQUALITYINDEXSTATUSREQUEST']._serialized_start=5981
  _globals['_GETPM25AIRQUALITYINDEXSTATUSREQUEST']._serialized_end=6018
  _globals['_GETPM25AIRQUALITYINDEXSTATUSRESPONSE']._serialized_start=6021
  _globals['_GETPM25AIRQUALITYINDEXSTATUSRESPONSE']._serialized_end=6322
  _globals['_GETCABINIONISERSTATUSREQUEST']._serialized_start=6324
  _globals['_GETCABINIONISERSTATUSREQUEST']._serialized_end=6354
  _globals['_GETCABINIONISERSTATUSRESPONSE']._serialized_start=6357
  _globals['_GETCABINIONISERSTATUSRESPONSE']._serialized_end=6688
  _globals['_SETCABINIONISERREQUEST']._serialized_start=6690
  _globals['_SETCABINIONISERREQUEST']._serialized_end=6787
  _globals['_SETCABINIONISERRESPONSE']._serialized_start=6789
  _globals['_SETCABINIONISERRESPONSE']._serialized_end=6863
  _globals['_SETHEATEDSTEERINGWHEELREQUEST']._serialized_start=6865
  _globals['_SETHEATEDSTEERINGWHEELREQUEST']._serialized_end=6968
  _globals['_SETHEATEDSTEERINGWHEELRESPONSE']._serialized_start=6970
  _globals['_SETHEATEDSTEERINGWHEELRESPONSE']._serialized_end=7051
  _globals['_GETHEATEDSTEERINGWHEELSTATUSREQUEST']._serialized_start=7053
  _globals['_GETHEATEDSTEERINGWHEELSTATUSREQUEST']._serialized_end=7090
  _globals['_GETHEATEDSTEERINGWHEELSTATUSRESPONSE']._serialized_start=7093
  _globals['_GETHEATEDSTEERINGWHEELSTATUSRESPONSE']._serialized_end=7318
  _globals['_NOTIFYSEATCLIMATESTATUS']._serialized_start=7321
  _globals['_NOTIFYSEATCLIMATESTATUS']._serialized_end=7460
  _globals['_NOTIFYFRAGRANCEDISPENSERSYSTEMSTATUS']._serialized_start=7463
  _globals['_NOTIFYFRAGRANCEDISPENSERSYSTEMSTATUS']._serialized_end=7632
  _globals['_NOTIFYFRAGRANCEDISPENSERBOOSTSTATUS']._serialized_start=7635
  _globals['_NOTIFYFRAGRANCEDISPENSERBOOSTSTATUS']._serialized_end=7801
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGESTATUS']._serialized_start=7804
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGESTATUS']._serialized_end=7982
  _globals['_NOTIFYFRAGRANCEDISPENSERINTENSITYSTATUS']._serialized_start=7985
  _globals['_NOTIFYFRAGRANCEDISPENSERINTENSITYSTATUS']._serialized_end=8163
  _globals['_NOTIFYFRAGRANCEDISPENSERTRAYSTATUS']._serialized_start=8166
  _globals['_NOTIFYFRAGRANCEDISPENSERTRAYSTATUS']._serialized_end=8329
  _globals['_NOTIFYFRAGRANCEDISPENSERAVAILABILITYSTATUS']._serialized_start=8332
  _globals['_NOTIFYFRAGRANCEDISPENSERAVAILABILITYSTATUS']._serialized_end=8519
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGE1INFOSTATUS']._serialized_start=8522
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGE1INFOSTATUS']._serialized_end=8801
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGE2INFOSTATUS']._serialized_start=8804
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGE2INFOSTATUS']._serialized_end=9083
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGE3INFOSTATUS']._serialized_start=9086
  _globals['_NOTIFYFRAGRANCEDISPENSERCARTRIDGE3INFOSTATUS']._serialized_end=9365
  _globals['_NOTIFYFRIDGESYSTEMSTATUS']._serialized_start=9368
  _globals['_NOTIFYFRIDGESYSTEMSTATUS']._serialized_end=9561
  _globals['_NOTIFYVEHICLECABINAIRCLEANSTATE']._serialized_start=9564
  _globals['_NOTIFYVEHICLECABINAIRCLEANSTATE']._serialized_end=10159
  _globals['_NOTIFYVEHICLEPRECONDITIONSTATE']._serialized_start=10162
  _globals['_NOTIFYVEHICLEPRECONDITIONSTATE']._serialized_end=10581
  _globals['_NOTIFYPM25AIRQUALITYINDEXSTATUS']._serialized_start=10584
  _globals['_NOTIFYPM25AIRQUALITYINDEXSTATUS']._serialized_end=10880
  _globals['_NOTIFYCABINIONISERSTATUS']._serialized_start=10883
  _globals['_NOTIFYCABINIONISERSTATUS']._serialized_end=11209
  _globals['_NOTIFYHEATEDSTEERINGWHEELSTATUS']._serialized_start=11212
  _globals['_NOTIFYHEATEDSTEERINGWHEELSTATUS']._serialized_end=11432
# @@protoc_insertion_point(module_scope)
