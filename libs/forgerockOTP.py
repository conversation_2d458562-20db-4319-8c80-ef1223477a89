import time
import requests
import json
from retry import retry
from pyaml_env import parse_config, BaseConfig
import gmailOTP
import random
import string
from robot.api.deco import keyword


VERIFIER = "ZpJiIM_G0SE9WlxzS69Cq0mQh8uyFaeEbILlW8tHs62SmEE6n7Nke0XJGx_F4OduTI4"
CODE_CHALLENGE = "j3wKnK2Fa_mc2tgdqa6GtUfCYjdWSA5S23JKTTtPF8Y"
headers = {"Content-Type": "application/json"}


def id_generator(size=8, chars=string.ascii_lowercase):
    autogeneratedName = "".join(random.choice(chars) for _ in range(size))
    return autogeneratedName


systemVarFile = "../variables/system.yml"

systemVars = BaseConfig(parse_config(systemVarFile))

IAM_HOST = getattr(systemVars, "IAM_HOST")

url = "https://" + IAM_HOST + "/gateway/json/realms/root/realms/customer/authenticate"


@keyword("Create a forgerock user")
@retry(exceptions=Exception, tries=5, delay=3, backoff=1)
def createForgeRockUser(password, country):
    gmailOTP.clearDownGmailInbox()
    landingPageJsonResp = StartTheMainLandingTree()
    submitLocalAuthResponse = enterAndSubmitLocalAuthentication(landingPageJsonResp)
    selectRegistrationRouteResponse = selectRegistrationRoute(submitLocalAuthResponse)
    continueToOTPResponse = continueToOTP(selectRegistrationRouteResponse)
    time.sleep(10)
    OTP = gmailOTP.GetOneTimePasscode()
    print(OTP)
    enterAndSubmitOTPResponse = enterAndSubmitOTP(OTP, continueToOTPResponse)
    enterAndSubmitNameResponse, firstName, lastName = enterAndSubmitFirstAndLastName(enterAndSubmitOTPResponse)
    enterAndSubmitCountryResponse = enterAndSubmitRegistrationCountry(country, enterAndSubmitNameResponse)
    email = enterAndSubmitPassword(password, enterAndSubmitCountryResponse)
    return email, firstName, lastName


# 1. Landing page authId
def StartTheMainLandingTree():
    try:
        response = requests.request("POST", url=url, data=None, allow_redirects=True)
        landingPageJsonResp = json.loads(response.text)
        print("Stage 1 complete")
        return landingPageJsonResp
    except KeyError as e:
        print(e)
        print("Failed stage 1")


# 2.
def enterAndSubmitLocalAuthentication(landingPageResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        requestLocalAuth = json.dumps(landingPageResponse)
        requestLocalAuthJson = json.loads(requestLocalAuth)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        global email
        email = "guvov20+test" + id_generator() + "@gmail.com"

        requestLocalAuthJson["callbacks"][1]["input"][0]["value"] = email
        requestLocalAuthJson["callbacks"][0]["input"][0]["value"] = "localAuthentication"

        requestLocalAuthJson.update(requestLocalAuthJson)
        payload = json.dumps(requestLocalAuthJson)

        response = requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        submitLocalAuthResponse = json.loads(response.text)
        print("Stage 2 complete")
        return submitLocalAuthResponse
    except KeyError as e:
        print(e)
        print("Failed stage 2")


# 3.
def selectRegistrationRoute(submitLocalAuthResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests

        requestRegistrationRoute = json.dumps(submitLocalAuthResponse)
        requestRegistrationRouteJson = json.loads(requestRegistrationRoute)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        requestRegistrationRouteJson["callbacks"][0]["input"][0]["value"] = "1"

        requestRegistrationRouteJson.update(requestRegistrationRouteJson)
        payload = json.dumps(requestRegistrationRouteJson)

        response = requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        print("Stage 3 complete")
        selectRegistrationRouteResponse = json.loads(response.text)
        return selectRegistrationRouteResponse
    except KeyError as e:
        print(e)
        print("Failed stage 3")


# 4.
def continueToOTP(selectRegistrationRouteResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests

        requestContinueToOTP = json.dumps(selectRegistrationRouteResponse)
        requestContinueToOTPJson = json.loads(requestContinueToOTP)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        payload = json.dumps(requestContinueToOTPJson)

        response = requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        continueToOTPResponse = json.loads(response.text)
        print("Stage 4 complete")
        return continueToOTPResponse
    except KeyError as e:
        print(e)
        print("Failed stage 4")


# 5.
def enterAndSubmitOTP(OTP, continueToOTPResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        requestEnterAndSubmitOTP = json.dumps(continueToOTPResponse)
        requestEnterAndSubmitOTPJson = json.loads(requestEnterAndSubmitOTP)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        requestEnterAndSubmitOTPJson["callbacks"][0]["input"][0]["value"] = OTP

        requestEnterAndSubmitOTPJson.update(requestEnterAndSubmitOTPJson)
        payload = json.dumps(requestEnterAndSubmitOTPJson)

        response = requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        enterAndSubmitOTPResponse = json.loads(response.text)
        print("Stage 5 complete")
        return enterAndSubmitOTPResponse
    except KeyError as e:
        print(e)
        print("Failed stage 5")


# 6.
def enterAndSubmitFirstAndLastName(enterAndSubmitOTPResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        requestEnterAndSubmitName = json.dumps(enterAndSubmitOTPResponse)
        requestEnterAndSubmitNameJson = json.loads(requestEnterAndSubmitName)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        firstName = requestEnterAndSubmitNameJson["callbacks"][0]["input"][0]["value"] = id_generator()
        lastName = requestEnterAndSubmitNameJson["callbacks"][1]["input"][0]["value"] = id_generator()

        requestEnterAndSubmitNameJson.update(requestEnterAndSubmitNameJson)
        payload = json.dumps(requestEnterAndSubmitNameJson)

        response = requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        print("Stage 6 complete")
        enterAndSubmitNameResponse = json.loads(response.text)
        return enterAndSubmitNameResponse, firstName, lastName
    except KeyError as e:
        print(e)
        print("Failed stage 6")


# 7.
def enterAndSubmitRegistrationCountry(country, enterAndSubmitNameResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        requestEnterAndSubmitCountry = json.dumps(enterAndSubmitNameResponse)
        requestEnterAndSubmitCountryJson = json.loads(requestEnterAndSubmitCountry)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        requestEnterAndSubmitCountryJson["callbacks"][0]["input"][0]["value"] = country

        requestEnterAndSubmitCountryJson.update(requestEnterAndSubmitCountryJson)
        payload = json.dumps(requestEnterAndSubmitCountryJson)

        response = requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        enterAndSubmitCountryResponse = json.loads(response.text)
        print("Stage 7 complete")
        return enterAndSubmitCountryResponse
    except KeyError as e:
        print(e)
        print("Failed stage 7")


# 8.
def enterAndSubmitPassword(password, enterAndSubmitCountryResponse):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        requestEnterAndSubmitPassword = json.dumps(enterAndSubmitCountryResponse)
        requestEnterAndSubmitPasswordJson = json.loads(requestEnterAndSubmitPassword)

        headers = {
            "Content-Type": "application/json",
            "Cookie": "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01",
            "Accept-Api-Version": "protocol=1.0,resource=2.1",
        }

        requestEnterAndSubmitPasswordJson["callbacks"][0]["input"][0]["value"] = password

        requestEnterAndSubmitPasswordJson.update(requestEnterAndSubmitPasswordJson)
        payload = json.dumps(requestEnterAndSubmitPasswordJson)

        requests.request("POST", url, headers=headers, data=payload, allow_redirects=True)
        return email
        print("Stage 8 complete")
    except KeyError as e:
        print(e)
        print("Failed stage 8")
