import requests
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig
from datetime import datetime, timezone
import uuid
import json
import commonUtils


systemVarFile = "../variables/system.yml"
DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.000Z"


@not_keyword
def get_url(url):
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, url)


@keyword("Bind or unbind vin for permissions")
def bind_for_permission(clientID, clientPW, vin, action):

    VRoom_Token = commonUtils.generate_keycloak_token_CandP_Vroom(clientID, clientPW, "VRoom")
    VRoom_url = get_url("VRoom_bind_unbind_url")
    FRid = get_url("ForgeRock_e2e_id")
    correlation_id = str(uuid.uuid4())[3:]
    print("Correlation ID:", correlation_id)
    vinString = str(vin)
    print(type(vinString))
    print(vinString)
    vinStriped = vinString.strip("['']")

    if action == "bind":
        now = datetime.now(timezone.utc).strftime(DATETIME_FORMAT)
        url = VRoom_url + "binding/bind"
        payload = json.dumps({"vin": vinStriped, "userId": FRid, "userIdType": "FORGEROCK", "bindingType": "WC_PRIMARY_BIND", "exclusiveFrom": now})

    elif action == "unbind":
        now = datetime.now(timezone.utc).strftime(DATETIME_FORMAT)
        url = VRoom_url + "binding/unbind"
        payload = json.dumps({"vin": vinStriped, "userId": FRid, "userIdType": "FORGEROCK", "unbindingType": "WC_PRIMARY_UNBIND", "exclusiveTo": now})

    headers = {"Content-Type": "application/json", "Correlation-Id": correlation_id, "Authorization": VRoom_Token}
    # print(headers)
    print("Payload:", payload)
    print("Request url:", url)
    response = requests.request("POST", url, data=payload, headers=headers)
    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("permission update on vin")
def permission_payload_bind_unbind(clientID, clientPW, vin, give_permission, action):

    CandP_Token = commonUtils.generate_keycloak_token_CandP_Vroom(clientID, clientPW, "ConsentAndPermission")
    CandP_url = get_url("consent_permission_url")
    FRid = get_url("ForgeRock_e2e_id")
    correlation_id = str(uuid.uuid4())[3:]
    print("Correlation ID:", correlation_id)
    headers = {"Content-Type": "application/json", "Correlation-Id": correlation_id, "Authorization": CandP_Token}

    if action == "enable":
        url = CandP_url + "enable"
        print("Request url:", url)
    elif action == "revoke":
        url = CandP_url + "revoke"
        print("Request url:", url)

    if give_permission == "singlePerm":

        permissionLabel = "L_App_Privacy"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "DpretryPerm":

        permissionLabel = "Dp_retry_Perm"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "multiplePerm":

        permissionLabel = "L_App_Privacy"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        permissionLabel = "A_App_Usage"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "SmartCharging":

        permissionLabel = "L_3rdParty_SmartCharging"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "AAppUsage":

        permissionLabel = "A_App_Usage"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "geoTrip":

        permissionLabel = "A_JourneyLogging"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        permissionLabel = "C_R&D_Geo"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "allPerm":

        permissionLabel = "L_App_Privacy"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        permissionLabel = "A_App_Usage"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        permissionLabel = "L_3rdParty_SmartCharging"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    elif give_permission == "noPerm":

        permissionLabel = "L_App_Privacy"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        permissionLabel = "A_App_Usage"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        permissionLabel = "L_3rdParty_SmartCharging"
        response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        # permissionLabel = "A_JourneyLogging"
        # response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

        # permissionLabel = "C_R&D_Geo"
        # response = enable_revoke_request(url, headers, vin, FRid, permissionLabel)

    print("response:", response)
    return response


@not_keyword
def enable_revoke_request(url, headers, vin, FRid, permissionLabel):

    vinString = str(vin)
    print(type(vinString))
    print(vinString)
    vinStriped = vinString.strip("['']")

    payload = json.dumps({"userId": FRid, "userIdType": "FORGEROCK", "label": permissionLabel, "vin": vinStriped})
    print("payload:", payload)
    print("url:", url)
    retry_request = commonUtils.create_retry_request_session()
    response = retry_request.request("POST", url, data=payload, headers=headers)
    response.raise_for_status()

    print(response.status_code)
    print(response.text)
    return response.text, response.status_code


@keyword("vehicle association check")
def vehicle_association(clientID, clientPW, vin, action=""):

    print("user:", clientID)
    print("pw:", clientPW)
    VRoom_Token = commonUtils.generate_keycloak_token_CandP_Vroom(clientID, clientPW, "VRoom")
    VRoom_association_url = get_url("VRoom_vehicle_association_url")
    FRid = get_url("ForgeRock_e2e_id")
    correlation_id = str(uuid.uuid4())[3:]
    print("Correlation ID:", correlation_id)
    vinString = str(vin)
    print(type(vinString))
    print(vinString)
    vinStriped = vinString.strip("['']")
    url = VRoom_association_url + "vp-association/vehicle/" + vinStriped
    headers = {"Correlation-Id": correlation_id, "Authorization": "Bearer " + VRoom_Token}
    print("Headers:", headers)
    print("Request url:", url)
    response = requests.request("GET", url, headers=headers)
    print(response.status_code)
    print(response.text)
    APIresponse = response.text
    print(type(APIresponse))
    json_request_body = json.loads(APIresponse)
    print(type(json_request_body))
    BindedFRid = json_request_body[0]["userId"]
    if BindedFRid != FRid or BindedFRid == "":
        response = "Vehicle is not Associated with user"

    else:
        print("User FRid:", BindedFRid)
        response = "Vehicle is Associated with user"

    return response
