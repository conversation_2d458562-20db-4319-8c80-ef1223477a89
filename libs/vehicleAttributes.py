from pyaml_env import parse_config, BaseConfig
import requests
from robot.api.deco import keyword, not_keyword
import json
import forgeRock
import commonUtils


systemVarFile = "../variables/system.yml"
CONTENT_TYPE = "application/json"


@not_keyword
def get_url_gateway():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "vehicle-attributes-url")


@not_keyword
def get_url_service():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "vehicle-attributes-service-url")


@keyword("Get vehicle attributes")
def get_vehicle_attributes(vins, filters=None):
    header = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "vins": vins}

    url = get_url_service()
    getVehicleAttributes = url + "/vin"
    print(getVehicleAttributes)

    response = requests.request("GET", getVehicleAttributes, params=filters, headers=header)

    print(response.text)
    print(response.url)
    return response.text, response.status_code


@keyword("Get vehicle attributes b2c")
def get_vehicle_attributes_b2c(user_email, user_password, filters=None, auth=True):
    if auth is not True:
        token = "abcdef12345"
    else:
        token = forgeRock.get_token(user_email, user_password)

    header = {
        "accept": "*/*",
        "Content-Type": CONTENT_TYPE,
        "authorization": "Bearer " + token,
    }

    url = get_url_gateway()
    getVehicleAttributes = url + "/b2c-token"

    response = requests.request("GET", getVehicleAttributes, params=filters, headers=header)

    print(response.text)
    print(response.url)
    return response.text, response.status_code


@keyword("Update vehicle registration")
def patch_update_vehicle_registration(user_email, user_password, GUID, PAYLOAD, auth=True):
    if auth is not True:
        token = "abcdef12345"
    else:
        token = forgeRock.get_token(user_email, user_password)

    header = {
        "accept": "*/*",
        "Content-Type": CONTENT_TYPE,
        "authorization": token,
        "guid": GUID,
    }

    url = get_url_service()
    getVehicleAttributes = url + "/update"

    openJsonData = open(PAYLOAD, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    payload = json.dumps(jsonRequestBody)

    response = requests.request("PATCH", getVehicleAttributes, headers=header, data=payload)

    print(response.text)
    print(response.url)
    return response.text, response.status_code


@not_keyword
def get_expected_response(expected):
    return commonUtils.get_expected_response(expected)


@keyword("Compare json response")
def compare_json_response(expected, actual):
    return commonUtils.compare_json_response(expected, actual)


@keyword("Count items returned")
def count_items_returned(response, expectedLength):
    item_dict = json.loads(response)
    length = len(item_dict["attributes"])
    lengthString = str(length)
    expectedLengthString = str(expectedLength)

    if lengthString == expectedLengthString:
        return True
    else:
        return False
