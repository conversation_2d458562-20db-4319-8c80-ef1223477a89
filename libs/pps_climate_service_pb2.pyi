import seats_common_pb2 as _seats_common_pb2
import climate_common_pb2 as _climate_common_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnumStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_STATUS_UNSPECIFIED: _ClassVar[EnumStatus]
    ENUM_STATUS_OK: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_DEGRADED: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_UNRELIABLE: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_UNAVAILABLE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_SERVICE_STATE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_MISSING_INPUT_FIELD: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_INPUT_FIELD: _ClassVar[EnumStatus]
    ENUM_STATUS_NOT_OK: _ClassVar[EnumStatus]

class EnumFragranceDispenserStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_DISPENSER_STATUS_UNSPECIFIED: _ClassVar[EnumFragranceDispenserStatus]
    ENUM_FRAGRANCE_DISPENSER_STATUS_OFF: _ClassVar[EnumFragranceDispenserStatus]
    ENUM_FRAGRANCE_DISPENSER_STATUS_ON: _ClassVar[EnumFragranceDispenserStatus]

class EnumFragranceDispenserBoost(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_DISPENSER_BOOST_UNSPECIFIED: _ClassVar[EnumFragranceDispenserBoost]
    ENUM_FRAGRANCE_DISPENSER_BOOST_OFF: _ClassVar[EnumFragranceDispenserBoost]
    ENUM_FRAGRANCE_DISPENSER_BOOST_ON: _ClassVar[EnumFragranceDispenserBoost]
    ENUM_FRAGRANCE_DISPENSER_BOOST_UNAVAILABLE: _ClassVar[EnumFragranceDispenserBoost]

class EnumFragranceDispenserCartridge(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_DISPENSER_CARTRIDGE_UNSPECIFIED: _ClassVar[EnumFragranceDispenserCartridge]
    ENUM_FRAGRANCE_DISPENSER_CARTRIDGE1: _ClassVar[EnumFragranceDispenserCartridge]
    ENUM_FRAGRANCE_DISPENSER_CARTRIDGE2: _ClassVar[EnumFragranceDispenserCartridge]
    ENUM_FRAGRANCE_DISPENSER_CARTRIDGE3: _ClassVar[EnumFragranceDispenserCartridge]
    ENUM_FRAGRANCE_DISPENSER_CARTRIDGE_UNAVAILABLE: _ClassVar[EnumFragranceDispenserCartridge]

class EnumFragranceDispenserIntensity(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_DISPENSER_INTENSITY_UNSPECIFIED: _ClassVar[EnumFragranceDispenserIntensity]
    ENUM_FRAGRANCE_DISPENSER_INTENSITY_LOW: _ClassVar[EnumFragranceDispenserIntensity]
    ENUM_FRAGRANCE_DISPENSER_INTENSITY_MEDIUM: _ClassVar[EnumFragranceDispenserIntensity]
    ENUM_FRAGRANCE_DISPENSER_INTENSITY_HIGH: _ClassVar[EnumFragranceDispenserIntensity]

class EnumFragranceDispenserTray(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_DISPENSER_TRAY_UNSPECIFIED: _ClassVar[EnumFragranceDispenserTray]
    ENUM_FRAGRANCE_DISPENSER_TRAY_CLOSE: _ClassVar[EnumFragranceDispenserTray]
    ENUM_FRAGRANCE_DISPENSER_TRAY_OPEN: _ClassVar[EnumFragranceDispenserTray]
    ENUM_FRAGRANCE_DISPENSER_TRAY_UNAVAILABLE: _ClassVar[EnumFragranceDispenserTray]

class EnumFragranceDispenserAvailability(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_DISPENSER_UNSPECIFIED: _ClassVar[EnumFragranceDispenserAvailability]
    ENUM_FRAGRANCE_DISPENSER_AVAILABLE: _ClassVar[EnumFragranceDispenserAvailability]
    ENUM_FRAGRANCE_DISPENSER_NOT_AVAILABLE: _ClassVar[EnumFragranceDispenserAvailability]

class EnumFragranceCartridgeLevel(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_CARTRIDGE_LEVEL_UNSPECIFIED: _ClassVar[EnumFragranceCartridgeLevel]
    ENUM_FRAGRANCE_CARTRIDGE_LEVEL_HIGH: _ClassVar[EnumFragranceCartridgeLevel]
    ENUM_FRAGRANCE_CARTRIDGE_LEVEL_MEDIUM: _ClassVar[EnumFragranceCartridgeLevel]
    ENUM_FRAGRANCE_CARTRIDGE_LEVEL_LOW: _ClassVar[EnumFragranceCartridgeLevel]
    ENUM_FRAGRANCE_CARTRIDGE_LEVEL_NOT_AVAILABLE: _ClassVar[EnumFragranceCartridgeLevel]

class EnumFragranceCartridgeTypeInfo(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRAGRANCE_CARTRIDGE_UNSPECIFIED: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_NOT_DETECTED: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE1: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE2: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE3: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE4: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE5: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE6: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE7: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE8: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE9: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE10: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE11: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE12: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE13: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE14: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE15: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE16: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE17: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE18: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE19: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE20: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE21: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE22: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE23: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE24: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE25: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE26: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE27: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE28: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE29: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_TYPE30: _ClassVar[EnumFragranceCartridgeTypeInfo]
    ENUM_FRAGRANCE_CARTRIDGE_READ_ERROR: _ClassVar[EnumFragranceCartridgeTypeInfo]

class EnumFridgeState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRIDGE_STATE_UNSPECIFIED: _ClassVar[EnumFridgeState]
    ENUM_FRIDGE_STATE_ON: _ClassVar[EnumFridgeState]
    ENUM_FRIDGE_STATE_OFF: _ClassVar[EnumFridgeState]

class EnumFridgeMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_FRIDGE_MODE_UNSPECIFIED: _ClassVar[EnumFridgeMode]
    ENUM_FRIDGE_MODE_FREEZE: _ClassVar[EnumFridgeMode]
    ENUM_FRIDGE_MODE_COOL: _ClassVar[EnumFridgeMode]
    ENUM_FRIDGE_MODE_HEAT: _ClassVar[EnumFridgeMode]

class EnumCabinAirCleanOperation(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CABIN_AIR_CLEAN_OPERATION_UNSPECIFIED: _ClassVar[EnumCabinAirCleanOperation]
    ENUM_CABIN_AIR_CLEAN_OPERATION_START: _ClassVar[EnumCabinAirCleanOperation]
    ENUM_CABIN_AIR_CLEAN_OPERATION_STOP: _ClassVar[EnumCabinAirCleanOperation]
    ENUM_CABIN_AIR_CLEAN_OPERATION_SCHEDULED_START: _ClassVar[EnumCabinAirCleanOperation]
    ENUM_CABIN_AIR_CLEAN_OPERATION_SCHEDULED_STOP: _ClassVar[EnumCabinAirCleanOperation]

class EnumCabinAirCleanStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_INACTIVE: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_COMPLETE: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CYCLE_EXHAUSTED: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_VEH_POWER_TRANSITION: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERV_ACTIVE: _ClassVar[EnumCabinAirCleanStatus]
    ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS: _ClassVar[EnumCabinAirCleanStatus]

class EnumPreconditionOperation(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_PRECONDITION_OPERATION_UNSPECIFIED: _ClassVar[EnumPreconditionOperation]
    ENUM_PRECONDITION_OPERATION_OFFBOARD_START: _ClassVar[EnumPreconditionOperation]
    ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP: _ClassVar[EnumPreconditionOperation]
    ENUM_PRECONDITION_OPERATION_ONBOARD_START: _ClassVar[EnumPreconditionOperation]
    ENUM_PRECONDITION_OPERATION_ONBOARD_STOP: _ClassVar[EnumPreconditionOperation]
    ENUM_PRECONDITION_OPERATION_SCHEDULED_START: _ClassVar[EnumPreconditionOperation]
    ENUM_PRECONDITION_OPERATION_SCHEDULED_STOP: _ClassVar[EnumPreconditionOperation]

class EnumPreConditionCurrentMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED: _ClassVar[EnumPreConditionCurrentMode]
    ENUM_PRECONDITION_CURRENT_MODE_INACTIVE: _ClassVar[EnumPreConditionCurrentMode]
    ENUM_PRECONDITION_CURRENT_MODE_ONDEMAND: _ClassVar[EnumPreConditionCurrentMode]
    ENUM_PRECONDITION_CURRENT_MODE_TIMED: _ClassVar[EnumPreConditionCurrentMode]

class EnumPreConditionStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_PRECONDITION_STS_UNSPECIFIED: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_OFF: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_START_UP: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_IN_PROGRESS: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_COMPLETE: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_PARTIAL_COMPLETE: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_ERR_LOW_BATTERY: _ClassVar[EnumPreConditionStatus]
    ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT: _ClassVar[EnumPreConditionStatus]

class EnumControlState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CONTROL_STATE_UNSPECIFIED: _ClassVar[EnumControlState]
    ENUM_CONTROL_STATE_INHIBITED: _ClassVar[EnumControlState]
    ENUM_CONTROL_STATE_AVAILABLE: _ClassVar[EnumControlState]

class EnumPM25AirQualityIndexBand(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_AIR_QUALITY_INDEX_BAND_UNSPECIFIED: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_UNDEFINED: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_BAND1: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_BAND2: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_BAND3: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_BAND4: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_BAND5: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_BAND6: _ClassVar[EnumPM25AirQualityIndexBand]
    ENUM_AIR_QUALITY_INDEX_BAND_ERROR: _ClassVar[EnumPM25AirQualityIndexBand]

class EnumCabinIonizerState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CABIN_IONIZER_STATE_UNSPECIFIED: _ClassVar[EnumCabinIonizerState]
    ENUM_CABIN_IONIZER_STATE_DISABLE: _ClassVar[EnumCabinIonizerState]
    ENUM_CABIN_IONIZER_STATE_ENABLE: _ClassVar[EnumCabinIonizerState]

class EnumCabinIonizerErrorState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_CABIN_IONIZER_ERROR_STATE_UNSPECIFIED: _ClassVar[EnumCabinIonizerErrorState]
    ENUM_CABIN_IONIZER_ERROR_STATE_NO: _ClassVar[EnumCabinIonizerErrorState]
    ENUM_CABIN_IONIZER_ERROR_STATE_YES: _ClassVar[EnumCabinIonizerErrorState]

class EnumPreconditionIndicatorState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_PRECON_INDICATOR_STATE_UNSPECIFIED: _ClassVar[EnumPreconditionIndicatorState]
    ENUM_PRECON_INDICATOR_STATE_INACTIVE: _ClassVar[EnumPreconditionIndicatorState]
    ENUM_PRECON_INDICATOR_STATE_ACTIVE_CABIN_VENTING: _ClassVar[EnumPreconditionIndicatorState]
    ENUM_PRECON_INDICATOR_STATE_ACTIVE_WITHOUT_FOH: _ClassVar[EnumPreconditionIndicatorState]
ENUM_STATUS_UNSPECIFIED: EnumStatus
ENUM_STATUS_OK: EnumStatus
ENUM_STATUS_DATA_DEGRADED: EnumStatus
ENUM_STATUS_DATA_UNRELIABLE: EnumStatus
ENUM_STATUS_DATA_UNAVAILABLE: EnumStatus
ENUM_STATUS_ERROR_INVALID_SERVICE_STATE: EnumStatus
ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE: EnumStatus
ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION: EnumStatus
ENUM_STATUS_ERROR_MISSING_INPUT_FIELD: EnumStatus
ENUM_STATUS_ERROR_INVALID_INPUT_FIELD: EnumStatus
ENUM_STATUS_NOT_OK: EnumStatus
ENUM_FRAGRANCE_DISPENSER_STATUS_UNSPECIFIED: EnumFragranceDispenserStatus
ENUM_FRAGRANCE_DISPENSER_STATUS_OFF: EnumFragranceDispenserStatus
ENUM_FRAGRANCE_DISPENSER_STATUS_ON: EnumFragranceDispenserStatus
ENUM_FRAGRANCE_DISPENSER_BOOST_UNSPECIFIED: EnumFragranceDispenserBoost
ENUM_FRAGRANCE_DISPENSER_BOOST_OFF: EnumFragranceDispenserBoost
ENUM_FRAGRANCE_DISPENSER_BOOST_ON: EnumFragranceDispenserBoost
ENUM_FRAGRANCE_DISPENSER_BOOST_UNAVAILABLE: EnumFragranceDispenserBoost
ENUM_FRAGRANCE_DISPENSER_CARTRIDGE_UNSPECIFIED: EnumFragranceDispenserCartridge
ENUM_FRAGRANCE_DISPENSER_CARTRIDGE1: EnumFragranceDispenserCartridge
ENUM_FRAGRANCE_DISPENSER_CARTRIDGE2: EnumFragranceDispenserCartridge
ENUM_FRAGRANCE_DISPENSER_CARTRIDGE3: EnumFragranceDispenserCartridge
ENUM_FRAGRANCE_DISPENSER_CARTRIDGE_UNAVAILABLE: EnumFragranceDispenserCartridge
ENUM_FRAGRANCE_DISPENSER_INTENSITY_UNSPECIFIED: EnumFragranceDispenserIntensity
ENUM_FRAGRANCE_DISPENSER_INTENSITY_LOW: EnumFragranceDispenserIntensity
ENUM_FRAGRANCE_DISPENSER_INTENSITY_MEDIUM: EnumFragranceDispenserIntensity
ENUM_FRAGRANCE_DISPENSER_INTENSITY_HIGH: EnumFragranceDispenserIntensity
ENUM_FRAGRANCE_DISPENSER_TRAY_UNSPECIFIED: EnumFragranceDispenserTray
ENUM_FRAGRANCE_DISPENSER_TRAY_CLOSE: EnumFragranceDispenserTray
ENUM_FRAGRANCE_DISPENSER_TRAY_OPEN: EnumFragranceDispenserTray
ENUM_FRAGRANCE_DISPENSER_TRAY_UNAVAILABLE: EnumFragranceDispenserTray
ENUM_FRAGRANCE_DISPENSER_UNSPECIFIED: EnumFragranceDispenserAvailability
ENUM_FRAGRANCE_DISPENSER_AVAILABLE: EnumFragranceDispenserAvailability
ENUM_FRAGRANCE_DISPENSER_NOT_AVAILABLE: EnumFragranceDispenserAvailability
ENUM_FRAGRANCE_CARTRIDGE_LEVEL_UNSPECIFIED: EnumFragranceCartridgeLevel
ENUM_FRAGRANCE_CARTRIDGE_LEVEL_HIGH: EnumFragranceCartridgeLevel
ENUM_FRAGRANCE_CARTRIDGE_LEVEL_MEDIUM: EnumFragranceCartridgeLevel
ENUM_FRAGRANCE_CARTRIDGE_LEVEL_LOW: EnumFragranceCartridgeLevel
ENUM_FRAGRANCE_CARTRIDGE_LEVEL_NOT_AVAILABLE: EnumFragranceCartridgeLevel
ENUM_FRAGRANCE_CARTRIDGE_UNSPECIFIED: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_NOT_DETECTED: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE1: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE2: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE3: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE4: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE5: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE6: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE7: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE8: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE9: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE10: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE11: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE12: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE13: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE14: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE15: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE16: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE17: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE18: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE19: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE20: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE21: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE22: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE23: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE24: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE25: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE26: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE27: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE28: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE29: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_TYPE30: EnumFragranceCartridgeTypeInfo
ENUM_FRAGRANCE_CARTRIDGE_READ_ERROR: EnumFragranceCartridgeTypeInfo
ENUM_FRIDGE_STATE_UNSPECIFIED: EnumFridgeState
ENUM_FRIDGE_STATE_ON: EnumFridgeState
ENUM_FRIDGE_STATE_OFF: EnumFridgeState
ENUM_FRIDGE_MODE_UNSPECIFIED: EnumFridgeMode
ENUM_FRIDGE_MODE_FREEZE: EnumFridgeMode
ENUM_FRIDGE_MODE_COOL: EnumFridgeMode
ENUM_FRIDGE_MODE_HEAT: EnumFridgeMode
ENUM_CABIN_AIR_CLEAN_OPERATION_UNSPECIFIED: EnumCabinAirCleanOperation
ENUM_CABIN_AIR_CLEAN_OPERATION_START: EnumCabinAirCleanOperation
ENUM_CABIN_AIR_CLEAN_OPERATION_STOP: EnumCabinAirCleanOperation
ENUM_CABIN_AIR_CLEAN_OPERATION_SCHEDULED_START: EnumCabinAirCleanOperation
ENUM_CABIN_AIR_CLEAN_OPERATION_SCHEDULED_STOP: EnumCabinAirCleanOperation
ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_INACTIVE: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_COMPLETE: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_CYCLE_EXHAUSTED: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_VEH_POWER_TRANSITION: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERV_ACTIVE: EnumCabinAirCleanStatus
ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS: EnumCabinAirCleanStatus
ENUM_PRECONDITION_OPERATION_UNSPECIFIED: EnumPreconditionOperation
ENUM_PRECONDITION_OPERATION_OFFBOARD_START: EnumPreconditionOperation
ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP: EnumPreconditionOperation
ENUM_PRECONDITION_OPERATION_ONBOARD_START: EnumPreconditionOperation
ENUM_PRECONDITION_OPERATION_ONBOARD_STOP: EnumPreconditionOperation
ENUM_PRECONDITION_OPERATION_SCHEDULED_START: EnumPreconditionOperation
ENUM_PRECONDITION_OPERATION_SCHEDULED_STOP: EnumPreconditionOperation
ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED: EnumPreConditionCurrentMode
ENUM_PRECONDITION_CURRENT_MODE_INACTIVE: EnumPreConditionCurrentMode
ENUM_PRECONDITION_CURRENT_MODE_ONDEMAND: EnumPreConditionCurrentMode
ENUM_PRECONDITION_CURRENT_MODE_TIMED: EnumPreConditionCurrentMode
ENUM_PRECONDITION_STS_UNSPECIFIED: EnumPreConditionStatus
ENUM_PRECONDITION_STS_OFF: EnumPreConditionStatus
ENUM_PRECONDITION_STS_START_UP: EnumPreConditionStatus
ENUM_PRECONDITION_STS_IN_PROGRESS: EnumPreConditionStatus
ENUM_PRECONDITION_STS_COMPLETE: EnumPreConditionStatus
ENUM_PRECONDITION_STS_PARTIAL_COMPLETE: EnumPreConditionStatus
ENUM_PRECONDITION_STS_ERR_LOW_BATTERY: EnumPreConditionStatus
ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT: EnumPreConditionStatus
ENUM_CONTROL_STATE_UNSPECIFIED: EnumControlState
ENUM_CONTROL_STATE_INHIBITED: EnumControlState
ENUM_CONTROL_STATE_AVAILABLE: EnumControlState
ENUM_AIR_QUALITY_INDEX_BAND_UNSPECIFIED: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_UNDEFINED: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_BAND1: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_BAND2: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_BAND3: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_BAND4: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_BAND5: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_BAND6: EnumPM25AirQualityIndexBand
ENUM_AIR_QUALITY_INDEX_BAND_ERROR: EnumPM25AirQualityIndexBand
ENUM_CABIN_IONIZER_STATE_UNSPECIFIED: EnumCabinIonizerState
ENUM_CABIN_IONIZER_STATE_DISABLE: EnumCabinIonizerState
ENUM_CABIN_IONIZER_STATE_ENABLE: EnumCabinIonizerState
ENUM_CABIN_IONIZER_ERROR_STATE_UNSPECIFIED: EnumCabinIonizerErrorState
ENUM_CABIN_IONIZER_ERROR_STATE_NO: EnumCabinIonizerErrorState
ENUM_CABIN_IONIZER_ERROR_STATE_YES: EnumCabinIonizerErrorState
ENUM_PRECON_INDICATOR_STATE_UNSPECIFIED: EnumPreconditionIndicatorState
ENUM_PRECON_INDICATOR_STATE_INACTIVE: EnumPreconditionIndicatorState
ENUM_PRECON_INDICATOR_STATE_ACTIVE_CABIN_VENTING: EnumPreconditionIndicatorState
ENUM_PRECON_INDICATOR_STATE_ACTIVE_WITHOUT_FOH: EnumPreconditionIndicatorState

class SetSeatClimateRequest(_message.Message):
    __slots__ = ["seat_climate_zone"]
    SEAT_CLIMATE_ZONE_FIELD_NUMBER: _ClassVar[int]
    seat_climate_zone: _containers.RepeatedCompositeFieldContainer[_climate_common_pb2.SeatClimateZoneState]
    def __init__(self, seat_climate_zone: _Optional[_Iterable[_Union[_climate_common_pb2.SeatClimateZoneState, _Mapping]]] = ...) -> None: ...

class SetSeatClimateResponse(_message.Message):
    __slots__ = ["seat_selection", "status"]
    SEAT_SELECTION_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    seat_selection: _seats_common_pb2.EnumSeatSelection
    status: EnumStatus
    def __init__(self, seat_selection: _Optional[_Union[_seats_common_pb2.EnumSeatSelection, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetSeatClimateStatusRequest(_message.Message):
    __slots__ = ["seat_selection"]
    SEAT_SELECTION_FIELD_NUMBER: _ClassVar[int]
    seat_selection: _seats_common_pb2.EnumSeatSelection
    def __init__(self, seat_selection: _Optional[_Union[_seats_common_pb2.EnumSeatSelection, str]] = ...) -> None: ...

class GetSeatClimateStatusResponse(_message.Message):
    __slots__ = ["status", "seat_climate_zone"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_ZONE_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    seat_climate_zone: _containers.RepeatedCompositeFieldContainer[_climate_common_pb2.SeatClimateZoneState]
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ..., seat_climate_zone: _Optional[_Iterable[_Union[_climate_common_pb2.SeatClimateZoneState, _Mapping]]] = ...) -> None: ...

class GetFragranceDispenserSystemStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserSystemStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_status", "status"]
    FRAG_DISPENSER_STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_status: EnumFragranceDispenserStatus
    status: EnumStatus
    def __init__(self, frag_dispenser_status: _Optional[_Union[EnumFragranceDispenserStatus, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetFragranceDispenserSystemRequest(_message.Message):
    __slots__ = ["frag_dispenser_status"]
    FRAG_DISPENSER_STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_status: EnumFragranceDispenserStatus
    def __init__(self, frag_dispenser_status: _Optional[_Union[EnumFragranceDispenserStatus, str]] = ...) -> None: ...

class SetFragranceDispenserSystemResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserBoostStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserBoostStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_boost", "status"]
    FRAG_DISPENSER_BOOST_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_boost: EnumFragranceDispenserBoost
    status: EnumStatus
    def __init__(self, frag_dispenser_boost: _Optional[_Union[EnumFragranceDispenserBoost, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetFragranceDispenserBoostRequest(_message.Message):
    __slots__ = ["frag_dispenser_boost"]
    FRAG_DISPENSER_BOOST_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_boost: EnumFragranceDispenserBoost
    def __init__(self, frag_dispenser_boost: _Optional[_Union[EnumFragranceDispenserBoost, str]] = ...) -> None: ...

class SetFragranceDispenserBoostResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserCartridgeStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserCartridgeStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_cartridge", "status"]
    FRAG_DISPENSER_CARTRIDGE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge: EnumFragranceDispenserCartridge
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge: _Optional[_Union[EnumFragranceDispenserCartridge, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetFragranceDispenserCartridgeRequest(_message.Message):
    __slots__ = ["frag_dispenser_cartridge"]
    FRAG_DISPENSER_CARTRIDGE_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge: EnumFragranceDispenserCartridge
    def __init__(self, frag_dispenser_cartridge: _Optional[_Union[EnumFragranceDispenserCartridge, str]] = ...) -> None: ...

class SetFragranceDispenserCartridgeResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserIntensityStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserIntensityStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_intensity", "status"]
    FRAG_DISPENSER_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_intensity: EnumFragranceDispenserIntensity
    status: EnumStatus
    def __init__(self, frag_dispenser_intensity: _Optional[_Union[EnumFragranceDispenserIntensity, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetFragranceDispenserIntensityRequest(_message.Message):
    __slots__ = ["frag_dispenser_intensity"]
    FRAG_DISPENSER_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_intensity: EnumFragranceDispenserIntensity
    def __init__(self, frag_dispenser_intensity: _Optional[_Union[EnumFragranceDispenserIntensity, str]] = ...) -> None: ...

class SetFragranceDispenserIntensityResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserTrayStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserTrayStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_tray", "status"]
    FRAG_DISPENSER_TRAY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_tray: EnumFragranceDispenserTray
    status: EnumStatus
    def __init__(self, frag_dispenser_tray: _Optional[_Union[EnumFragranceDispenserTray, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetFragranceDispenserTrayRequest(_message.Message):
    __slots__ = ["frag_dispenser_tray"]
    FRAG_DISPENSER_TRAY_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_tray: EnumFragranceDispenserTray
    def __init__(self, frag_dispenser_tray: _Optional[_Union[EnumFragranceDispenserTray, str]] = ...) -> None: ...

class SetFragranceDispenserTrayResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserAvailabilityStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserAvailabilityStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_availability", "status"]
    FRAG_DISPENSER_AVAILABILITY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_availability: EnumFragranceDispenserAvailability
    status: EnumStatus
    def __init__(self, frag_dispenser_availability: _Optional[_Union[EnumFragranceDispenserAvailability, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserCartridge1InfoStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserCartridge1InfoStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_cartridge1_level", "frag_dispenser_cartridge1_type", "status"]
    FRAG_DISPENSER_CARTRIDGE1_LEVEL_FIELD_NUMBER: _ClassVar[int]
    FRAG_DISPENSER_CARTRIDGE1_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge1_level: EnumFragranceCartridgeLevel
    frag_dispenser_cartridge1_type: EnumFragranceCartridgeTypeInfo
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge1_level: _Optional[_Union[EnumFragranceCartridgeLevel, str]] = ..., frag_dispenser_cartridge1_type: _Optional[_Union[EnumFragranceCartridgeTypeInfo, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserCartridge2InfoStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserCartridge2InfoStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_cartridge2_level", "frag_dispenser_cartridge2_type", "status"]
    FRAG_DISPENSER_CARTRIDGE2_LEVEL_FIELD_NUMBER: _ClassVar[int]
    FRAG_DISPENSER_CARTRIDGE2_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge2_level: EnumFragranceCartridgeLevel
    frag_dispenser_cartridge2_type: EnumFragranceCartridgeTypeInfo
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge2_level: _Optional[_Union[EnumFragranceCartridgeLevel, str]] = ..., frag_dispenser_cartridge2_type: _Optional[_Union[EnumFragranceCartridgeTypeInfo, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFragranceDispenserCartridge3InfoStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFragranceDispenserCartridge3InfoStatusResponse(_message.Message):
    __slots__ = ["frag_dispenser_cartridge3_level", "frag_dispenser_cartridge3_type", "status"]
    FRAG_DISPENSER_CARTRIDGE3_LEVEL_FIELD_NUMBER: _ClassVar[int]
    FRAG_DISPENSER_CARTRIDGE3_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge3_level: EnumFragranceCartridgeLevel
    frag_dispenser_cartridge3_type: EnumFragranceCartridgeTypeInfo
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge3_level: _Optional[_Union[EnumFragranceCartridgeLevel, str]] = ..., frag_dispenser_cartridge3_type: _Optional[_Union[EnumFragranceCartridgeTypeInfo, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetFridgeSystemRequest(_message.Message):
    __slots__ = ["fridge_state", "fridge_mode"]
    FRIDGE_STATE_FIELD_NUMBER: _ClassVar[int]
    FRIDGE_MODE_FIELD_NUMBER: _ClassVar[int]
    fridge_state: EnumFridgeState
    fridge_mode: EnumFridgeMode
    def __init__(self, fridge_state: _Optional[_Union[EnumFridgeState, str]] = ..., fridge_mode: _Optional[_Union[EnumFridgeMode, str]] = ...) -> None: ...

class SetFridgeSystemResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetFridgeSystemStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetFridgeSystemStatusResponse(_message.Message):
    __slots__ = ["status", "fridge_state", "fridge_mode"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    FRIDGE_STATE_FIELD_NUMBER: _ClassVar[int]
    FRIDGE_MODE_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    fridge_state: EnumFridgeState
    fridge_mode: EnumFridgeMode
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ..., fridge_state: _Optional[_Union[EnumFridgeState, str]] = ..., fridge_mode: _Optional[_Union[EnumFridgeMode, str]] = ...) -> None: ...

class SetVehicleCabinAirCleanRequest(_message.Message):
    __slots__ = ["cabin_air_cleaning_request"]
    CABIN_AIR_CLEANING_REQUEST_FIELD_NUMBER: _ClassVar[int]
    cabin_air_cleaning_request: EnumCabinAirCleanOperation
    def __init__(self, cabin_air_cleaning_request: _Optional[_Union[EnumCabinAirCleanOperation, str]] = ...) -> None: ...

class SetVehicleCabinAirCleanResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetVehicleCabinAirCleanStateRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetVehicleCabinAirCleanStateResponse(_message.Message):
    __slots__ = ["cabin_air_cleaning_status", "cabin_pm2_5_level", "cabin_pm2_5_band", "external_pm2_5_level", "external_pm2_5_band", "status", "cabin_air_clean_cycles_remaining", "row1_cabin_ionizer_error_state", "row2_cabin_ionizer_error_state"]
    CABIN_AIR_CLEANING_STATUS_FIELD_NUMBER: _ClassVar[int]
    CABIN_PM2_5_LEVEL_FIELD_NUMBER: _ClassVar[int]
    CABIN_PM2_5_BAND_FIELD_NUMBER: _ClassVar[int]
    EXTERNAL_PM2_5_LEVEL_FIELD_NUMBER: _ClassVar[int]
    EXTERNAL_PM2_5_BAND_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CABIN_AIR_CLEAN_CYCLES_REMAINING_FIELD_NUMBER: _ClassVar[int]
    ROW1_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    ROW2_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    cabin_air_cleaning_status: EnumCabinAirCleanStatus
    cabin_pm2_5_level: int
    cabin_pm2_5_band: EnumPM25AirQualityIndexBand
    external_pm2_5_level: int
    external_pm2_5_band: EnumPM25AirQualityIndexBand
    status: EnumStatus
    cabin_air_clean_cycles_remaining: int
    row1_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    row2_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    def __init__(self, cabin_air_cleaning_status: _Optional[_Union[EnumCabinAirCleanStatus, str]] = ..., cabin_pm2_5_level: _Optional[int] = ..., cabin_pm2_5_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., external_pm2_5_level: _Optional[int] = ..., external_pm2_5_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ..., cabin_air_clean_cycles_remaining: _Optional[int] = ..., row1_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ..., row2_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ...) -> None: ...

class SetVehiclePreconditionRequest(_message.Message):
    __slots__ = ["precondition_request", "precondition_target_temperature"]
    PRECONDITION_REQUEST_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_TARGET_TEMPERATURE_FIELD_NUMBER: _ClassVar[int]
    precondition_request: EnumPreconditionOperation
    precondition_target_temperature: float
    def __init__(self, precondition_request: _Optional[_Union[EnumPreconditionOperation, str]] = ..., precondition_target_temperature: _Optional[float] = ...) -> None: ...

class SetVehiclePreconditionResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetVehiclePreconditionStateRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetVehiclePreconditionStateResponse(_message.Message):
    __slots__ = ["precondition_mode", "precondition_status", "precon_time", "status", "precondition_control_state", "precondition_indicator_state"]
    PRECONDITION_MODE_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_STATUS_FIELD_NUMBER: _ClassVar[int]
    PRECON_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_CONTROL_STATE_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_INDICATOR_STATE_FIELD_NUMBER: _ClassVar[int]
    precondition_mode: EnumPreConditionCurrentMode
    precondition_status: EnumPreConditionStatus
    precon_time: int
    status: EnumStatus
    precondition_control_state: EnumControlState
    precondition_indicator_state: EnumPreconditionIndicatorState
    def __init__(self, precondition_mode: _Optional[_Union[EnumPreConditionCurrentMode, str]] = ..., precondition_status: _Optional[_Union[EnumPreConditionStatus, str]] = ..., precon_time: _Optional[int] = ..., status: _Optional[_Union[EnumStatus, str]] = ..., precondition_control_state: _Optional[_Union[EnumControlState, str]] = ..., precondition_indicator_state: _Optional[_Union[EnumPreconditionIndicatorState, str]] = ...) -> None: ...

class GetPM25AirQualityIndexStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetPM25AirQualityIndexStatusResponse(_message.Message):
    __slots__ = ["external_aqi_band", "pm25_external_status", "internal_aqi_band", "pm25_internal_status", "status"]
    EXTERNAL_AQI_BAND_FIELD_NUMBER: _ClassVar[int]
    PM25_EXTERNAL_STATUS_FIELD_NUMBER: _ClassVar[int]
    INTERNAL_AQI_BAND_FIELD_NUMBER: _ClassVar[int]
    PM25_INTERNAL_STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    external_aqi_band: EnumPM25AirQualityIndexBand
    pm25_external_status: int
    internal_aqi_band: EnumPM25AirQualityIndexBand
    pm25_internal_status: int
    status: EnumStatus
    def __init__(self, external_aqi_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., pm25_external_status: _Optional[int] = ..., internal_aqi_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., pm25_internal_status: _Optional[int] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetCabinIoniserStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetCabinIoniserStatusResponse(_message.Message):
    __slots__ = ["cabin_ionizer_state", "row1_cabin_ionizer_error_state", "row2_cabin_ionizer_error_state", "status"]
    CABIN_IONIZER_STATE_FIELD_NUMBER: _ClassVar[int]
    ROW1_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    ROW2_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    cabin_ionizer_state: EnumCabinIonizerState
    row1_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    row2_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    status: EnumStatus
    def __init__(self, cabin_ionizer_state: _Optional[_Union[EnumCabinIonizerState, str]] = ..., row1_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ..., row2_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetCabinIoniserRequest(_message.Message):
    __slots__ = ["cabin_ionizer_state"]
    CABIN_IONIZER_STATE_FIELD_NUMBER: _ClassVar[int]
    cabin_ionizer_state: EnumCabinIonizerState
    def __init__(self, cabin_ionizer_state: _Optional[_Union[EnumCabinIonizerState, str]] = ...) -> None: ...

class SetCabinIoniserResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetHeatedSteeringWheelRequest(_message.Message):
    __slots__ = ["hsw_temperature_level"]
    HSW_TEMPERATURE_LEVEL_FIELD_NUMBER: _ClassVar[int]
    hsw_temperature_level: _climate_common_pb2.EnumHSWTemperatureLevel
    def __init__(self, hsw_temperature_level: _Optional[_Union[_climate_common_pb2.EnumHSWTemperatureLevel, str]] = ...) -> None: ...

class SetHeatedSteeringWheelResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetHeatedSteeringWheelStatusRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetHeatedSteeringWheelStatusResponse(_message.Message):
    __slots__ = ["hsw_temperature_level", "status", "hsw_control_state"]
    HSW_TEMPERATURE_LEVEL_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    HSW_CONTROL_STATE_FIELD_NUMBER: _ClassVar[int]
    hsw_temperature_level: _climate_common_pb2.EnumHSWTemperatureLevel
    status: EnumStatus
    hsw_control_state: EnumControlState
    def __init__(self, hsw_temperature_level: _Optional[_Union[_climate_common_pb2.EnumHSWTemperatureLevel, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ..., hsw_control_state: _Optional[_Union[EnumControlState, str]] = ...) -> None: ...

class NotifySeatClimateStatus(_message.Message):
    __slots__ = ["status", "seat_climate_zone"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_ZONE_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    seat_climate_zone: _containers.RepeatedCompositeFieldContainer[_climate_common_pb2.SeatClimateZoneState]
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ..., seat_climate_zone: _Optional[_Iterable[_Union[_climate_common_pb2.SeatClimateZoneState, _Mapping]]] = ...) -> None: ...

class NotifyFragranceDispenserSystemStatus(_message.Message):
    __slots__ = ["frag_dispenser_status", "status"]
    FRAG_DISPENSER_STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_status: EnumFragranceDispenserStatus
    status: EnumStatus
    def __init__(self, frag_dispenser_status: _Optional[_Union[EnumFragranceDispenserStatus, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserBoostStatus(_message.Message):
    __slots__ = ["frag_dispenser_boost", "status"]
    FRAG_DISPENSER_BOOST_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_boost: EnumFragranceDispenserBoost
    status: EnumStatus
    def __init__(self, frag_dispenser_boost: _Optional[_Union[EnumFragranceDispenserBoost, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserCartridgeStatus(_message.Message):
    __slots__ = ["frag_dispenser_cartridge", "status"]
    FRAG_DISPENSER_CARTRIDGE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge: EnumFragranceDispenserCartridge
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge: _Optional[_Union[EnumFragranceDispenserCartridge, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserIntensityStatus(_message.Message):
    __slots__ = ["frag_dispenser_intensity", "status"]
    FRAG_DISPENSER_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_intensity: EnumFragranceDispenserIntensity
    status: EnumStatus
    def __init__(self, frag_dispenser_intensity: _Optional[_Union[EnumFragranceDispenserIntensity, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserTrayStatus(_message.Message):
    __slots__ = ["frag_dispenser_tray", "status"]
    FRAG_DISPENSER_TRAY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_tray: EnumFragranceDispenserTray
    status: EnumStatus
    def __init__(self, frag_dispenser_tray: _Optional[_Union[EnumFragranceDispenserTray, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserAvailabilityStatus(_message.Message):
    __slots__ = ["frag_dispenser_availability", "status"]
    FRAG_DISPENSER_AVAILABILITY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_availability: EnumFragranceDispenserAvailability
    status: EnumStatus
    def __init__(self, frag_dispenser_availability: _Optional[_Union[EnumFragranceDispenserAvailability, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserCartridge1InfoStatus(_message.Message):
    __slots__ = ["frag_dispenser_cartridge1_level", "frag_dispenser_cartridge1_type", "status"]
    FRAG_DISPENSER_CARTRIDGE1_LEVEL_FIELD_NUMBER: _ClassVar[int]
    FRAG_DISPENSER_CARTRIDGE1_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge1_level: EnumFragranceCartridgeLevel
    frag_dispenser_cartridge1_type: EnumFragranceCartridgeTypeInfo
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge1_level: _Optional[_Union[EnumFragranceCartridgeLevel, str]] = ..., frag_dispenser_cartridge1_type: _Optional[_Union[EnumFragranceCartridgeTypeInfo, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserCartridge2InfoStatus(_message.Message):
    __slots__ = ["frag_dispenser_cartridge2_level", "frag_dispenser_cartridge2_type", "status"]
    FRAG_DISPENSER_CARTRIDGE2_LEVEL_FIELD_NUMBER: _ClassVar[int]
    FRAG_DISPENSER_CARTRIDGE2_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge2_level: EnumFragranceCartridgeLevel
    frag_dispenser_cartridge2_type: EnumFragranceCartridgeTypeInfo
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge2_level: _Optional[_Union[EnumFragranceCartridgeLevel, str]] = ..., frag_dispenser_cartridge2_type: _Optional[_Union[EnumFragranceCartridgeTypeInfo, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFragranceDispenserCartridge3InfoStatus(_message.Message):
    __slots__ = ["frag_dispenser_cartridge3_level", "frag_dispenser_cartridge3_type", "status"]
    FRAG_DISPENSER_CARTRIDGE3_LEVEL_FIELD_NUMBER: _ClassVar[int]
    FRAG_DISPENSER_CARTRIDGE3_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    frag_dispenser_cartridge3_level: EnumFragranceCartridgeLevel
    frag_dispenser_cartridge3_type: EnumFragranceCartridgeTypeInfo
    status: EnumStatus
    def __init__(self, frag_dispenser_cartridge3_level: _Optional[_Union[EnumFragranceCartridgeLevel, str]] = ..., frag_dispenser_cartridge3_type: _Optional[_Union[EnumFragranceCartridgeTypeInfo, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyFridgeSystemStatus(_message.Message):
    __slots__ = ["status", "fridge_state", "fridge_mode"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    FRIDGE_STATE_FIELD_NUMBER: _ClassVar[int]
    FRIDGE_MODE_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    fridge_state: EnumFridgeState
    fridge_mode: EnumFridgeMode
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ..., fridge_state: _Optional[_Union[EnumFridgeState, str]] = ..., fridge_mode: _Optional[_Union[EnumFridgeMode, str]] = ...) -> None: ...

class NotifyVehicleCabinAirCleanState(_message.Message):
    __slots__ = ["cabin_air_cleaning_status", "cabin_pm2_5_level", "cabin_pm2_5_band", "external_pm2_5_level", "external_pm2_5_band", "status", "cabin_air_clean_cycles_remaining", "row1_cabin_ionizer_error_state", "row2_cabin_ionizer_error_state"]
    CABIN_AIR_CLEANING_STATUS_FIELD_NUMBER: _ClassVar[int]
    CABIN_PM2_5_LEVEL_FIELD_NUMBER: _ClassVar[int]
    CABIN_PM2_5_BAND_FIELD_NUMBER: _ClassVar[int]
    EXTERNAL_PM2_5_LEVEL_FIELD_NUMBER: _ClassVar[int]
    EXTERNAL_PM2_5_BAND_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CABIN_AIR_CLEAN_CYCLES_REMAINING_FIELD_NUMBER: _ClassVar[int]
    ROW1_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    ROW2_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    cabin_air_cleaning_status: EnumCabinAirCleanStatus
    cabin_pm2_5_level: int
    cabin_pm2_5_band: EnumPM25AirQualityIndexBand
    external_pm2_5_level: int
    external_pm2_5_band: EnumPM25AirQualityIndexBand
    status: EnumStatus
    cabin_air_clean_cycles_remaining: int
    row1_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    row2_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    def __init__(self, cabin_air_cleaning_status: _Optional[_Union[EnumCabinAirCleanStatus, str]] = ..., cabin_pm2_5_level: _Optional[int] = ..., cabin_pm2_5_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., external_pm2_5_level: _Optional[int] = ..., external_pm2_5_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ..., cabin_air_clean_cycles_remaining: _Optional[int] = ..., row1_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ..., row2_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ...) -> None: ...

class NotifyVehiclePreconditionState(_message.Message):
    __slots__ = ["precondition_mode", "precondition_status", "precon_time", "status", "precondition_control_state", "precondition_indicator_state"]
    PRECONDITION_MODE_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_STATUS_FIELD_NUMBER: _ClassVar[int]
    PRECON_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_CONTROL_STATE_FIELD_NUMBER: _ClassVar[int]
    PRECONDITION_INDICATOR_STATE_FIELD_NUMBER: _ClassVar[int]
    precondition_mode: EnumPreConditionCurrentMode
    precondition_status: EnumPreConditionStatus
    precon_time: int
    status: EnumStatus
    precondition_control_state: EnumControlState
    precondition_indicator_state: EnumPreconditionIndicatorState
    def __init__(self, precondition_mode: _Optional[_Union[EnumPreConditionCurrentMode, str]] = ..., precondition_status: _Optional[_Union[EnumPreConditionStatus, str]] = ..., precon_time: _Optional[int] = ..., status: _Optional[_Union[EnumStatus, str]] = ..., precondition_control_state: _Optional[_Union[EnumControlState, str]] = ..., precondition_indicator_state: _Optional[_Union[EnumPreconditionIndicatorState, str]] = ...) -> None: ...

class NotifyPM25AirQualityIndexStatus(_message.Message):
    __slots__ = ["external_aqi_band", "pm25_external_status", "internal_aqi_band", "pm25_internal_status", "status"]
    EXTERNAL_AQI_BAND_FIELD_NUMBER: _ClassVar[int]
    PM25_EXTERNAL_STATUS_FIELD_NUMBER: _ClassVar[int]
    INTERNAL_AQI_BAND_FIELD_NUMBER: _ClassVar[int]
    PM25_INTERNAL_STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    external_aqi_band: EnumPM25AirQualityIndexBand
    pm25_external_status: int
    internal_aqi_band: EnumPM25AirQualityIndexBand
    pm25_internal_status: int
    status: EnumStatus
    def __init__(self, external_aqi_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., pm25_external_status: _Optional[int] = ..., internal_aqi_band: _Optional[_Union[EnumPM25AirQualityIndexBand, str]] = ..., pm25_internal_status: _Optional[int] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyCabinIoniserStatus(_message.Message):
    __slots__ = ["cabin_ionizer_state", "row1_cabin_ionizer_error_state", "row2_cabin_ionizer_error_state", "status"]
    CABIN_IONIZER_STATE_FIELD_NUMBER: _ClassVar[int]
    ROW1_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    ROW2_CABIN_IONIZER_ERROR_STATE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    cabin_ionizer_state: EnumCabinIonizerState
    row1_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    row2_cabin_ionizer_error_state: EnumCabinIonizerErrorState
    status: EnumStatus
    def __init__(self, cabin_ionizer_state: _Optional[_Union[EnumCabinIonizerState, str]] = ..., row1_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ..., row2_cabin_ionizer_error_state: _Optional[_Union[EnumCabinIonizerErrorState, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class NotifyHeatedSteeringWheelStatus(_message.Message):
    __slots__ = ["hsw_temperature_level", "status", "hsw_control_state"]
    HSW_TEMPERATURE_LEVEL_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    HSW_CONTROL_STATE_FIELD_NUMBER: _ClassVar[int]
    hsw_temperature_level: _climate_common_pb2.EnumHSWTemperatureLevel
    status: EnumStatus
    hsw_control_state: EnumControlState
    def __init__(self, hsw_temperature_level: _Optional[_Union[_climate_common_pb2.EnumHSWTemperatureLevel, str]] = ..., status: _Optional[_Union[EnumStatus, str]] = ..., hsw_control_state: _Optional[_Union[EnumControlState, str]] = ...) -> None: ...
