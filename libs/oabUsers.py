from robot.api.deco import keyword
import commonUtils
import string
import random
import requests
import forgeRock
import json


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"

serviceUrl = "oneapp-backend-url"


def id_generator(size=8, chars=string.ascii_lowercase):
    autogeneratedName = "".join(random.choice(chars) for _ in range(size))
    return autogeneratedName


# POST customers details
@keyword("Post customers details")
def post_customer_details(user_email, user_password, request_body_path, approov=False, auth=True):
    # Retrieve endpoint URL
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/register"

    # Safely load JSON request body
    with open(request_body_path, "r") as file:
        json_request_body = json.load(file)

    # Generate random UK customer details
    customer_address_details = {
        "address_line1": commonUtils.generate_uk_address_line1(),
        "county": commonUtils.generate_uk_county_temp(),
        "city": commonUtils.generate_uk_city(),
        "post_code": commonUtils.generate_uk_postcode(),
    }

    # Generate random UK customer phone details
    customer_phone_details = {"phone_number": commonUtils.generate_uk_phone_number()}

    # Update both home and delivery addresses
    for address_type in ["home_address", "delivery_address"]:
        if address_type in json_request_body:
            json_request_body[address_type].update(customer_address_details)
        else:
            print(f"Warning: '{address_type}' key missing in request body.")

    # Add phone number separately
    json_request_body["phone_number"] = customer_phone_details["phone_number"]

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, json_request_body, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]


# GET customer details
@keyword("Get customer details")
def get_customer_details(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# PUT customers details
@keyword("Update customers details")
def update_customer_details(user_email, user_password, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/update"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PUT", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]


@keyword("Update customers details with name")
def update_customer_details_with_name(user_email, user_password, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    token = forgeRock.get_token(user_email, user_password)

    headers = commonUtils.addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    endpoint = url + "/update"

    response = requests.request("PUT", endpoint, json=requestBody, headers=headers)
    print(response)
    return response.text, response.status_code


# POST customers details via GW
@keyword("Post customers details via GW")
def post_customer_details_via_GW(user_email, user_password, request_body_path, approov=False, auth=True):
    # Retrieve endpoint URL
    endpoint = commonUtils.get_url(serviceUrl)

    # Safely load JSON request body
    with open(request_body_path, "r") as file:
        json_request_body = json.load(file)

    # Generate random UK customer details
    customer_address_details = {
        "address_line1": commonUtils.generate_uk_address_line1(),
        "county": commonUtils.generate_uk_county(),
        "city": commonUtils.generate_uk_city(),
        "post_code": commonUtils.generate_uk_postcode(),
    }

    # Generate random UK customer phone details
    customer_phone_details = {"phone_number": commonUtils.generate_uk_phone_number()}

    # Update both home and delivery addresses
    for address_type in ["home_address", "delivery_address"]:
        if address_type in json_request_body:
            json_request_body[address_type].update(customer_address_details)
        else:
            print(f"Warning: '{address_type}' key missing in request body.")

    # Add phone number separately
    json_request_body["phone_number"] = customer_phone_details["phone_number"]

    print(f"json body: '{json_request_body}'")

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, json_request_body, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1], response[2]


# PUT customers details via GW
@keyword("Update customers details via GW")
def update_customer_details_via_GW(user_email, user_password, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PUT", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]


# GET customer details via GW
@keyword("Get customer details via GW")
def get_customer_details_via_gw(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me"

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# PUT customer details via GW
@keyword("Update customers details with name via GW")
def update_customer_details_with_name_via_gw(user_email, user_password, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    token = forgeRock.get_token(user_email, user_password)

    headers = commonUtils.addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    endpoint = url + "/me"

    response = requests.request("PUT", endpoint, json=requestBody, headers=headers)
    print(response)
    return response.text, response.status_code


# DELETE customer account
@keyword("Delete customers account")
def delete_customer_details(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    if approov is True:
        endpoint = url + "/me"
    else:
        endpoint = url + "/delete"

    response = commonUtils.delete_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]
