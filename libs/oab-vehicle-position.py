import commonUtils
from robot.api.deco import keyword

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"
serviceUrl = "oneapp-backend-url"


# GET vehicle position
@keyword("Get vehicle position")
def get_vehicle_position(user_email, user_password, vehicleId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me/vehicles/" + vehicleId + "/positions/latest"
    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]
