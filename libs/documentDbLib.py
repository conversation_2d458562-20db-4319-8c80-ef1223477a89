from robot.api.deco import keyword, not_keyword
import pymongo
import sys

sys.path.append("../libs")


# Pass this object with the relevant connection details for your service to connect to mongo client
class MongoConnectionDetails:
    def __init__(self, db_username: str, db_password: str, db_host: str, mongo_port: str, tls_cert: str, database: str, collection: str):
        self.db_username = db_username
        self.db_password = db_password
        self.db_host = db_host
        self.mongo_port = mongo_port
        self.tls_cert = tls_cert
        self.database = database
        self.collection = collection


# The Record object is used to query the db and delete records using a key and value pair
class Record:
    def __init__(self, key: str, value: any):
        self.key = key
        self.value = value


# This InsertRecord object is used to insert one record into the db
class InsertRecord:
    def __init__(self, value: any):
        self.value = value


# This object is an array of InsertRecord objects that can be used to insert multiple records in the db
class InsertRecordList:
    def __init__(self, record_array: list[InsertRecord]):
        self.record = record_array


# This object is an array of Record objects to delete many records from the db
class DeleteRecordList:
    def __init__(self, record_array: list[Record]):
        self.record = record_array


# These keywords are used with robot to be able to add any value type into a python record object
@keyword("Create Mongo connection object")
def create_connection_object(db_username, db_password, db_host, mongo_port, tls_cert, database, collection):
    return MongoConnectionDetails(db_username, db_password, db_host, mongo_port, tls_cert, database, collection)


@keyword("Create Record object")
def create_record(key: str, value: any):
    return Record(key, value)


@keyword("Create InsertRecord object")
def create_insert_record(value: any):
    return InsertRecord(value)


@keyword("Create InsertRecordList object")
def create_insert_record_list(value: any):
    return InsertRecordList(value)


@keyword("Create DeleteRecordList object")
def create_delete_record_list(value: any):
    return DeleteRecordList(value)


# Client is closed in the query/update functions
@not_keyword
def connect_to_documentdb(db_connection: MongoConnectionDetails):
    db_url = (
        "mongodb://"
        + db_connection.db_username
        + ":"
        + db_connection.db_password
        + "@"
        + db_connection.db_host
        + ":"
        + db_connection.mongo_port
        + "/?authMechanism=SCRAM-SHA-1"
        + "&"
        + "tls=true&tlsCAFile="
        + db_connection.tls_cert
        + "&"
        + "retryWrites=false"
    )

    client = pymongo.MongoClient(db_url)

    db = client.get_database(db_connection.database)

    col = db.get_collection(db_connection.collection)

    return client, col


# Passes in the Record object
@keyword("Find one record from mongo")
def find_one_mongo_field(record: Record, db_connection: MongoConnectionDetails):
    client, col = connect_to_documentdb(db_connection)

    record = col.find_one({record.key: record.value})

    print(record)

    client.close()

    return record


# Passes in the InsertRecord object
@keyword("Insert a record to a mongo db collection")
def insert_record_to_mongo(record: InsertRecord, db_connection: MongoConnectionDetails):
    client, col = connect_to_documentdb(db_connection)

    rec_id1 = col.insert_one(record.value)

    print("Data inserted with record ids", rec_id1)

    client.close()


# Passes in the InsertRecordList object
@keyword("Insert multiple records into mongo db collection")
def insert_multiple_records_to_mongo(records_list: InsertRecordList, db_connection: MongoConnectionDetails):
    client, col = connect_to_documentdb(db_connection)

    record_list = []

    for record in records_list.record:
        record_list.append(record.value)

    col.insert_many(record_list)

    print("Data inserted with record ids", record_list)

    client.close()


# Passes in the Record object
@keyword("Delete a record in mongodb collection")
def delete_record(record: Record, db_connection: MongoConnectionDetails):
    client, col = connect_to_documentdb(db_connection)

    col.delete_one({record.key: record.value})

    print("Data deleted with record id", record.key, record.value)

    client.close()


# Passes in the DeleteRecordList object
@keyword("Delete multiple records in mongodb collection")
def delete_multiple_records(records_list: DeleteRecordList, db_connection: MongoConnectionDetails):
    i = 0
    while len(records_list.record) > i:
        delete_record(records_list.record[i], db_connection)
        i += 1
