from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class EnumVehicleWakeUpReason(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_WAKE_UP_REASON_UNSPECIFIED: _ClassVar[EnumVehicleWakeUpReason]
    ENUM_WAKE_UP_REASON_INLET_HEATING: _ClassVar[EnumVehicleWakeUpReason]
    ENUM_WAKE_UP_REASON_CHARGING: _ClassVar[EnumVehicleWakeUpReason]
    ENUM_WAKE_UP_REASON_BATTERY_PRECONDITIONING: _ClassVar[EnumVehicleWakeUpReason]
    ENUM_WAKE_UP_REASON_CALCULATE_WAKE_UP_TIMES: _ClassVar[EnumVehicleWakeUpReason]
ENUM_WAKE_UP_REASON_UNSPECIFIED: EnumVehicleWakeUpReason
ENUM_WAKE_UP_REASON_INLET_HEATING: EnumVehicleWakeUpReason
ENUM_WAKE_UP_REASON_CHARGING: EnumVehicleWakeUpReason
ENUM_WAKE_UP_REASON_BATTERY_PRECONDITIONING: EnumVehicleWakeUpReason
ENUM_WAKE_UP_REASON_CALCULATE_WAKE_UP_TIMES: EnumVehicleWakeUpReason
