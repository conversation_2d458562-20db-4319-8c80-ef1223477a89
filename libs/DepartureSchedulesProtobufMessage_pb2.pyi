import EnumStatus_pb2 as _EnumStatus_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnumEventTimeType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_EVENT_TIME_TYPE_UNSPECIFIED: _ClassVar[EnumEventTimeType]
    ENUM_EVENT_TIME_TYPE_BEGIN: _ClassVar[EnumEventTimeType]
    ENUM_EVENT_TIME_TYPE_END: _ClassVar[EnumEventTimeType]

class EnumSeatClimateIntensity(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_OFF: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5: _ClassVar[EnumSeatClimateIntensity]

class EnumSeatClimateArea(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED: _ClassVar[EnumSeatClimateArea]
    ENUM_SEAT_CLIMATE_AREA_ALL: _ClassVar[EnumSeatClimateArea]
    ENUM_SEAT_CLIMATE_AREA_CUSHION: _ClassVar[EnumSeatClimateArea]
    ENUM_SEAT_CLIMATE_AREA_SQUAB: _ClassVar[EnumSeatClimateArea]

class EnumSeatClimateState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED: _ClassVar[EnumSeatClimateState]
    ENUM_SEAT_CLIMATE_STATE_ON: _ClassVar[EnumSeatClimateState]
    ENUM_SEAT_CLIMATE_STATE_OFF: _ClassVar[EnumSeatClimateState]
    ENUM_SEAT_CLIMATE_STATE_INHIBIT: _ClassVar[EnumSeatClimateState]

class EnumHSWTemperatureLevel(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED: _ClassVar[EnumHSWTemperatureLevel]
    ENUM_HSW_TEMPERATURE_LEVEL_OFF: _ClassVar[EnumHSWTemperatureLevel]
    ENUM_HSW_TEMPERATURE_LEVEL_1: _ClassVar[EnumHSWTemperatureLevel]
    ENUM_HSW_TEMPERATURE_LEVEL_2: _ClassVar[EnumHSWTemperatureLevel]

class EnumWakeUpReason(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_WAKE_UP_REASON_UNSPECIFIED: _ClassVar[EnumWakeUpReason]
    ENUM_WAKE_UP_REASON_INLET_HEATING: _ClassVar[EnumWakeUpReason]
    ENUM_WAKE_UP_REASON_CHARGING: _ClassVar[EnumWakeUpReason]
    ENUM_WAKE_UP_REASON_BATTERY_PRECONDITIONING: _ClassVar[EnumWakeUpReason]

class EnumSeatSelection(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_SELECTION_UNSPECIFIED: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_LEFT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_LEFT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_LEFT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_REAR_ALL: _ClassVar[EnumSeatSelection]
ENUM_EVENT_TIME_TYPE_UNSPECIFIED: EnumEventTimeType
ENUM_EVENT_TIME_TYPE_BEGIN: EnumEventTimeType
ENUM_EVENT_TIME_TYPE_END: EnumEventTimeType
ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_OFF: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_AREA_ALL: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_AREA_CUSHION: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_AREA_SQUAB: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED: EnumSeatClimateState
ENUM_SEAT_CLIMATE_STATE_ON: EnumSeatClimateState
ENUM_SEAT_CLIMATE_STATE_OFF: EnumSeatClimateState
ENUM_SEAT_CLIMATE_STATE_INHIBIT: EnumSeatClimateState
ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED: EnumHSWTemperatureLevel
ENUM_HSW_TEMPERATURE_LEVEL_OFF: EnumHSWTemperatureLevel
ENUM_HSW_TEMPERATURE_LEVEL_1: EnumHSWTemperatureLevel
ENUM_HSW_TEMPERATURE_LEVEL_2: EnumHSWTemperatureLevel
ENUM_WAKE_UP_REASON_UNSPECIFIED: EnumWakeUpReason
ENUM_WAKE_UP_REASON_INLET_HEATING: EnumWakeUpReason
ENUM_WAKE_UP_REASON_CHARGING: EnumWakeUpReason
ENUM_WAKE_UP_REASON_BATTERY_PRECONDITIONING: EnumWakeUpReason
ENUM_SEAT_SELECTION_UNSPECIFIED: EnumSeatSelection
ENUM_SEAT_SELECTION_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_LEFT: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_LEFT: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_LEFT: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_REAR_ALL: EnumSeatSelection

class DepartureTaskPreconditioning(_message.Message):
    __slots__ = ["precondition_target_temperature", "seat_climate_zone", "hsw_temperature_level"]
    PRECONDITION_TARGET_TEMPERATURE_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_ZONE_FIELD_NUMBER: _ClassVar[int]
    HSW_TEMPERATURE_LEVEL_FIELD_NUMBER: _ClassVar[int]
    precondition_target_temperature: float
    seat_climate_zone: _containers.RepeatedCompositeFieldContainer[SeatClimateZoneState]
    hsw_temperature_level: EnumHSWTemperatureLevel
    def __init__(self, precondition_target_temperature: _Optional[float] = ..., seat_climate_zone: _Optional[_Iterable[_Union[SeatClimateZoneState, _Mapping]]] = ..., hsw_temperature_level: _Optional[_Union[EnumHSWTemperatureLevel, str]] = ...) -> None: ...

class DepartureTaskCabinClean(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class DepartureTask(_message.Message):
    __slots__ = ["preconditioning", "cabin_clean"]
    PRECONDITIONING_FIELD_NUMBER: _ClassVar[int]
    CABIN_CLEAN_FIELD_NUMBER: _ClassVar[int]
    preconditioning: DepartureTaskPreconditioning
    cabin_clean: DepartureTaskCabinClean
    def __init__(self, preconditioning: _Optional[_Union[DepartureTaskPreconditioning, _Mapping]] = ..., cabin_clean: _Optional[_Union[DepartureTaskCabinClean, _Mapping]] = ...) -> None: ...

class DepartureEvent(_message.Message):
    __slots__ = ["departure_time", "departure_tasks"]
    DEPARTURE_TIME_FIELD_NUMBER: _ClassVar[int]
    DEPARTURE_TASKS_FIELD_NUMBER: _ClassVar[int]
    departure_time: int
    departure_tasks: _containers.RepeatedCompositeFieldContainer[DepartureTask]
    def __init__(self, departure_time: _Optional[int] = ..., departure_tasks: _Optional[_Iterable[_Union[DepartureTask, _Mapping]]] = ...) -> None: ...

class WakeUpRequestDetails(_message.Message):
    __slots__ = ["event_time_type", "event_time", "event_duration_seconds", "wakeup_reason"]
    EVENT_TIME_TYPE_FIELD_NUMBER: _ClassVar[int]
    EVENT_TIME_FIELD_NUMBER: _ClassVar[int]
    EVENT_DURATION_SECONDS_FIELD_NUMBER: _ClassVar[int]
    WAKEUP_REASON_FIELD_NUMBER: _ClassVar[int]
    event_time_type: EnumEventTimeType
    event_time: int
    event_duration_seconds: int
    wakeup_reason: EnumWakeUpReason
    def __init__(self, event_time_type: _Optional[_Union[EnumEventTimeType, str]] = ..., event_time: _Optional[int] = ..., event_duration_seconds: _Optional[int] = ..., wakeup_reason: _Optional[_Union[EnumWakeUpReason, str]] = ...) -> None: ...

class WakeUpResponseDetails(_message.Message):
    __slots__ = ["result", "start_time_utc", "start_time_localtime", "end_time_utc", "end_time_localtime"]
    RESULT_FIELD_NUMBER: _ClassVar[int]
    START_TIME_UTC_FIELD_NUMBER: _ClassVar[int]
    START_TIME_LOCALTIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_UTC_FIELD_NUMBER: _ClassVar[int]
    END_TIME_LOCALTIME_FIELD_NUMBER: _ClassVar[int]
    result: _EnumStatus_pb2.EnumStatus
    start_time_utc: int
    start_time_localtime: int
    end_time_utc: int
    end_time_localtime: int
    def __init__(self, result: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ..., start_time_utc: _Optional[int] = ..., start_time_localtime: _Optional[int] = ..., end_time_utc: _Optional[int] = ..., end_time_localtime: _Optional[int] = ...) -> None: ...

class SetDepartureScheduleRequest(_message.Message):
    __slots__ = ["departure_events"]
    DEPARTURE_EVENTS_FIELD_NUMBER: _ClassVar[int]
    departure_events: _containers.RepeatedCompositeFieldContainer[DepartureEvent]
    def __init__(self, departure_events: _Optional[_Iterable[_Union[DepartureEvent, _Mapping]]] = ...) -> None: ...

class SetDepartureScheduleResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _EnumStatus_pb2.EnumStatus
    def __init__(self, status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...) -> None: ...

class SetWakeUpTimesRequest(_message.Message):
    __slots__ = ["wakeup_details"]
    WAKEUP_DETAILS_FIELD_NUMBER: _ClassVar[int]
    wakeup_details: _containers.RepeatedCompositeFieldContainer[WakeUpRequestDetails]
    def __init__(self, wakeup_details: _Optional[_Iterable[_Union[WakeUpRequestDetails, _Mapping]]] = ...) -> None: ...

class SetWakeUpTimesResponse(_message.Message):
    __slots__ = ["status", "wakeup_resp_details"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    WAKEUP_RESP_DETAILS_FIELD_NUMBER: _ClassVar[int]
    status: _EnumStatus_pb2.EnumStatus
    wakeup_resp_details: _containers.RepeatedCompositeFieldContainer[WakeUpResponseDetails]
    def __init__(self, status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ..., wakeup_resp_details: _Optional[_Iterable[_Union[WakeUpResponseDetails, _Mapping]]] = ...) -> None: ...

class ClearChargingWakeUpTimesRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class ClearChargingWakeUpTimesResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _EnumStatus_pb2.EnumStatus
    def __init__(self, status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ...) -> None: ...

class GetNextDepartureEventRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetNextDepartureEventResponse(_message.Message):
    __slots__ = ["status", "departure_event"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    DEPARTURE_EVENT_FIELD_NUMBER: _ClassVar[int]
    status: _EnumStatus_pb2.EnumStatus
    departure_event: DepartureEvent
    def __init__(self, status: _Optional[_Union[_EnumStatus_pb2.EnumStatus, str]] = ..., departure_event: _Optional[_Union[DepartureEvent, _Mapping]] = ...) -> None: ...

class SeatClimateOperation(_message.Message):
    __slots__ = ["seat_area", "seat_state", "seat_climate_intensity"]
    SEAT_AREA_FIELD_NUMBER: _ClassVar[int]
    SEAT_STATE_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    seat_area: EnumSeatClimateArea
    seat_state: EnumSeatClimateState
    seat_climate_intensity: EnumSeatClimateIntensity
    def __init__(self, seat_area: _Optional[_Union[EnumSeatClimateArea, str]] = ..., seat_state: _Optional[_Union[EnumSeatClimateState, str]] = ..., seat_climate_intensity: _Optional[_Union[EnumSeatClimateIntensity, str]] = ...) -> None: ...

class SeatClimateZoneState(_message.Message):
    __slots__ = ["seat_selection", "seat_operation", "seat_climate_intensity"]
    SEAT_SELECTION_FIELD_NUMBER: _ClassVar[int]
    SEAT_OPERATION_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    seat_selection: EnumSeatSelection
    seat_operation: _containers.RepeatedCompositeFieldContainer[SeatClimateOperation]
    seat_climate_intensity: EnumSeatClimateIntensity
    def __init__(self, seat_selection: _Optional[_Union[EnumSeatSelection, str]] = ..., seat_operation: _Optional[_Iterable[_Union[SeatClimateOperation, _Mapping]]] = ..., seat_climate_intensity: _Optional[_Union[EnumSeatClimateIntensity, str]] = ...) -> None: ...
