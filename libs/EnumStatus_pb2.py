# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: EnumStatus.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x45numStatus.proto\x12\x13jlr.protobuf.common*\x98\x03\n\nEnumStatus\x12\x1b\n\x17\x45NUM_STATUS_UNSPECIFIED\x10\x00\x12\x12\n\x0e\x45NUM_STATUS_OK\x10\x01\x12\x1d\n\x19\x45NUM_STATUS_DATA_DEGRADED\x10\x02\x12\x1f\n\x1b\x45NUM_STATUS_DATA_UNRELIABLE\x10\x03\x12 \n\x1c\x45NUM_STATUS_DATA_UNAVAILABLE\x10\x04\x12+\n\'ENUM_STATUS_ERROR_INVALID_SERVICE_STATE\x10\x05\x12+\n\'ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE\x10\x06\x12/\n+ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION\x10\x07\x12)\n%ENUM_STATUS_ERROR_MISSING_INPUT_FIELD\x10\x08\x12)\n%ENUM_STATUS_ERROR_INVALID_INPUT_FIELD\x10\t\x12\x16\n\x12\x45NUM_STATUS_NOT_OK\x10\nB?\n;com.jaguarlandrover.commandandcontrolprotobuflibrary.sharedP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'EnumStatus_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n;com.jaguarlandrover.commandandcontrolprotobuflibrary.sharedP\001'
  _globals['_ENUMSTATUS']._serialized_start=42
  _globals['_ENUMSTATUS']._serialized_end=450
# @@protoc_insertion_point(module_scope)
