import boto3
import boto3.dynamodb
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
import traceback
from robot.api.deco import keyword
import os


def connect_to_dynamodb():
    try:
        if "GITLAB_CI" in os.environ:
            session = boto3.session.Session()
        else:
            session = boto3.session.Session(profile_name=os.environ.get("AWS_PROFILE"))

        dynamodb = session.resource(service_name="dynamodb", region_name="eu-west-2")

    except ConnectionError as ce:
        print("Database Connection error" + str(ce) + ":: " + str(ce))
        return -1

    except Exception:
        print("Database connection FAILED")
        traceback.print_exc()
        return -1

    print("Connected to DynamoDB")
    return dynamodb


@keyword("Check if item exists in dynamoDB table")
def check_item_exists(table, key):
    try:
        # Create DynamoDB resource
        dynamodb = connect_to_dynamodb()
        table = dynamodb.Table(table)
        # Attempt to get the item
        response = table.get_item(Key=key)
        # Check if 'Item' is in response
        return "Item" in response
    except ClientError as e:
        print(f"An error occurred: {e.response['Error']['Message']}")
        return False


# Delete the item in table
@keyword("Delete item from dynamoDB table")
def delete_row(table, key):

    # connect to DB
    dynamodb = connect_to_dynamodb()

    # Specify the table name
    table = dynamodb.Table(table)

    # Delete
    response = table.delete_item(Key=key)
    return response


@keyword("Delete all items from dynamoDB table by key")
def delete_all_items_by_key(table_name, partition_key_name, partition_key_value, sort_key_name=None):
    dynamodb = connect_to_dynamodb()
    table = dynamodb.Table(table_name)

    # Query all items with the given partition key
    response = table.query(KeyConditionExpression=Key(partition_key_name).eq(partition_key_value))

    items = response.get("Items", [])
    print(f"Found {len(items)} items with {partition_key_name} = {partition_key_value}")

    # Delete each item using its full key
    for item in items:
        key = {partition_key_name: item[partition_key_name]}
        if sort_key_name:
            key[sort_key_name] = item[sort_key_name]
        table.delete_item(Key=key)
        print(f"Deleted item: {key}")

    return f"Deleted {len(items)} items with {partition_key_name} = {partition_key_value}"


@keyword("Write data bundle to dynamoDB table")
def write_data_bundle_to_table(table_name, data_bundles):
    try:
        dynamodb = connect_to_dynamodb()
        table = dynamodb.Table(table_name)

        if not data_bundles:
            print("Data bundle is empty.")
            return None

        total_written = 0
        for bundle_name, items in data_bundles.items():
            print(f"Processing bundle: {bundle_name} with {len(items)} items")
            for item in items:
                print(f"Writing item: {item}")
                table.put_item(Item=item)
                total_written += 1
                print(f"Item successfully written to {table_name}")

        print(f"Total items written: {total_written}")
        return True
    except ClientError as e:
        print(f"Failed to write item: {e.response['Error']['Message']}")
        return None
    except Exception:
        print("Unexpected error occurred while writing item:")
        traceback.print_exc()
        return None
