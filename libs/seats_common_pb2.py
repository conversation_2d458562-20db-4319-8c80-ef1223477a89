# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: seats_common.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12seats_common.proto\x12\x0cseats_common*\xe1\x04\n\x11\x45numSeatSelection\x12#\n\x1f\x45NUM_SEAT_SELECTION_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x45NUM_SEAT_SELECTION_ALL\x10\x01\x12&\n\"ENUM_SEAT_SELECTION_FIRST_ROW_LEFT\x10\x02\x12\'\n#ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT\x10\x03\x12(\n$ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE\x10\x04\x12%\n!ENUM_SEAT_SELECTION_FIRST_ROW_ALL\x10\x05\x12\'\n#ENUM_SEAT_SELECTION_SECOND_ROW_LEFT\x10\x06\x12(\n$ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT\x10\x07\x12)\n%ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE\x10\x08\x12&\n\"ENUM_SEAT_SELECTION_SECOND_ROW_ALL\x10\t\x12&\n\"ENUM_SEAT_SELECTION_THIRD_ROW_LEFT\x10\n\x12\'\n#ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT\x10\x0b\x12(\n$ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE\x10\x0c\x12%\n!ENUM_SEAT_SELECTION_THIRD_ROW_ALL\x10\r\x12 \n\x1c\x45NUM_SEAT_SELECTION_REAR_ALL\x10\x0e\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'seats_common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_ENUMSEATSELECTION']._serialized_start=37
  _globals['_ENUMSEATSELECTION']._serialized_end=646
# @@protoc_insertion_point(module_scope)
