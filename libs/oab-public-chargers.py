from robot.api.deco import keyword
import commonUtils


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"


serviceUrl = "oneapp-backend-url-2"


# POST remote start public charger
@keyword("remote start public charger")
def remote_start_public_charger(user_email, user_password, evseId, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/public-chargers/" + evseId + "/commands/start"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]
