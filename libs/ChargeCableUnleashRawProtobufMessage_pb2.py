# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ChargeCableUnleashRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n*ChargeCableUnleashRawProtobufMessage.proto\x12\x18jlr.protobuf.chargecable\x1a\x10\x45numStatus.proto"m\n\x1eSetChargeCableOperationRequest\x12K\n\x0f\x63harge_cable_op\x18\x01 \x01(\x0e\x32\x32.jlr.protobuf.chargecable.EnumChargeCableOperation"R\n\x1fSetChargeCableOperationResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\xbc\x01\n\x18\x45numChargeCableOperation\x12+\n\'ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED\x10\x00\x12&\n"ENUM_CHARGE_CABLE_OPERATION_UNLOCK\x10\x01\x12$\n ENUM_CHARGE_CABLE_OPERATION_LOCK\x10\x02\x12%\n!ENUM_CHARGE_CABLE_OPERATION_ERROR\x10\x03\x42j\<EMAIL>$ChargeCableUnleashRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "ChargeCableUnleashRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\<EMAIL>$ChargeCableUnleashRawProtobufMessageP\001"
    _globals["_ENUMCHARGECABLEOPERATION"]._serialized_start = 286
    _globals["_ENUMCHARGECABLEOPERATION"]._serialized_end = 474
    _globals["_SETCHARGECABLEOPERATIONREQUEST"]._serialized_start = 90
    _globals["_SETCHARGECABLEOPERATIONREQUEST"]._serialized_end = 199
    _globals["_SETCHARGECABLEOPERATIONRESPONSE"]._serialized_start = 201
    _globals["_SETCHARGECABLEOPERATIONRESPONSE"]._serialized_end = 283
# @@protoc_insertion_point(module_scope)
