from robot.api.deco import keyword
import commonUtils
import random as r

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
serviceUrl = "oneapp-backend-url"


@keyword("invalid path")
def invalid_path(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    print(url)

    endpoint = url + "/me/invalid"

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


def generate_phone_number():
    # import module
    ph_no = []

    # the first number should be 0
    ph_no.append(r.randint(0, 0))

    # the for loop is used to append the other 10 numbers.
    # the other 9 numbers can be in the range of 0 to 9.
    for i in range(1, 11):
        ph_no.append(r.randint(0, 9))

    # printing the number
    for i in ph_no:
        print(i, end="")
