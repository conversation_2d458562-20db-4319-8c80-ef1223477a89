import requests
import json
import uuid
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig

from commonUtils import get_keycloak_token

systemVarFile = "../variables/system.yml"
contentType = "application/json"
authType = "Bearer "
headers = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot"}

id = "notSet"


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "config-url")


def set_id(metaDataId):
    global id
    id = metaDataId


def get_id():
    return id


@keyword("Get Configs From Configman")
def get_configs_from_configman(user, pw, auth=True):
    token = get_keycloak_token(user, pw, auth)

    header = {"Authorization": authType + token}

    url = get_url()
    getMeta = url + "/api/v1/config/getmeta"

    response = requests.request("GET", getMeta, headers=header)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return "No response body returned", response.status_code


@keyword("Get Configs From Configman By Id")
def get_configs_from_configman_by_id(configId, user, pw, auth=True):
    token = get_keycloak_token(user, pw, auth)

    header = {"Authorization": authType + token}
    url = get_url()
    getbyid = url + "/api/v1/config/getbyid?id="

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", getbyid + configId, headers=header)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return "No response body returned", response.status_code


@keyword("Send Config")
def send_config(configId, vin, user, pw, auth=True):
    token = get_keycloak_token(user, pw, auth)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": user,
        "Authorization": authType + token,
    }

    payload = json.dumps({"configurationID": configId, "vins": [vin]})

    url = get_url()
    send = url + "/api/v1/config/send"

    response = requests.request("POST", send, headers=header, data=payload)
    return response.text, response.status_code


@keyword("Create Config")
def create_config(data, user, pw, endpoint):
    token = get_keycloak_token(user, pw)

    f = open(data, "r")
    y = f.read()
    dataFile = json.loads(y)
    myuuid = str(uuid.uuid4())

    if dataFile.get("name") == "TEST":
        newData = {"name": myuuid, "description": myuuid}
        dataFile.update(newData)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    url = get_url()
    ruleset = url + "/" + endpoint
    print(ruleset)

    payload = json.dumps(dataFile)
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("POST", ruleset, headers=header, data=payload)

    if response.status_code == 201:
        s = str(response.text)
        y = json.loads(s)
        configId = y["id"]
        set_id(y["id"])
        return response.text, response.status_code, configId
    else:
        configId = -1
        return response.text, response.status_code, configId


@keyword("Delete Config")
def delete_config(user, pw):
    token = get_keycloak_token(user, pw)

    header = {"Authorization": authType + token}

    url = get_url()
    deleteConfig = url + "/api/v1/config/deletebyid?id="

    configId = get_id()

    response = requests.request("DELETE", deleteConfig + configId, headers=header)
    return response.text, response.status_code


# Ashs api calls for his e2e, could we remove some of these?


@keyword("Delete Config e2e")
def delete_config_e2e(configId, user, pw):
    token = get_keycloak_token(user, pw)

    header = {"Authorization": authType + token}

    url = get_url()
    deleteConfig = url + "/api/v1/config/deletebyid?id="
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("DELETE", deleteConfig + configId, headers=header)
    return response.text, response.status_code


@keyword("VA Control")
def va_control(vin, enable, user, pw, auth=True):
    """
    Enables / Disbles VA on a vehicle.

    Examples:
    | ${statusCode}= | VA Control | TSTLRGBL663001234 | true |

    """
    token = get_keycloak_token(user, pw, auth)
    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": user,
        "Authorization": authType + token,
    }
    payload = json.dumps({"vins": [vin], "controls": [{"name": "enableVA", "action": enable}]})

    url = get_url()
    vaControl = url + "/controls/va"

    response = requests.request("POST", vaControl, headers=header, data=payload)
    return response.status_code
