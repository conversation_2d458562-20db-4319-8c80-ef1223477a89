import commonUtils
from robot.api.deco import keyword

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"
serviceUrl = "oneapp-backend-url-2"


# GET find address
@keyword("Get find address")
def get_find_address(user_email, user_password, addressLocator, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/addresses" + addressLocator

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# GET retrieve address
@keyword("Get retrieve address")
def get_retrieve_address(user_email, user_password, addressId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/addresses/" + addressId

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]
