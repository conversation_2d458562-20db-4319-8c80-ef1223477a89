import requests
import json
import forgeRock
import time
import getUserId
import os
import random
from pyaml_env import parse_config, BaseConfig
from robot.api.deco import keyword, not_keyword
from robot.api import logger
from requests.adapters import HTTPAdapter, Retry

systemVarFile = "../variables/system.yml"


@not_keyword
def get_url(url):
    systemVars = BaseConfig(parse_config(systemVarFile))
    return getattr(systemVars, url)


"""
    Provides generic common methods used across multiple libs
"""


def get_keycloak_token(user, pw, auth=True):
    """
    get a valid keycloak token, with optional switch to return a dummy token
    This function returns a token using a username and password
    for authorized access to the endpoints

    Examples:
    | get_token | username | password |
    """
    if not auth:
        return "abcdef12345"
    system_vars = BaseConfig(parse_config(systemVarFile))
    kcUrl = getattr(system_vars, "app-kcUrl")
    realm = getattr(system_vars, "config-man-realm")
    client_id = getattr(system_vars, "client_id")
    token = kcUrl + "/" + realm

    kcHeader = {"Content-Type": "application/x-www-form-urlencoded"}

    payload = "client_id=" + client_id + "&grant_type=password&username=" + user + "&password=" + pw

    response = requests.request("POST", token, headers=kcHeader, data=payload)

    y = json.loads(response.text)
    logger.debug(y["access_token"])
    logger.debug(time.time())
    time.sleep(2)  # adding to see if the certificate isn't quite valid when we try to use it - TimF
    return y["access_token"]


"""send a request with json payload and return content body and status code"""


def send_request_with_json_payload(user_email, user_password, requestBody, endpoint, approov, auth, request_type, CONTENT_TYPE, AUTH_TYPE):
    token = getToken(user_email, user_password, auth)
    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    decodedToken = getUserId.decode_jwt_base64(token)

    # Check if requestBody is a dictionary (JSON data) or a file path
    if isinstance(requestBody, dict):
        jsonRequestBody = requestBody  # Use the dictionary directly
    elif isinstance(requestBody, str):  # If it's a file path, read the JSON file
        with open(requestBody, "r") as file:
            jsonRequestBody = json.load(file)
    else:
        raise TypeError("requestBody must be either a file path (str) or a dictionary (dict)")

    # Convert JSON body to string format
    payload = json.dumps(jsonRequestBody)

    # Send request
    response = requests.request(request_type, endpoint, headers=headers, data=payload)

    print(f"Endpoint: {endpoint}")
    print(f"Response Status Code: {response.status_code}")
    print(f"Response Headers: {response.headers}")
    print(f"Response Body: {response.text}")
    return response.text, response.status_code, decodedToken


"""send a request with an updated json payload and return content body, payload and status code"""


def send_request_with_json_updated_payload(
    user_email, user_password, requestBody, array1, array2, value, endpoint, approov, auth, request_type, CONTENT_TYPE, AUTH_TYPE
):
    token = getToken(user_email, user_password, auth)

    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    openJsonData = open(requestBody, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody[array1][array2] = [value]

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(request_type, endpoint, headers=headers, data=payload)
    print(endpoint)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code, payload


"""send a request with params and return content body and status code"""


def send_request_with_params(user_email, user_password, endpoint, approov, auth, request_type, CONTENT_TYPE, AUTH_TYPE, params):
    token = getToken(user_email, user_password, auth)

    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(request_type, endpoint, headers=headers, params=params)
    print(endpoint)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


def send_request(url, payload, CONTENT_TYPE, AUTH_TYPE):
    forgeRockUsername = os.environ.get("HSEATS_FR_EMAIL_PREPROD")
    forgeRockPassword = os.environ.get("HSEATS_FR_PASSWORD_PREPROD")
    token = forgeRock.get_token(forgeRockUsername, forgeRockPassword)

    headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Authorization": AUTH_TYPE + token, "Attestation": os.environ["ApproovToken"]}

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("POST", url, data=payload, headers=headers)
    print(url)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


"""Send a Get request and return content body and status code"""


@not_keyword
def get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE):
    token = getToken(user_email, user_password, auth)

    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", endpoint, headers=headers)
    print(endpoint)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


"""Send a Get request with params and return content body and status code"""


@not_keyword
def get_request_with_params(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE, params):
    token = getToken(user_email, user_password, auth)

    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", endpoint, headers=headers, params=params)
    print(endpoint)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


""" send put request and return response text and status code"""


@not_keyword
def send_put_request(user_email, user_password, endpoint, approov, auth, request_type, CONTENT_TYPE, AUTH_TYPE, payload):
    token = getToken(user_email, user_password, auth)

    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)
    payload = json.dumps(payload)
    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(request_type, endpoint, headers=headers, data=payload)
    return response.text, response.status_code


"""get expected response and return it to calling method"""


@not_keyword
def delete_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE):
    token = getToken(user_email, user_password, auth)

    headers = addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("DELETE", endpoint, headers=headers)
    print(headers)
    print(endpoint)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


@keyword("load expected response")
def get_expected_response(expected):
    openJsonData = open(expected, "r")
    readJsonData = openJsonData.read()
    expectedResponse = json.loads(readJsonData)
    return expectedResponse


"""compare 2 json responses to see if they are the same"""


def compare_json_response(expected, actual):
    expectedResponse = get_expected_response(expected)
    actualResponse = json.loads(actual)
    print(expectedResponse)
    print(actualResponse)

    if expectedResponse == actualResponse:
        return True
    else:
        return False


@keyword("Delete json object")
def delete_json_object(response):
    if "legal_compliance" in response:
        del response["legal_compliance"]
    return response


@keyword("Count items returned")
def count_items_returned(response, expectedLength):
    item_dict = json.loads(response)
    length = len(item_dict)
    lengthString = str(length)
    expectedLengthString = str(expectedLength)
    print(length)
    print(expectedLength)
    if lengthString == expectedLengthString:
        return True
    else:
        return False


@keyword("add sleep")
def add_sleep(waitTime):
    time.sleep(waitTime)


def addHeader(approov, CONTENT_TYPE, AUTH_TYPE, token):
    if approov is True:
        print("approve is true")
        headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Authorization": AUTH_TYPE + token, "Attestation": os.environ["ApproovToken"]}
    else:

        print("approve is false")
        headers = {
            "accept": "*/*",
            "Content-Type": CONTENT_TYPE,
            "Authorization": AUTH_TYPE + token,
        }
    return headers


def getToken(user_email, user_password, auth):
    if auth is False:
        print("auth is false")
        token = os.environ["invalidToken"]
    else:
        print("auth is true")
        token = forgeRock.get_token(user_email, user_password)
    return token


def generate_uk_phone_number():
    """
    Generates a random UK mobile phone number in the format: 07XXX XXXXXX.
    """
    prefix = random.choice(["071", "072", "073", "074", "075", "076", "077", "078", "079"])
    suffix = random.randint(10000000, 99999999)  # Generates a 8-digit random number
    return f"{prefix}{suffix}"


def generate_uk_address_line1():
    """
    Generates a random UK address line 1, including house number and street name.
    """
    house_number = random.randint(1, 999)  # Random house number
    street_names = [
        "High Street",
        "Church Road",
        "Station Road",
        "Main Street",
        "Park Avenue",
        "Victoria Road",
        "London Road",
        "School Lane",
        "Market Street",
        "King's Road",
    ]

    street = random.choice(street_names)  # Randomly select a street name
    return f"{house_number} {street}"


def generate_uk_county():
    """
    Generates a random UK county from a broad selection of options.
    """
    counties = [
        "Merseyside",
        "Lancashire",
        "Cheshire",
        "Derbyshire",
        "Cumbria",
        "Nottinghamshire",
        "Lincolnshire",
        "Norfolk",
        "Suffolk",
        "Cambridgeshire",
        "Essex",
        "Kent",
        "East Sussex",
        "West Sussex",
        "Surrey",
        "Hampshire",
        "Dorset",
        "Somerset",
        "Wiltshire",
        "Gloucestershire",
        "Herefordshire",
        "Worcestershire",
        "Warwickshire",
        "Leicestershire",
        "Northamptonshire",
        "Bedfordshire",
        "Buckinghamshire",
        "Oxfordshire",
        "Berkshire",
        "Devon",
        "Cornwall",
        "Shropshire",
        "Staffordshire",
        "Durham",
        "Northumberland",
    ]

    return random.choice(counties)


def generate_uk_county_temp():
    """
    Generates a random UK county from a broad selection of options.
    """
    counties = [
        "Merseyside",
        "Lancashire",
        "Cheshire",
        "Derbyshire",
        "Cumbria",
        "Nottinghamshire",
        "Lincolnshire",
        "Norfolk",
        "Suffolk",
        "Cambridgeshire",
        "Essex",
        "Kent",
        "Staffordshire",
    ]

    return random.choice(counties)


def generate_uk_city():
    """
    Generates a random UK city from a diverse selection of options.
    """
    cities = [
        "London",
        "Manchester",
        "Birmingham",
        "Liverpool",
        "Leeds",
        "Glasgow",
        "Edinburgh",
        "Newcastle",
        "Bristol",
        "Sheffield",
        "Cardiff",
        "Belfast",
        "Nottingham",
        "Leicester",
        "Coventry",
        "Southampton",
        "Brighton",
        "Aberdeen",
        "Portsmouth",
        "Norwich",
        "York",
        "Stoke-on-Trent",
        "Dundee",
        "Swansea",
        "Exeter",
        "Reading",
        "Milton Keynes",
        "Cambridge",
        "Oxford",
        "Wolverhampton",
        "Plymouth",
        "Derby",
        "Carlisle",
        "Luton",
        "Bath",
        "Preston",
        "Hull",
        "Sunderland",
        "Lancaster",
        "Chelmsford",
        "Worcester",
    ]

    return random.choice(cities)


def generate_uk_postcode():
    """
    Generates a random UK postcode in common formats.
    """
    # Sample postcode areas and districts
    postcode_areas = ["B", "L", "M", "E", "SW", "NW", "NE", "SE", "W", "G", "CF", "NP", "BT", "AB", "PH", "FK"]
    district_numbers = [str(random.randint(1, 99)) for _ in range(len(postcode_areas))]  # Random district numbers
    inward_code = (
        f"{random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')}{random.randint(0, 9)}{random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')}"  # Random inward code
    )

    # Construct postcode
    postcode = f"{random.choice(postcode_areas)}{random.choice(district_numbers)} {inward_code}"

    return postcode


def generate_keycloak_token_CandP_Vroom(ClientId, ClientSecret, service):
    """
    Generates an access token for consent enable/revoke using Keycloak authentication.

    :param keyCloakUrl: Keycloak authentication URL.
    :param ClientId: Client ID for authentication.
    :param ClientSecret: Client Secret for authentication.
    :param service:  Consent And Permission / VRoom.
    :return: Access token as a string.
    """

    payload = {"client_id": ClientId, "client_secret": ClientSecret, "grant_type": "client_credentials"}

    if service == "ConsentAndPermission":
        url = get_url("keycloak-url-consent-permission")

    elif service == "VRoom":
        url = get_url("keycloak-url-vehicle-binding")
    try:
        response = requests.post(url, data=payload)
        response.raise_for_status()  # Raises an HTTPError for bad responses (4xx, 5xx)

        json_response = response.json()  # Automatically parses JSON response
        access_token = json_response.get("access_token")

        if not access_token:
            print("Error: Access token missing in response.")
            return None

        print(f"Access Token: {access_token}")
        return access_token

    except requests.exceptions.RequestException as e:
        print(f"API Request Failed: {e}")
        return None
    except json.JSONDecodeError:
        print("Error: Failed to parse JSON response.")
        return None


@not_keyword
def create_retry_request_session():
    session = requests.Session()
    retries = Retry(total=3, backoff_factor=1, status_forcelist=[500, 502, 503, 504], allowed_methods=False)
    session.mount("https://", HTTPAdapter(max_retries=retries))
    return session
