from robot.api.deco import keyword
import commonUtils
import os
import requests

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
serviceUrl = "oneapp-backend-url-2"


# GET FAQs data
def add_faqs_header(approov, CONTENT_TYPE, AUTH_TYPE, token):
    if approov is True:
        print("approve is true")
        headers = {
            "accept": "*/*",
            "Content-Type": CONTENT_TYPE,
            "Authorization": AUTH_TYPE + token,
            "Attestation": os.environ["ApproovToken"],
            "accept-language": "zz-TX",
        }
    else:
        print("approve is false")
        headers = {
            "accept": "*/*",
            "Content-Type": CONTENT_TYPE,
            "Authorization": AUTH_TYPE + token,
        }
    return headers


def send_faqs_with_params(user_email, user_password, endpoint, approov, auth, request_type, CONTENT_TYPE, AUTH_TYPE, params):
    token = commonUtils.getToken(user_email, user_password, auth)
    headers = add_faqs_header(approov, CONTENT_TYPE, AUTH_TYPE, token)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request(request_type, endpoint, headers=headers, params=params)
    print(endpoint)
    print(response)
    print(response.headers)
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


@keyword("Get all FAQ data")
def get_faqs(user_email, user_password, faqBrand, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + "/faqs"
    queryParams = {"brand": faqBrand, "path": "/faqs"}
    response = send_faqs_with_params(user_email, user_password, endpoint, approov, auth, "GET", CONTENT_TYPE, AUTH_TYPE, params=queryParams)
    print(response[0])
    return response[0], response[1]
