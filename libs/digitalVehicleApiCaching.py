#!/usr/bin python3
"""
this script contains functions to aid in
the testing of the scheduling service
"""
import csv
from datetime import datetime, timedelta, timezone
import json
import os
import pymongo
import requests
from pyaml_env import parse_config, BaseConfig
from robot.api.deco import keyword, not_keyword
import base64
import jwt
import uuid
import forgeRock

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"


# choose environment to run test in
SYSTEM_VAR_FILE_DEV = "../variables/system-dev.yml"
SYSTEM_VAR_FILE_PRE_PROD = "../variables/system-pre-prod.yml"

response_time_filename = "../apiResponseTimeData.csv"


@not_keyword
def get_var_from_file(var_file, var_name):
    """
    get a variable from a yml file.
    Example:
    | get_var_from_file
    | path to system.yml file
    | name of variable as defined in system.yml file
    """
    sys_vars = BaseConfig(parse_config(var_file))
    return getattr(sys_vars, var_name)


@keyword("load json file in digital vehicle api project")
def load_json_file(json_file_location):
    """
    This function loads a json file and returns a
    json data object.
    Examples:
    | load_json_file | ...location/file.json |
    """
    with open(json_file_location, encoding="utf-8") as file:
        json_object = json.load(file)
        print("\n", "json file from: ", json_file_location, "  \n")
    return json_object


@keyword("generate vehicle reg from json file")
def generate_vehicle_reg_from_json_file(json_file_location):
    """
    This generates a random vehicle registation number by changing the last
    digit of the reg number from 0 to 9.
    Examples:
    | generate_vehicle_reg_from_json_file
    | ...location/updateRegNumber.json |
    """
    request_body = load_json_file(json_file_location)

    # get current reg number
    current_reg_num = request_body["vehicleReg"]
    # update reg number
    int_last_char = int(current_reg_num[-1])

    if int_last_char >= 9:
        int_last_char = 0

    new_last_char = str(int_last_char + 1)
    new_reg_number = current_reg_num[:-1] + new_last_char

    request_body["vehicleReg"] = new_reg_number

    # update json file
    with open(json_file_location, "w") as file:
        json.dump(request_body, file, indent=2)
    return request_body


def get_forgerock_token_with_client_credentials(environment="dev"):
    """
    This generates a forgerock token for digital vehicle api
    using the auth certificate and private key.
    Examples:
    | get_forgerock_token_with_client_credentials
    | dev
    """
    private_key_location = os.getenv("forgerock_b2b_private_key")

    auth_path = (
        get_var_from_file(SYSTEM_VAR_FILE_DEV, "b2b_auth_path")
        if environment.lower() == "dev"
        else get_var_from_file(SYSTEM_VAR_FILE_PRE_PROD, "b2b_auth_path")
    )

    with open(private_key_location, "r") as key_file:
        private_key = key_file.read()

    # create a client assertion token
    assertionToken = jwt.encode(
        {
            "iss": "digital-vehicle-test",
            "sub": "digital-vehicle-test",
            "aud": auth_path,
            "exp": datetime.now(timezone.utc) + timedelta(minutes=15),
            "jti": str(uuid.uuid4()),
        },
        private_key,
        algorithm="PS256",
    )

    # client_assertion = json.dumps({"clientCredsAllClientAssertionToken": token})

    print(auth_path)
    payload = (
        "grant_type=client_credentials&client_id=digital-vehicle-test"
        + "&scope=urn%3Aiam2-mgd-v1%3Ascopes%3Avehicle%3Avehicle-data"
        + "&client_assertion="
        + assertionToken
        + "&client_assertion_type=urn%3Aietf%3Aparams%3Aoauth%3Aclient-assertion-type%3Ajwt-bearer"
    )

    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    response = requests.request("POST", auth_path, headers=headers, data=payload)
    y = json.loads(response.text)
    return y["access_token"]


def encode_to_base64(username, password):
    """
    This function is use do encode the keycloak username and password
    before it is used to get a keycloak token
    Examples:
    | encode_to_base64
    | keycloak username
    | keycloak password
    """
    # Combine username and password with a colon
    credentials = f"{username}:{password}"

    # Encode the credentials to bytes
    credentials_bytes = credentials.encode("utf-8")

    # Convert to Base64
    base64_bytes = base64.b64encode(credentials_bytes)

    # Convert Base64 bytes back to string
    base64_string = base64_bytes.decode("utf-8")

    return base64_string


def get_dv_keycloak_token(username, password, environment="dev", auth=True):
    """
    This function is use do get a keycloak token for dv api ingress path
    Examples:
    | get_dv_keycloak_token
    | keycloak username
    | keycloak password
    | dev or pre-prod
    | True
    """
    if not auth:
        return "abcdef12345"
    keycloak_request_token = encode_to_base64(username, password)

    url = (
        get_var_from_file(SYSTEM_VAR_FILE_PRE_PROD, "dv_caching_keycloak_url")
        if environment.lower() == "pre-prod"
        else get_var_from_file(SYSTEM_VAR_FILE_DEV, "dv_caching_keycloak_url")
    )

    print(url)

    kcHeader = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": "Basic " + keycloak_request_token,
    }

    payload = "grant_type=client_credentials"

    response = requests.request("POST", url, headers=kcHeader, data=payload)
    y = json.loads(response.text)
    return y["access_token"]


def get_dv_keycloak_token_for_vlc(username, password, environment="dev", auth=True):
    """
    This function is use do get a keycloak token digital vehicle state handler vlc api requests
    Examples:
    | get_dv_keycloak_token_for_vlc
    | keycloak username
    | keycloak password
    | dev or pre-prod
    | True
    """
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV
    url = get_var_from_file(SYSTEM_VAR_FILE, "dv_vlc_keycloak_url")
    client_id = get_var_from_file(SYSTEM_VAR_FILE, "dv_vlc_keycloak_client_id")
    payload = "client_id=" + client_id + "&grant_type=password&username=" + username + "&password=" + password
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    response = requests.request("POST", url, headers=headers, data=payload)

    y = json.loads(response.text)
    return y["access_token"]


@not_keyword
def record_api_response_time(url, elapsedTime):
    data = [url, datetime.now(), elapsedTime]
    with open(response_time_filename, mode="a", newline="") as file:
        writer = csv.writer(file)
        writer.writerow(data)


@keyword("send digital vehicle api request")
def send_digital_vehicle_api_request(
    username,
    password,
    command,
    base_url,
    vin_or_vuid,
    environment="dev",
    auth_method="forgerock",
):
    """
    This function is used to send get digital vehicle information via the caching service using APIs.
    Example:
    | send_digital_vehicle_api_request
    | auth username
    | auth password
    | get-vehicle-data or find-vin-by-uuid or identity-lookup or find-reg-by-uuid or update-reg-by-uuid
    | base_url
    | vin or uuid depending on api request
    | dev or pre-prod
    | forgerock or keycloak
    """

    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    # use customer forgerock for B2C access (gateway)
    # use Forgerock or keycloak for B2B access (ingress)
    if "vehicle-data-api" in base_url:
        # gateway path (B2C) forgerock only
        token = forgeRock.get_token(
            username,
            password,
            get_var_from_file(SYSTEM_VAR_FILE, "DV_API_CACHING_SCOPE"),
        )
        print("Auth: B2C-ForgeRock")
    else:  # ingress path (B2B) forgerock or keycloak
        token = (
            get_dv_keycloak_token(username, password, environment)
            if auth_method.lower() == "keycloak"
            else get_forgerock_token_with_client_credentials(environment)
        )

    print("token")
    print(token)

    # header
    vs_headers = {
        "accept": "*/*",
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token,
    }

    # choose which path to go through i.e. gateway or ingress
    url = (
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-gateway-endpoint")
        if "vehicle-data-api" in base_url
        else get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint")
    )

    # append endpoints
    url_vin = url + "/" + "vehicles" + "/" + vin_or_vuid + "/identity/vin"
    url_identity_lookup = url + "/" + "identity-lookup"
    url_reg_number = url + "/" + "vehicles" + "/" + vin_or_vuid + "/identity/reg-number"
    url_vehicles = url + "/" + "vehicles" + "/" + vin_or_vuid + "?include" + "=" + "IDENTITY,ASSETS,INVENTORY,POLICIES,APPLICATIONS,AUDIT"
    url_vehicles_customer = url + "/" + "vehicles" + "/" + vin_or_vuid + "?include" + "=" + "IDENTITY,ASSETS"

    match command.lower():
        case "find-vin-by-uuid":
            print(url_vin)
            print("command: ", command)

            response = requests.request("GET", url_vin, headers=vs_headers)
            # print("elapsed time in sec: ", response.elapsed.total_seconds())
            # record_api_response_time(url_vin, response.elapsed.total_seconds())
            response_body = json.loads(response.text)

        case "identity-lookup":
            request_body = load_json_file("../json-request-data/dv-api-caching/findByVinBody.json")
            request_body["id"] = vin_or_vuid

            print(url_identity_lookup)
            print("command: ", command)

            response = requests.request(
                "POST",
                url_identity_lookup,
                headers=vs_headers,
                data=json.dumps(request_body),
            )
            # print("elapsed time in sec: ", response.elapsed.total_seconds())
            # record_api_response_time(
            #     url_identity_lookup, response.elapsed.total_seconds()
            # )
            response_body = json.loads(response.text)

        case "find-reg-by-uuid":
            print(url_reg_number)
            print("command: ", command)

            response = requests.request(
                "GET",
                url_reg_number,
                headers=vs_headers,
            )
            # print("elapsed time in sec: ", response.elapsed.total_seconds())
            # record_api_response_time(url_reg_number, response.elapsed.total_seconds())
            response_body = json.loads(response.text)

        case "update-reg-by-uuid":
            update_reg_request = generate_vehicle_reg_from_json_file("../json-request-data/dv-api-caching/updateRegNumber.json")
            # send request
            print(url_reg_number)
            print("command: ", command)

            response = requests.request(
                "PATCH",
                url_reg_number,
                headers=vs_headers,
                data=json.dumps(update_reg_request),
            )
            # print("elapsed time in sec: ", response.elapsed.total_seconds())
            # record_api_response_time(url_reg_number, response.elapsed.total_seconds())
            response_body = {}

        case "get-vehicle-data":
            print("command: ", command)
            if "vehicle-data-api" in base_url:
                print(url_vehicles_customer)
                response = requests.request("GET", url_vehicles_customer, headers=vs_headers)
            else:
                print(url_vehicles)
                response = requests.request("GET", url_vehicles, headers=vs_headers)
            # print("elapsed time in sec: ", response.elapsed.total_seconds())
            # record_api_response_time(url_vehicles, response.elapsed.total_seconds())
            response_body = json.loads(response.text)

    print("Status: ", response.status_code)
    print(response.text)
    return response_body, response.status_code


@keyword("create a new vehicle for dv api test")
def create_new_vehicle_data_for_dv_api_test(kc_username, kc_password, vehicle_data_path, environment="dev", auth=True):
    """
    This function is used to create a new vehicle in digital vehicle via state handler vlc api.
    Example:
    | create_new_vehicle_data_for_dv_api_test
    | keycloak auth username
    | keycloak auth password
    | path to new vehicle json file
    | dev or pre-prod
    | True or False
    """
    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    if auth is not True:
        token = "abcdef12345"
    else:
        token = get_dv_keycloak_token_for_vlc(kc_username, kc_password, environment=environment)

    print("token")
    print(token)

    request_body = load_json_file(vehicle_data_path)
    request_body["uniqueId"] = str(uuid.uuid4())

    payload = json.dumps(request_body)

    url = get_var_from_file(SYSTEM_VAR_FILE, "dv-vlc-url") + "/" + "dv-state-handler/vehicles/create"

    headers = {"Content-Type": "application/json", "Authorization": "Bearer " + token}

    response = requests.request("POST", url, headers=headers, data=payload)
    response_body = json.loads(response.text)

    print("Status: ", response.status_code)
    print(response.text)
    return response_body, response.status_code


@keyword("delete an existing vehicle for dv api test")
def delete_existing_vehicle_data_by_vin_for_dv_api_test(kc_username, kc_password, vin, environment="dev", auth=True):
    """
    This function is used to delete an exisiting vehicle in digital vehicle via state handler vlc api.
    Example:
    | delete_existing_vehicle_data_by_vin_for_dv_api_test
    | keycloak auth username
    | keycloak auth password
    | vin
    | dev or pre-prod
    | True or False
    """
    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    if auth is not True:
        token = "abcdef12345"
    else:
        token = get_dv_keycloak_token_for_vlc(kc_username, kc_password, environment=environment)

    print("token")
    print(token)

    url = get_var_from_file(SYSTEM_VAR_FILE, "dv-vlc-url") + "/" + "dv-state-handler/vehicles/" + vin

    headers = {"Content-Type": "application/json", "Authorization": "Bearer " + token}

    response = requests.request("DELETE", url, headers=headers)
    # response_body = json.loads(response.text)

    print("Status: ", response.status_code)
    # print(response.text)
    return response.status_code


@keyword("partial_update_of data of existing vehicle for dv api test")
def partial_update_of_vehicle_data_by_vin_for_dv_api_test(kc_username, kc_password, vin, json_data, environment="dev", auth=True):
    """
    This function is used to update a field in an exisiting vehicle in digital vehicle via state handler vlc api.
    Example:
    | partial_update_of_vehicle_data_by_vin_for_dv_api_test
    | keycloak auth username
    | keycloak auth password
    | vin to search database
    | field to update in Json format
    | dev or pre-prod
    | True or False
    """
    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    if auth is not True:
        token = "abcdef12345"
    else:
        token = get_dv_keycloak_token_for_vlc(kc_username, kc_password, environment=environment)

    print("token")
    print(token)

    url = get_var_from_file(SYSTEM_VAR_FILE, "dv-vlc-url") + "/" + "dv-state-handler/vehicles/updateVehicle/" + vin

    print(url)

    headers = {"Content-Type": "application/json", "Authorization": "Bearer " + token}

    payload = json.dumps(json_data)

    response = requests.request("PATCH", url, headers=headers, data=payload)

    print("Status: ", response.status_code)
    return response.status_code


@keyword("get data of existing vehicle for dv api test via vlc")
def get_vehicle_data_via_vlc_for_dv_api_test(kc_username, kc_password, identifier, environment="dev", auth=True):
    """
    This function is used to get the digital vehicle of a vehicle via state handler vlc api.
    Example:
    | get_vehicle_data_via_vlc_for_dv_api_test
    | keycloak auth username
    | keycloak auth password
    | uuid or vin
    | dev or pre-prod
    | True or False
    """
    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    if auth is not True:
        token = "abcdef12345"
    else:
        token = get_dv_keycloak_token_for_vlc(kc_username, kc_password, environment=environment)

    print("token")
    print(token)

    url = get_var_from_file(SYSTEM_VAR_FILE, "dv-vlc-url") + "/" + "dv-state-handler/v2/vehicles?identifierType=VIN"

    print(url)

    headers = {"Content-Type": "application/json", "Authorization": "Bearer " + token}

    json_data = {
        "identifiers": [identifier],
        "inclusions": ["IDENTITY", "POLICIES", "APPLICATIONS", "ASSETS", "INVENTORY"],
    }

    payload = json.dumps(json_data)

    response = requests.request("POST", url, headers=headers, data=payload)

    response_body = json.loads(response.text)
    print("Status: ", response.status_code)
    print(response.text)
    return response_body, response.status_code


@keyword("connect to a collection in a mongodb databse for dv api test")
def connect_to_dv_database_collection(
    db_username,
    db_password,
    db_host,
    tls_cert,
    collections_name,
    database_name="digital-vehicle",
):
    mongo_port = "27017"

    db_url = (
        "mongodb://"
        + db_username
        + ":"
        + db_password
        + "@"
        + db_host
        + ":"
        + mongo_port
        + "/?authMechanism=DEFAULT"
        + "&"
        + "tls=true&tlsCAFile="
        + tls_cert
        + "&"
        + "retryWrites=false"
    )

    print(db_url)

    # Create a MongoDB client, open a connection to Amazon DocumentDB as
    # a replica set and specify the read preference as secondary preferred
    client = pymongo.MongoClient(db_url)

    # #the database to be used
    db = client.get_database(database_name)
    print("Connected to database: ", db)

    # #the collection to be used
    col = db.get_collection(collections_name)
    print("Connected to collections: ", col)
    return col, client


@keyword("delete digital vehicle from database for dv api test")
def delete_digital_vehicle_from_database(db_username, db_password, db_host, tls_cert, collections_name, query):
    """
    This function connect to delete a document in a collection in a mongodb database
    Example:
    | delete_digital_vehicle_from_database
    | username
    | password
    | host
    | tls certificate
    | collection name
    | query
    """
    col, client = connect_to_dv_database_collection(db_username, db_password, db_host, tls_cert, collections_name)

    y = col.delete_one(query)
    client.close()
    return y.deleted_count


@keyword("update registration in dv database for dv api test")
def update_registration_in_dv_database(db_username, db_password, db_host, tls_cert, collections_name, query):
    """
    This function connect to update a document in a collection in a mongodb database
    Example:
    | update_registration_in_dv_database
    | username
    | password
    | host
    | tls certificate
    | collection name
    | query
    """
    update_reg_request = generate_vehicle_reg_from_json_file("../json-request-data/dv-api-caching/updateRegNumber.json")
    print("new reg number:", update_reg_request["vehicleId"])

    new_reg = {"$set": {"identity.vehicleReg": str(update_reg_request["vehicleId"])}}

    col, client = connect_to_dv_database_collection(db_username, db_password, db_host, tls_cert, collections_name)

    y = col.update_one(query, new_reg)
    client.close()
    return y


@keyword("get digital vehicle from database")
def get_digital_vehicle_from_database(db_username, db_password, db_host, tls_cert, collections_name, query):
    """
    This function connect to get a document in a collection in a mongodb database
    Example:
    | get_digital_vehicle_from_database
    | username
    | password
    | host
    | tls certificate
    | collection name
    | query
    """
    col, client = connect_to_dv_database_collection(db_username, db_password, db_host, tls_cert, collections_name)

    # #Find the document that was previously written
    x = col.find_one(query)

    if x is None:
        x = {}

    client.close()
    return x


@keyword("send digital vehicle api request with invalid token")
def send_digital_vehicle_api_request_with_invalid_token(
    expired_token,
    base_url,
    environment="dev",
):
    """
    This function is used try to get vehicle information via the caching service with an invalid token.
    Example:
    | send_digital_vehicle_api_request_with_invalid_token
    | string of expired token
    | private or public
    | dev or pre-prod
    | gateway or ingress
    """

    print("token")
    print(expired_token)

    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    # header
    vs_headers = {
        "accept": "*/*",
        "Content-Type": "application/json",
        "Authorization": "Bearer " + expired_token,
    }

    # choose which path to go through i.e. gateway or ingress
    url = (
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-gateway-endpoint")
        if "vehicle-data-api" in base_url
        else get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint")
    )

    url_vehicles = (
        url
        + "/"
        + "vehicles"
        + "/"
        + get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid")
        + "?include"
        + "="
        + "IDENTITY,ASSETS,INVENTORY,POLICIES,APPLICATIONS,AUDIT"
    )

    print(url_vehicles)

    response = requests.request("GET", url_vehicles, headers=vs_headers)
    response_body = json.loads(response.text)

    print("Status: ", response.status_code)
    print(response.text)
    return response_body, response.status_code


if __name__ == "__main__":
    # Ensure getSegetSecretsFromAWSSecretsManager.py is ran to get variables needed from secrets manager
    # run "export $(cat ci_secrets.env | xargs)" in root folder to add variables to terminal environement
    # run "aws --profile vcdp-developers sso login" or "aws --profile vcdp-pre-production sso login" to connect to aws
    # you can run script in cd to libs and run script as so: python3 digitalVehicleApiCaching.py

    # environment = "dev"
    environment = "pre-prod"

    SYSTEM_VAR_FILE = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("keycloakUsername"),
        os.getenv("keycloakPassword"),
        "find-reg-by-uuid",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "keycloak",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        "noneRequired",
        "noneRequired",
        "find-reg-by-uuid",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "forgerock",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("keycloakUsername"),
        os.getenv("keycloakPassword"),
        "update-reg-by-uuid",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "keycloak",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        "noneRequired",
        "noneRequired",
        "update-reg-by-uuid",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "forgerock",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("keycloakUsername"),
        os.getenv("keycloakPassword"),
        "get-vehicle-data",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "keycloak",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        "noneRequired",
        "noneRequired",
        "get-vehicle-data",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "forgerock",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("keycloakUsername"),
        os.getenv("keycloakPassword"),
        "find-vin-by-uuid",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "keycloak",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("keycloakUsername"),
        os.getenv("keycloakPassword"),
        "identity-lookup",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_vin"),
        environment,
        "keycloak",
    )

    print("\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "find-vin-by-uuid",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "forgerock",
    )

    print("\n\n\n\n---------------------- Invalid token request ----------------")
    response, status = send_digital_vehicle_api_request_with_invalid_token(
        os.getenv("DvApiCachingB2B_KeycloakExpTokens"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        environment,
    )

    print("\n\n\n\n--------------------------------------------------------")
    response, status = send_digital_vehicle_api_request(
        os.getenv("forgeRockEmail"),
        os.getenv("forgeRockPassword"),
        "get-vehicle-data",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-gateway-endpoint"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_test_uuid"),
        environment,
        "forgerock",
    )

    print(
        "\n*********************************************************************",
        "\n*********************************************************************",
        "\n*********************************************************************",
        "\n*********************************************************************",
    )
    print("\n\n\n\n---------------------- CACHE EVICTION TESTING -----------------")

    print("\n\n\n\n---------------------- create new vehicle ---------------------")
    response, status = create_new_vehicle_data_for_dv_api_test(
        os.getenv("dvStateUsername"),
        os.getenv("dvStatePassword"),
        "../jsonschema/new-digital-vehicle-creation-dvApi.json",
        environment,
        True,
    )
    print("new vehicle created: \n", response)
    uuid_of_temp_vehicle_created = response["identity"]["uniqueId"]

    print("\n\n\n\n------- check cache for created vehicle -----------------------")
    response, status = send_digital_vehicle_api_request(
        "noneRequired",
        "noneRequired",
        "get-vehicle-data",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        uuid_of_temp_vehicle_created,
        environment,
        "forgerock",
    )
    print(
        "----vehicle reg in caching service is: ",
        response["data"]["identity"]["vehicleReg"],
    )
    if response["data"]["identity"]["uniqueId"] == uuid_of_temp_vehicle_created:
        print("----digital vehicle created via state handler is now present in cache")
    else:
        print("----digital vehicle created via state handler IS NOT present in cache")

    print("\n\n\n\n---------------------- update vehicle -----------------------")
    update_reg_request = generate_vehicle_reg_from_json_file("../json-request-data/dv-api-caching/updateRegNumber.json")
    print("new reg for update:", update_reg_request, "\n")
    status = partial_update_of_vehicle_data_by_vin_for_dv_api_test(
        os.getenv("dvStateUsername"),
        os.getenv("dvStatePassword"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_eviction_test_vin"),
        update_reg_request,
        environment,
    )
    print(
        "vehicle: ",
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_eviction_test_vin"),
        "update status",
        status,
    )

    print("\n\n\n\n---------------------- check for updated vehicle reg in digital vehicle database via vlc api -----------------------")
    response, status = get_vehicle_data_via_vlc_for_dv_api_test(
        os.getenv("dvStateUsername"),
        os.getenv("dvStatePassword"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_eviction_test_vin"),
        environment,
        True,
    )
    print(
        "----vehicle reg in digital vehicle service is: ",
        response["data"][0]["identity"]["vehicleReg"],
    )
    if response["data"][0]["identity"]["vehicleReg"] == update_reg_request["vehicleReg"]:
        print("----digital vehicle database has been udpated with new reg updated via state handler")
    else:
        print("----database has not been updated with new reg updated via state handler")

    print("\n\n\n\n---------------------- check cache for update vehicle -----------------------")
    response, status = send_digital_vehicle_api_request(
        "noneRequired",
        "noneRequired",
        "get-vehicle-data",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        uuid_of_temp_vehicle_created,
        environment,
        "forgerock",
    )
    print(
        "----vehicle reg in caching service is: ",
        response["data"]["identity"]["vehicleReg"],
    )
    if response["data"]["identity"]["vehicleReg"] == update_reg_request["vehicleReg"]:
        print("----cache has been udpated with new reg updated via state handler")
    else:
        print("----cache has not been updated with new reg updated via state handler")

    print("\n\n\n\n------ delete temporarily created vehicle via digital vehicle state handler api ------")
    status = delete_existing_vehicle_data_by_vin_for_dv_api_test(
        os.getenv("dvStateUsername"),
        os.getenv("dvStatePassword"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_eviction_test_vin"),
        environment,
        True,
    )

    print("\n\n\n\n------ check vehicle does not exist in digital vehicle state handler database------")
    status = get_vehicle_data_via_vlc_for_dv_api_test(
        os.getenv("dvStateUsername"),
        os.getenv("dvStatePassword"),
        get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_eviction_test_vin"),
        environment,
        True,
    )
    print("Deleted vehicle data response: ", status)

    print("\n\n\n\n****************** get vehicle from database **************")
    query = {"identity.vin": str(get_var_from_file(SYSTEM_VAR_FILE, "dv_caching_eviction_test_vin"))}

    # you need the goblab-bundle.pem in the path specified for it to work
    db_response = get_digital_vehicle_from_database(
        os.getenv("DB_USER"),
        os.getenv("DB_PASSWORD"),
        os.getenv("DB_HOST"),
        "/home/<USER>/Documents/Projects/digital-vehicle-api-component-tests/global-bundle.pem",
        "vehicles",
        query,
    )
    print("\n Check deleted vehicle is not in database: ", db_response, "\n\n")

    print("\n\n\n-------------------------check deleted vehicle data via api caching----------")
    response, status = send_digital_vehicle_api_request(
        "noneRequired",
        "noneRequired",
        "get-vehicle-data",
        get_var_from_file(SYSTEM_VAR_FILE, "dv-api-ingress-endpoint"),
        uuid_of_temp_vehicle_created,
        environment,
        "forgerock",
    )
