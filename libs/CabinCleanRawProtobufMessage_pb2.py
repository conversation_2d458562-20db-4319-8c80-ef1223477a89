# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CabinCleanRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n"CabinCleanRawProtobufMessage.proto\x12\x17jlr.protobuf.cabinclean\x1a\x10\x45numStatus.proto"y\n\x1eSetVehicleCabinAirCleanRequest\x12W\n\x1a\x63\x61\x62in_air_cleaning_request\x18\x01 \x01(\x0e\x32\x33.jlr.protobuf.cabinclean.EnumCabinAirCleanOperation"R\n\x1fSetVehicleCabinAirCleanResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"%\n#GetVehicleCabinAirCleanStateRequest"\x9c\x02\n$GetVehicleCabinAirCleanStateResponse\x12S\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32\x30.jlr.protobuf.cabinclean.EnumCabinAirCleanStatus\x12\x19\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\r\x12\x18\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\r\x12\x1c\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\r\x12\x1b\n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\r\x12/\n\x06status\x18\x06 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x97\x02\n\x1fNotifyVehicleCabinAirCleanState\x12S\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32\x30.jlr.protobuf.cabinclean.EnumCabinAirCleanStatus\x12\x19\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\r\x12\x18\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\r\x12\x1c\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\r\x12\x1b\n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\r\x12/\n\x06status\x18\x06 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\x9e\x06\n\x17\x45numCabinAirCleanStatus\x12+\n\'ENUM_CABIN_AIR_CLEAN_STATUS_UNSPECIFIED\x10\x00\x12(\n$ENUM_CABIN_AIR_CLEAN_STATUS_COMPLETE\x10\x01\x12+\n\'ENUM_CABIN_AIR_CLEAN_STATUS_IN_PROGRESS\x10\x02\x12\x38\n4ENUM_CABIN_AIR_CLEAN_STATUS_WARN_USER_STOP_REQUESTED\x10\x03\x12\x31\n-ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_LOW_BATTERY\x10\x04\x12\x38\n4ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_VEHICLE_NOT_SECURE\x10\x05\x12>\n:ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_CLEANING_CYCLE_EXHAUSTED\x10\x06\x12\x32\n.ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_SYSTEM_FAULT\x10\x07\x12>\n:ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_VEHICLE_POWER_TRANSITION\x10\x08\x12:\n6ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_OTHER_FEATURE_ACTIVE\x10\t\x12\x34\n0ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_CRASH_DETECTED\x10\n\x12\x36\n2ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_EXTERNAL_FAILURE\x10\x0b\x12=\n9ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_CRITICAL_SERVICE_ACTIVE\x10\x0c\x12;\n7ENUM_CABIN_AIR_CLEAN_STATUS_ERROR_SW_UPDATE_IN_PROGRESS\x10\r*\x9f\x01\n\x1a\x45numCabinAirCleanOperation\x12.\n*ENUM_CABIN_AIR_CLEAN_OPERATION_UNSPECIFIED\x10\x00\x12(\n$ENUM_CABIN_AIR_CLEAN_OPERATION_START\x10\x01\x12\'\n#ENUM_CABIN_AIR_CLEAN_OPERATION_STOP\x10\x02\x42\x61\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.cabincleanB\x1c\x43\x61\x62inCleanRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "CabinCleanRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.cabincleanB\034CabinCleanRawProtobufMessageP\001"
    _globals["_ENUMCABINAIRCLEANSTATUS"]._serialized_start = 897
    _globals["_ENUMCABINAIRCLEANSTATUS"]._serialized_end = 1695
    _globals["_ENUMCABINAIRCLEANOPERATION"]._serialized_start = 1698
    _globals["_ENUMCABINAIRCLEANOPERATION"]._serialized_end = 1857
    _globals["_SETVEHICLECABINAIRCLEANREQUEST"]._serialized_start = 81
    _globals["_SETVEHICLECABINAIRCLEANREQUEST"]._serialized_end = 202
    _globals["_SETVEHICLECABINAIRCLEANRESPONSE"]._serialized_start = 204
    _globals["_SETVEHICLECABINAIRCLEANRESPONSE"]._serialized_end = 286
    _globals["_GETVEHICLECABINAIRCLEANSTATEREQUEST"]._serialized_start = 288
    _globals["_GETVEHICLECABINAIRCLEANSTATEREQUEST"]._serialized_end = 325
    _globals["_GETVEHICLECABINAIRCLEANSTATERESPONSE"]._serialized_start = 328
    _globals["_GETVEHICLECABINAIRCLEANSTATERESPONSE"]._serialized_end = 612
    _globals["_NOTIFYVEHICLECABINAIRCLEANSTATE"]._serialized_start = 615
    _globals["_NOTIFYVEHICLECABINAIRCLEANSTATE"]._serialized_end = 894
# @@protoc_insertion_point(module_scope)
