import requests
import json
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig

from commonUtils import get_keycloak_token

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "excluded-vehicles-controller-url")


@keyword("Exclude vehicle from auto data products")
def exclude_vehicle(user, pw, vins: list):
    url = get_url() + "excluded-vehicles"
    token = get_keycloak_token(user, pw)

    payload = json.dumps({"vins": vins})
    headers = {"accept": "*/*", "Authorization": AUTH_TYPE + token, "Content-Type": "application/json"}

    response = requests.request("POST", url, headers=headers, data=payload)
    print(response)
    return response.status_code


@keyword("Get vehicles Data Product excluded state")
def get_excluded_status_for_vin(user, pw, vin: str):
    url = get_url() + "excluded-vehicles"
    token = get_keycloak_token(user, pw)

    payload = ""
    headers = {"accept": "*/*", "VIN": vin, "Authorization": AUTH_TYPE + token}

    response = requests.request("GET", url, headers=headers, data=payload)
    print(response.text)

    return json.loads(response.text)["data"][0]["isExcluded"]


@keyword("Include vehicle from auto data products")
def include_vehicle(user, pw, vins: list):
    url = get_url() + "excluded-vehicles"
    token = get_keycloak_token(user, pw)

    payload = json.dumps({"vins": vins})

    headers = {"accept": "*/*", "Authorization": AUTH_TYPE + token, "Content-Type": "application/json"}

    response = requests.request("DELETE", url, headers=headers, data=payload)
    print(response.text)
    return response.status_code
