import boto3

import time
import json
from datetime import datetime, timedelta
from robot.api.deco import keyword
import itertools

cloudwatch_resource = boto3.client("cloudwatch", region_name="eu-west-2")
cloudwatch_client = boto3.client("logs", region_name="eu-west-2")


def get_resource_log_group(log_group_regex, resource_identifier):
    log_prefix = log_group_regex.format(resource_identifier)
    log_group = cloudwatch_client.describe_log_groups(logGroupNamePrefix=log_prefix)

    return log_group["logGroups"][0]


def get_log_entries(log_group_name, query, start_time=None):
    time.sleep(2)

    stime = int((datetime.now() - timedelta(minutes=120)).timestamp()) if start_time is None else int(start_time)

    start_query_response = cloudwatch_client.start_query(
        logGroupName=log_group_name,
        startTime=stime,
        endTime=int((datetime.now().timestamp())),
        queryString=query,
    )

    query_id = start_query_response["queryId"]

    response = None

    while response is None or response["status"] == "Running":
        time.sleep(1)

        response = cloudwatch_client.get_query_results(queryId=query_id)

    return response


@keyword("Get logs from CloudWatch")
def get_log_stream(log_group_name, log_stream_name, requiredStringInLog, retryAttempts, retryDelayInSeconds):
    """
    This method is used to extract recent logs from cloud watch. User must know
    Log Group name & Log Stream name in order to extract the messages.
    Returns: It will return a list of all the event messages for the string
    that you are looking for. If the substring matches, it is added into the list.
    By default, extraction of recents logs is only possible without any time range.
    TODO: If required, we will add time range in future.
    Note: Expect certain delay (avg 10 sec) from the
          current timestamp for the log extraction.
    For that reason, parameters retryDelayInSeconds & retryAttempts is added.
    retryAttempts = No of times this method tries to extract the recent records.
                    Highly receommeneded for extracting recent event based
                    on the test execution.
    retryDelayInSeconds = Add delay before each execution before each iteration
                          of event extraction.
    """
    data_queue = []
    for _ in itertools.repeat(None, retryAttempts):
        response = cloudwatch_client.get_log_events(logGroupName=log_group_name, logStreamName=log_stream_name, startFromHead=False)
        start_time = datetime.now()
        print(start_time.strftime("%Y-%m-%d %H:%M:%S.%f"))
        for num in range(0, len(response["events"])):
            eventMsg = json.loads(response["events"][num]["message"])
            if eventMsg["message"] not in data_queue and requiredStringInLog in eventMsg["message"]:
                data_queue.append(eventMsg["message"])

        time.sleep(retryDelayInSeconds)
    return data_queue


def get_full_log(request_id, log_group_name, log_stream_name):
    process_time = 300
    delay = 0
    full_log_in_str = None
    start_position = None
    position = None
    end_position = None

    while delay < process_time:
        log_details = get_log_stream(log_group_name, log_stream_name)["events"]
        full_log = []
        for item in log_details:
            full_log.append(item["message"])
        full_log_in_str = json.dumps(full_log)
        position = full_log_in_str.find(request_id)
        start_position = full_log_in_str[:position].rfind("Start Request")
        end_position = full_log_in_str[:position].rfind("End Request")

        if end_position == -1:
            time.sleep(12)
            delay = delay + 15
        else:
            break
    return full_log_in_str[start_position : position + end_position + 100]


def get_logs_for_specific_requests(request_method, request_id, start_time_stamp=None):
    """
    This method used to get log for requested function for specific request made
    :param request_method: Partial functionname or full name to get logs for
    :param: request_id: specific requests to check in logs for
    :param: start_time_stamp: starttime of when function was invoked
    :return: log_results: returns list of items, item[0] log details & item[1] log id
    """
    process_time = 300
    delay = 0
    log_group = get_resource_log_group("*", request_method)  # TODO I'm pretty sure this isn't correct
    full_log = []
    search_in_log = request_id
    log_stream_name = None

    while delay < process_time:
        query = 'fields @timestamp, @message, @logStream| filter @message like "{searchInLog}" ' "sort @timestamp desc".format(
            searchInLog=search_in_log
        )

        log_entries = get_log_entries(log_group["logGroupName"], query, start_time_stamp)
        log_details = json.dumps(log_entries).replace("\\n", "").replace("\\t", "")

        if request_id in log_details:
            for item in log_details:
                for item in log_entries["results"][0]:
                    if item["field"] == "@logStream":
                        log_stream_name = item["value"]
                        break
                full_log = get_full_log(request_id, log_group["logGroupName"], log_stream_name)

                break
        else:
            time.sleep(12)
            delay = delay + 15

        log_results = None if delay > process_time else full_log

    return log_results
