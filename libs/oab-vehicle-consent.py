import commonUtils
from robot.api.deco import keyword

systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
serviceUrl = "oneapp-backend-url-2"


# GET Vehicle consents
@keyword("Get Vehicle consents")
def get_vehicle_consents(user_email, user_password, vehicleID, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/vehicles/" + vehicleID + "/consents?type=JOURNEY_LOGS"

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# PATCH Vehicle consents
@keyword("Update Vehicle consents")
def update_vehicle_consents(user_email, user_password, vehicleID, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/vehicles/" + vehicleID + "/consents?type=JOURNEY_LOGS"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]
