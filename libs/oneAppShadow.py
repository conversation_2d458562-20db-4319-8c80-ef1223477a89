import requests
from robot.api.deco import keyword
from pyaml_env import parse_config, BaseConfig

systemVarFile = "../variables/system.yml"
headers = {"accept": "*/*", "Content-Type": "application/json", "Requester": "Robot"}


@keyword("GET request to VehicleShadow API")
def get_vs_with_uuid(uuid):
    system_vars = BaseConfig(parse_config(systemVarFile))
    url = getattr(system_vars, "vehicle-shadow-url")
    vehicle_url = url + uuid
    print(url)
    response = requests.request("GET", url=vehicle_url)
    return response.status_code, response.text


@keyword("Vehicle Shadow get value and timestamp for signal")
def vehicle_shadow_get_value_for_signal(json_response, signal):
    """
    Returns the vehicle shadow value and timestamp from the json response
    """

    vs_value = "NoValue"
    timestamp = "NoTimeStamp"

    for x in json_response["properties"]:
        print(x["name"])
        if x["name"] == signal:
            vs_value = x["value"]
            timestamp = x["timestamp"]
            break

    return vs_value, timestamp
