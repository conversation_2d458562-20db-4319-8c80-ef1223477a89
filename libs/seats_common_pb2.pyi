from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class EnumSeatSelection(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_SELECTION_UNSPECIFIED: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_LEFT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_FIRST_ROW_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_LEFT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_SECOND_ROW_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_LEFT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_THIRD_ROW_ALL: _ClassVar[EnumSeatSelection]
    ENUM_SEAT_SELECTION_REAR_ALL: _ClassVar[EnumSeatSelection]
ENUM_SEAT_SELECTION_UNSPECIFIED: EnumSeatSelection
ENUM_SEAT_SELECTION_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_LEFT: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE: EnumSeatSelection
ENUM_SEAT_SELECTION_FIRST_ROW_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_LEFT: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE: EnumSeatSelection
ENUM_SEAT_SELECTION_SECOND_ROW_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_LEFT: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE: EnumSeatSelection
ENUM_SEAT_SELECTION_THIRD_ROW_ALL: EnumSeatSelection
ENUM_SEAT_SELECTION_REAR_ALL: EnumSeatSelection
