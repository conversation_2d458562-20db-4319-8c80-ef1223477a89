# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: someip_adapter_service.proto
# Protobuf Python Version: 5.27.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    1,
    '',
    'someip_adapter_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import charging_common_pb2 as charging__common__pb2
import climate_common_pb2 as climate__common__pb2
import seats_common_pb2 as seats__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1csomeip_adapter_service.proto\x12\x13someip_adpt_service\x1a\x15\x63harging_common.proto\x1a\x14\x63limate_common.proto\x1a\x12seats_common.proto\"\xe1\x01\n\x14SeatClimateOperation\x12;\n\tseat_area\x18\x01 \x01(\x0e\x32(.someip_adpt_service.EnumSeatClimateArea\x12=\n\nseat_state\x18\x02 \x01(\x0e\x32).someip_adpt_service.EnumSeatClimateState\x12M\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32-.someip_adpt_service.EnumSeatClimateIntensity\"\x81\x02\n\x14SeatClimateZoneState\x12\x37\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x1f.seats_common.EnumSeatSelection\x12\x41\n\x0eseat_operation\x18\x02 \x03(\x0b\x32).someip_adpt_service.SeatClimateOperation\x12R\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32-.someip_adpt_service.EnumSeatClimateIntensityH\x00\x88\x01\x01\x42\x19\n\x17_seat_climate_intensity\"\x98\x02\n\x1c\x44\x65partureTaskPreconditioning\x12,\n\x1fprecondition_target_temperature\x18\x01 \x01(\x02H\x00\x88\x01\x01\x12?\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32$.climate_common.SeatClimateZoneState\x12K\n\x15hsw_temperature_level\x18\x03 \x01(\x0e\x32\'.climate_common.EnumHSWTemperatureLevelH\x01\x88\x01\x01\x42\"\n _precondition_target_temperatureB\x18\n\x16_hsw_temperature_level\"\x19\n\x17\x44\x65partureTaskCabinClean\"\xab\x01\n\rDepartureTask\x12L\n\x0fpreconditioning\x18\x01 \x01(\x0b\x32\x31.someip_adpt_service.DepartureTaskPreconditioningH\x00\x12\x43\n\x0b\x63\x61\x62in_clean\x18\x02 \x01(\x0b\x32,.someip_adpt_service.DepartureTaskCabinCleanH\x00\x42\x07\n\x05value\"}\n\x0e\x44\x65partureEvent\x12\x1b\n\x0e\x64\x65parture_time\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12;\n\x0f\x64\x65parture_tasks\x18\x02 \x03(\x0b\x32\".someip_adpt_service.DepartureTaskB\x11\n\x0f_departure_time\"\xa2\x01\n\x17SetChargeControlRequest\x12K\n\x16\x63harge_control_request\x18\x01 \x01(\x0e\x32+.charging_common.EnumChargeControlOperation\x12:\n\x0e\x63harge_context\x18\x02 \x01(\x0e\x32\".charging_common.EnumChargeContext\"G\n\x18SetChargeControlResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\x17\n\x15GetChargeStateRequest\"\xc5\x08\n\x16GetChargeStateResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x39\n\x0f\x63harging_status\x18\x02 \x01(\x0e\x32 .charging_common.EnumChargeState\x12@\n\x12\x63harge_error_state\x18\x03 \x01(\x0e\x32$.charging_common.EnumChargeErrorMode\x12\x45\n\x14\x63harging_inlet_state\x18\x04 \x01(\x0e\x32\'.charging_common.EnumChargingInletState\x12<\n\x0f\x63harging_method\x18\x05 \x01(\x0e\x32#.charging_common.EnumChargingMethod\x12\x1e\n\x11max_current_limit\x18\x06 \x01(\rH\x00\x88\x01\x01\x12\x19\n\x0cinst_current\x18\x07 \x01(\rH\x01\x88\x01\x01\x12\x1e\n\x11inst_charge_power\x18\x08 \x01(\x11H\x02\x88\x01\x01\x12\x1f\n\x12\x63harge_rate_mts_ph\x18\t \x01(\rH\x03\x88\x01\x01\x12&\n\x19\x63harge_rate_percentage_ph\x18\n \x01(\rH\x04\x88\x01\x01\x12+\n\x1enext_charge_start_time_seconds\x18\x0b \x01(\x04H\x05\x88\x01\x01\x12\x43\n\x15predicted_charge_data\x18\r \x03(\x0b\x32$.charging_common.PredictedChargeData\x12\x1c\n\x0ftime_to_tgt_soc\x18\x0e \x01(\rH\x06\x88\x01\x01\x12 \n\x13tgt_soc_ev_range_km\x18\x0f \x01(\rH\x07\x88\x01\x01\x12K\n\x18\x63harge_cable_lock_status\x18\x10 \x01(\x0e\x32).charging_common.EnumChargeCableOperation\x12\x42\n\x15\x63harge_troubleshooter\x18\x11 \x01(\x0e\x32#.charging_common.EnumChrgTroubleSht\x12+\n\x1etgt_soc_reachable_by_departure\x18\x12 \x01(\x08H\x08\x88\x01\x01\x42\x14\n\x12_max_current_limitB\x0f\n\r_inst_currentB\x14\n\x12_inst_charge_powerB\x15\n\x13_charge_rate_mts_phB\x1c\n\x1a_charge_rate_percentage_phB!\n\x1f_next_charge_start_time_secondsB\x12\n\x10_time_to_tgt_socB\x16\n\x14_tgt_soc_ev_range_kmB!\n\x1f_tgt_soc_reachable_by_departureJ\x04\x08\x0c\x10\r\"\xf1\x02\n\x1dSetTimedChargeSettingsRequest\x12\x34\n\x0b\x63harge_type\x18\x01 \x01(\x0e\x32\x1f.charging_common.EnumChargeType\x12&\n\x19off_peak_start_time_hours\x18\x02 \x01(\rH\x00\x88\x01\x01\x12(\n\x1boff_peak_start_time_minutes\x18\x03 \x01(\rH\x01\x88\x01\x01\x12%\n\x18off_peak_stop_time_hours\x18\x04 \x01(\rH\x02\x88\x01\x01\x12\'\n\x1aoff_peak_stop_time_minutes\x18\x05 \x01(\rH\x03\x88\x01\x01\x42\x1c\n\x1a_off_peak_start_time_hoursB\x1e\n\x1c_off_peak_start_time_minutesB\x1b\n\x19_off_peak_stop_time_hoursB\x1d\n\x1b_off_peak_stop_time_minutes\"M\n\x1eSetTimedChargeSettingsResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\x1a\n\x18GetChargeSettingsRequest\"\x96\x04\n\x19GetChargeSettingsResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x34\n\x0b\x63harge_type\x18\x02 \x01(\x0e\x32\x1f.charging_common.EnumChargeType\x12&\n\x19off_peak_start_time_hours\x18\x03 \x01(\rH\x00\x88\x01\x01\x12(\n\x1boff_peak_start_time_minutes\x18\x04 \x01(\rH\x01\x88\x01\x01\x12%\n\x18off_peak_stop_time_hours\x18\x05 \x01(\rH\x02\x88\x01\x01\x12\'\n\x1aoff_peak_stop_time_minutes\x18\x06 \x01(\rH\x03\x88\x01\x01\x12\x1c\n\x0fmax_battery_soc\x18\x07 \x01(\rH\x04\x88\x01\x01\x12(\n\x1b\x61\x63tual_ac_charge_rate_limit\x18\x08 \x01(\rH\x05\x88\x01\x01\x42\x1c\n\x1a_off_peak_start_time_hoursB\x1e\n\x1c_off_peak_start_time_minutesB\x1b\n\x19_off_peak_stop_time_hoursB\x1d\n\x1b_off_peak_stop_time_minutesB\x12\n\x10_max_battery_socB\x1e\n\x1c_actual_ac_charge_rate_limit\"U\n!SetBatteryMaxStateOfChargeRequest\x12\x1c\n\x0fmax_battery_soc\x18\x01 \x01(\rH\x00\x88\x01\x01\x42\x12\n\x10_max_battery_soc\"Q\n\"SetBatteryMaxStateOfChargeResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\'\n%GetBatteryCurrentStateOfChargeRequest\"\xf1\x01\n&GetBatteryCurrentStateOfChargeResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12 \n\x13\x63urrent_battery_soc\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x34\n\'hv_batt_energy_available_at_current_soc\x18\x03 \x01(\rH\x01\x88\x01\x01\x42\x16\n\x14_current_battery_socB*\n(_hv_batt_energy_available_at_current_soc\"a\n\x1bSetACChargeRateLimitRequest\x12%\n\x18max_ac_charge_rate_limit\x18\x01 \x01(\rH\x00\x88\x01\x01\x42\x1b\n\x19_max_ac_charge_rate_limit\"K\n\x1cSetACChargeRateLimitResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\xa3\x01\n\x1dSetChargeDoorOperationRequest\x12\x39\n\x10\x63harge_door_side\x18\x01 \x01(\x0e\x32\x1f.charging_common.EnumChargeDoor\x12G\n\x15\x63harge_door_operation\x18\x02 \x01(\x0e\x32(.charging_common.EnumChargeDoorOperation\"M\n\x1eSetChargeDoorOperationResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\x1c\n\x1aGetChargeDoorStatusRequest\"\xe1\x01\n\x1bGetChargeDoorStatusResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12I\n\x17left_charge_door_status\x18\x02 \x01(\x0e\x32(.charging_common.EnumChargeDoorOperation\x12J\n\x18right_charge_door_status\x18\x03 \x01(\x0e\x32(.charging_common.EnumChargeDoorOperation\"d\n\x1eSetChargeCableOperationRequest\x12\x42\n\x0f\x63harge_cable_op\x18\x01 \x01(\x0e\x32).charging_common.EnumChargeCableOperation\"N\n\x1fSetChargeCableOperationResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"[\n\x1aSetPnCPaymentChoiceRequest\x12=\n\x0epayment_choice\x18\x01 \x01(\x0e\x32%.charging_common.EnumPnCPaymentChoice\"J\n\x1bSetPnCPaymentChoiceResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\x15\n\x13GetPnCStatusRequest\"\xc2\x01\n\x14GetPnCStatusResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x41\n\x12pnc_payment_method\x18\x02 \x01(\x0e\x32%.charging_common.EnumPnCPaymentMethod\x12:\n\x12pnc_feature_status\x18\x03 \x01(\x0e\x32\x1e.charging_common.EnumPnCStatus\"\"\n GetPnCFeatureAvailabilityRequest\"\x9f\x01\n!GetPnCFeatureAvailabilityResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12M\n\x18pnc_feature_availability\x18\x02 \x01(\x0e\x32+.charging_common.EnumPnCFeatureAvailability\"L\n\x15SetPnCVINShareRequest\x12\x33\n\tvin_share\x18\x01 \x01(\x0e\x32 .charging_common.EnumPnCVINShare\"E\n\x16SetPnCVINShareResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\xa9\x01\n\"SetPnCContractCertOperationRequest\x12N\n\x17\x63ontract_cert_operation\x18\x01 \x01(\x0e\x32-.charging_common.EnumPnCContractCertOperation\x12\r\n\x05\x65maid\x18\x02 \x01(\t\x12$\n\x1c\x63\x65rtificate_installation_res\x18\x03 \x01(\x0c\"R\n#SetPnCContractCertOperationResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\x1d\n\x1bGetPnCContractStatusRequest\"\xae\x01\n\x1cGetPnCContractStatusResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12?\n\x0f\x63ontract_status\x18\x02 \x01(\x0e\x32&.charging_common.EnumPnCContractStatus\x12 \n\x18pnc_contract_cert_id_res\x18\x03 \x01(\t\"\x1c\n\x1aGetPnCV2GRootStatusRequest\"\xb3\x01\n\x1bGetPnCV2GRootStatusResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12N\n\x14v2g_root_cert_status\x18\x02 \x01(\x0e\x32\x30.charging_common.EnumPnCV2GRootCertificateStatus\x12\x17\n\x0fpnc_v2g_root_id\x18\x03 \x01(\t\"#\n!GetChargeSessionAttributesRequest\"\xdd\x01\n\"GetChargeSessionAttributesResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x18\n\x0brange_added\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x19\n\x0c\x65nergy_added\x18\x03 \x01(\rH\x01\x88\x01\x01\x12\x1e\n\x11\x63harging_duration\x18\x04 \x01(\rH\x02\x88\x01\x01\x42\x0e\n\x0c_range_addedB\x0f\n\r_energy_addedB\x14\n\x12_charging_duration\"\x1d\n\x1bGetBatteryEnergyDataRequest\"\x93\x03\n\x1cGetBatteryEnergyDataResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12&\n\x19hv_batt_energy_max_usable\x18\x02 \x01(\rH\x00\x88\x01\x01\x12)\n\x1chv_battery_energy_min_usable\x18\x03 \x01(\rH\x01\x88\x01\x01\x12=\n0predicted_hv_batt_energy_available_at_target_soc\x18\x04 \x01(\rH\x02\x88\x01\x01\x12$\n\x17hv_batt_state_of_health\x18\x05 \x01(\rH\x03\x88\x01\x01\x42\x1c\n\x1a_hv_batt_energy_max_usableB\x1f\n\x1d_hv_battery_energy_min_usableB3\n1_predicted_hv_batt_energy_available_at_target_socB\x1a\n\x18_hv_batt_state_of_health\"\x1d\n\x1bGetBatteryEnergyLossRequest\"\xa7\x02\n\x1cGetBatteryEnergyLossResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x34\n\'hv_batt_energy_estimated_loss_discharge\x18\x02 \x01(\rH\x00\x88\x01\x01\x12@\n3hv_batt_energy_estimated_loss_discharge_route_total\x18\x03 \x01(\rH\x01\x88\x01\x01\x42*\n(_hv_batt_energy_estimated_loss_dischargeB6\n4_hv_batt_energy_estimated_loss_discharge_route_total\"\x1d\n\x1bGetChargingSessionIDRequest\"{\n\x1cGetChargingSessionIDResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x1b\n\x0epnc_session_id\x18\x02 \x01(\x04H\x00\x88\x01\x01\x42\x11\n\x0f_pnc_session_id\"]\n\x15SetSeatClimateRequest\x12\x44\n\x11seat_climate_zone\x18\x01 \x03(\x0b\x32).someip_adpt_service.SeatClimateZoneState\"~\n\x16SetSeatClimateResponse\x12\x37\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x1f.seats_common.EnumSeatSelection\x12+\n\x06status\x18\x02 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"V\n\x1bGetSeatClimateStatusRequest\x12\x37\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x1f.seats_common.EnumSeatSelection\"\x91\x01\n\x1cGetSeatClimateStatusResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x44\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32).someip_adpt_service.SeatClimateZoneState\"l\n\x1dSetHeatedSteeringWheelRequest\x12K\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32,.someip_adpt_service.EnumHSWTemperatureLevel\"M\n\x1eSetHeatedSteeringWheelResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"%\n#GetHeatedSteeringWheelStatusRequest\"\xe2\x01\n$GetHeatedSteeringWheelStatusResponse\x12K\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32,.someip_adpt_service.EnumHSWTemperatureLevel\x12+\n\x06status\x18\x02 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12@\n\x11hsw_control_state\x18\x03 \x01(\x0e\x32%.someip_adpt_service.EnumControlState\"\xbf\x01\n\x1dSetVehiclePreconditionRequest\x12L\n\x14precondition_request\x18\x01 \x01(\x0e\x32..someip_adpt_service.EnumPreconditionOperation\x12,\n\x1fprecondition_target_temperature\x18\x02 \x01(\x02H\x00\x88\x01\x01\x42\"\n _precondition_target_temperature\"M\n\x1eSetVehiclePreconditionResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"$\n\"GetVehiclePreconditionStateRequest\"\x99\x02\n#GetVehiclePreconditionStateResponse\x12K\n\x11precondition_mode\x18\x01 \x01(\x0e\x32\x30.someip_adpt_service.EnumPreConditionCurrentMode\x12H\n\x13precondition_status\x18\x02 \x01(\x0e\x32+.someip_adpt_service.EnumPreConditionStatus\x12\x1b\n\x0etime_remaining\x18\x03 \x01(\rH\x00\x88\x01\x01\x12+\n\x06status\x18\x04 \x01(\x0e\x32\x1b.charging_common.EnumStatusB\x11\n\x0f_time_remaining\"\\\n\x1bSetDepartureScheduleRequest\x12=\n\x10\x64\x65parture_events\x18\x01 \x03(\x0b\x32#.someip_adpt_service.DepartureEvent\"K\n\x1cSetDepartureScheduleResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\x1d\n\x1bGetApertureLockStateRequest\"\xd0\x04\n\x1cGetApertureLockStateResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12G\n\x12\x64river_door_status\x18\x02 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12J\n\x15passenger_door_status\x18\x03 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12K\n\x16reardriver_door_status\x18\x04 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12N\n\x19rearpassenger_door_status\x18\x05 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12\x44\n\x0ftailgate_status\x18\x06 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12\x42\n\rbonnet_status\x18\x07 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12G\n\x13\x63\x65ntral_lock_status\x18\x08 \x01(\x0e\x32*.someip_adpt_service.EnumCentralLockStatus\"\x1d\n\x1bGetVehicleAlarmStateRequest\"\x87\x01\n\x1cGetVehicleAlarmStateResponse\x12:\n\x0c\x61larm_status\x18\x01 \x01(\x0e\x32$.someip_adpt_service.EnumAlarmStatus\x12+\n\x06status\x18\x02 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"%\n#GetVehicleCabinAirCleanStateRequest\"\xd8\x03\n$GetVehicleCabinAirCleanStateResponse\x12O\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32,.someip_adpt_service.EnumCabinAirCleanStatus\x12\x1e\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1d\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\rH\x01\x88\x01\x01\x12!\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\rH\x02\x88\x01\x01\x12 \n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\rH\x03\x88\x01\x01\x12+\n\x06status\x18\x06 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12-\n cabin_air_clean_cycles_remaining\x18\x07 \x01(\rH\x04\x88\x01\x01\x42\x14\n\x12_cabin_pm2_5_levelB\x13\n\x11_cabin_pm2_5_bandB\x17\n\x15_external_pm2_5_levelB\x16\n\x14_external_pm2_5_bandB#\n!_cabin_air_clean_cycles_remaining\"\xe5\x03\n\x11NotifyChargeState\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x39\n\x0f\x63harging_status\x18\x02 \x01(\x0e\x32 .charging_common.EnumChargeState\x12@\n\x12\x63harge_error_state\x18\x03 \x01(\x0e\x32$.charging_common.EnumChargeErrorMode\x12\x45\n\x14\x63harging_inlet_state\x18\x04 \x01(\x0e\x32\'.charging_common.EnumChargingInletState\x12K\n\x18\x63harge_cable_lock_status\x18\x05 \x01(\x0e\x32).charging_common.EnumChargeCableOperation\x12\x42\n\x15\x63harge_troubleshooter\x18\x06 \x01(\x0e\x32#.charging_common.EnumChrgTroubleSht\x12+\n\x1etgt_soc_reachable_by_departure\x18\x07 \x01(\x08H\x00\x88\x01\x01\x42!\n\x1f_tgt_soc_reachable_by_departure\"\x91\x04\n\x14NotifyChargeSettings\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x34\n\x0b\x63harge_type\x18\x02 \x01(\x0e\x32\x1f.charging_common.EnumChargeType\x12&\n\x19off_peak_start_time_hours\x18\x03 \x01(\rH\x00\x88\x01\x01\x12(\n\x1boff_peak_start_time_minutes\x18\x04 \x01(\rH\x01\x88\x01\x01\x12%\n\x18off_peak_stop_time_hours\x18\x05 \x01(\rH\x02\x88\x01\x01\x12\'\n\x1aoff_peak_stop_time_minutes\x18\x06 \x01(\rH\x03\x88\x01\x01\x12\x1c\n\x0fmax_battery_soc\x18\x07 \x01(\rH\x04\x88\x01\x01\x12(\n\x1b\x61\x63tual_ac_charge_rate_limit\x18\x08 \x01(\rH\x05\x88\x01\x01\x42\x1c\n\x1a_off_peak_start_time_hoursB\x1e\n\x1c_off_peak_start_time_minutesB\x1b\n\x19_off_peak_stop_time_hoursB\x1d\n\x1b_off_peak_stop_time_minutesB\x12\n\x10_max_battery_socB\x1e\n\x1c_actual_ac_charge_rate_limit\"\xe6\x01\n!NotifyBatteryCurrentStateOfCharge\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12 \n\x13\x63urrent_battery_soc\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x31\n$hv_batt_energy_available_current_soc\x18\x03 \x01(\rH\x01\x88\x01\x01\x42\x16\n\x14_current_battery_socB\'\n%_hv_batt_energy_available_current_soc\"\x9d\x03\n\x1aNotifyChargeInProgressData\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12<\n\x0f\x63harging_method\x18\x02 \x01(\x0e\x32#.charging_common.EnumChargingMethod\x12\x1e\n\x11max_current_limit\x18\x03 \x01(\rH\x00\x88\x01\x01\x12\x19\n\x0cinst_current\x18\x04 \x01(\rH\x01\x88\x01\x01\x12\x1e\n\x11inst_charge_power\x18\x05 \x01(\x11H\x02\x88\x01\x01\x12\x1f\n\x12\x63harge_rate_mts_ph\x18\x06 \x01(\rH\x03\x88\x01\x01\x12&\n\x19\x63harge_rate_percentage_ph\x18\x07 \x01(\rH\x04\x88\x01\x01\x42\x14\n\x12_max_current_limitB\x0f\n\r_inst_currentB\x14\n\x12_inst_charge_powerB\x15\n\x13_charge_rate_mts_phB\x1c\n\x1a_charge_rate_percentage_ph\"\xcb\x02\n\x15NotifyPredictedCharge\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12+\n\x1enext_charge_start_time_seconds\x18\x02 \x01(\x04H\x00\x88\x01\x01\x12\x43\n\x15predicted_charge_data\x18\x04 \x03(\x0b\x32$.charging_common.PredictedChargeData\x12\x1c\n\x0ftime_to_tgt_soc\x18\x05 \x01(\rH\x01\x88\x01\x01\x12 \n\x13tgt_soc_ev_range_km\x18\x06 \x01(\rH\x02\x88\x01\x01\x42!\n\x1f_next_charge_start_time_secondsB\x12\n\x10_time_to_tgt_socB\x16\n\x14_tgt_soc_ev_range_kmJ\x04\x08\x03\x10\x04\"\xdc\x01\n\x16NotifyChargeDoorStatus\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12I\n\x17left_charge_door_status\x18\x02 \x01(\x0e\x32(.charging_common.EnumChargeDoorOperation\x12J\n\x18right_charge_door_status\x18\x03 \x01(\x0e\x32(.charging_common.EnumChargeDoorOperation\"\x9a\x01\n\x1cNotifyPnCFeatureAvailability\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12M\n\x18pnc_feature_availability\x18\x02 \x01(\x0e\x32+.charging_common.EnumPnCFeatureAvailability\"\x88\x01\n\x16NotifyPnCPaymentMethod\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x41\n\x12pnc_payment_method\x18\x02 \x01(\x0e\x32%.charging_common.EnumPnCPaymentMethod\"z\n\x0fNotifyPnCStatus\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12:\n\x12pnc_feature_status\x18\x02 \x01(\x0e\x32\x1e.charging_common.EnumPnCStatus\"\xa9\x01\n\x17NotifyPnCContractStatus\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12?\n\x0f\x63ontract_status\x18\x02 \x01(\x0e\x32&.charging_common.EnumPnCContractStatus\x12 \n\x18pnc_contract_cert_id_res\x18\x03 \x01(\t\"\xae\x01\n\x16NotifyPnCV2GRootStatus\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12N\n\x14v2g_root_cert_status\x18\x02 \x01(\x0e\x32\x30.charging_common.EnumPnCV2GRootCertificateStatus\x12\x17\n\x0fpnc_v2g_root_id\x18\x03 \x01(\t\"\xd8\x01\n\x1dNotifyChargeSessionAttributes\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x18\n\x0brange_added\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x19\n\x0c\x65nergy_added\x18\x03 \x01(\rH\x01\x88\x01\x01\x12\x1e\n\x11\x63harging_duration\x18\x04 \x01(\rH\x02\x88\x01\x01\x42\x0e\n\x0c_range_addedB\x0f\n\r_energy_addedB\x14\n\x12_charging_duration\"\x8e\x03\n\x17NotifyBatteryEnergyData\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12&\n\x19hv_batt_energy_max_usable\x18\x02 \x01(\rH\x00\x88\x01\x01\x12)\n\x1chv_battery_energy_min_usable\x18\x03 \x01(\rH\x01\x88\x01\x01\x12=\n0predicted_hv_batt_energy_available_at_target_soc\x18\x04 \x01(\rH\x02\x88\x01\x01\x12$\n\x17hv_batt_state_of_health\x18\x05 \x01(\rH\x03\x88\x01\x01\x42\x1c\n\x1a_hv_batt_energy_max_usableB\x1f\n\x1d_hv_battery_energy_min_usableB3\n1_predicted_hv_batt_energy_available_at_target_socB\x1a\n\x18_hv_batt_state_of_health\"\xa2\x02\n\x17NotifyBatteryEnergyLoss\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x34\n\'hv_batt_energy_estimated_loss_discharge\x18\x02 \x01(\rH\x00\x88\x01\x01\x12@\n3hv_batt_energy_estimated_loss_discharge_route_total\x18\x03 \x01(\rH\x01\x88\x01\x01\x42*\n(_hv_batt_energy_estimated_loss_dischargeB6\n4_hv_batt_energy_estimated_loss_discharge_route_total\"v\n\x17NotifyChargingSessionID\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x1b\n\x0epnc_session_id\x18\x02 \x01(\x04H\x00\x88\x01\x01\x42\x11\n\x0f_pnc_session_id\"\x8c\x01\n\x17NotifySeatClimateStatus\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12\x44\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32).someip_adpt_service.SeatClimateZoneState\"\xdd\x01\n\x1fNotifyHeatedSteeringWheelStatus\x12K\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32,.someip_adpt_service.EnumHSWTemperatureLevel\x12+\n\x06status\x18\x02 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12@\n\x11hsw_control_state\x18\x03 \x01(\x0e\x32%.someip_adpt_service.EnumControlState\"\x94\x02\n\x1eNotifyVehiclePreconditionState\x12K\n\x11precondition_mode\x18\x01 \x01(\x0e\x32\x30.someip_adpt_service.EnumPreConditionCurrentMode\x12H\n\x13precondition_status\x18\x02 \x01(\x0e\x32+.someip_adpt_service.EnumPreConditionStatus\x12\x1b\n\x0etime_remaining\x18\x03 \x01(\rH\x00\x88\x01\x01\x12+\n\x06status\x18\x04 \x01(\x0e\x32\x1b.charging_common.EnumStatusB\x11\n\x0f_time_remaining\"R\n#NotifyDepartureScheduleStoredStatus\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\xcb\x04\n\x17NotifyApertureLockState\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12G\n\x12\x64river_door_status\x18\x02 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12J\n\x15passenger_door_status\x18\x03 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12K\n\x16reardriver_door_status\x18\x04 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12N\n\x19rearpassenger_door_status\x18\x05 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12\x44\n\x0ftailgate_status\x18\x06 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12\x42\n\rbonnet_status\x18\x07 \x01(\x0e\x32+.someip_adpt_service.EnumApertureLockStatus\x12G\n\x13\x63\x65ntral_lock_status\x18\x08 \x01(\x0e\x32*.someip_adpt_service.EnumCentralLockStatus\"\x82\x01\n\x17NotifyVehicleAlarmState\x12:\n\x0c\x61larm_status\x18\x01 \x01(\x0e\x32$.someip_adpt_service.EnumAlarmStatus\x12+\n\x06status\x18\x02 \x01(\x0e\x32\x1b.charging_common.EnumStatus\"\xd3\x03\n\x1fNotifyVehicleCabinAirCleanState\x12O\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32,.someip_adpt_service.EnumCabinAirCleanStatus\x12\x1e\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1d\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\rH\x01\x88\x01\x01\x12!\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\rH\x02\x88\x01\x01\x12 \n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\rH\x03\x88\x01\x01\x12+\n\x06status\x18\x06 \x01(\x0e\x32\x1b.charging_common.EnumStatus\x12-\n cabin_air_clean_cycles_remaining\x18\x07 \x01(\rH\x04\x88\x01\x01\x42\x14\n\x12_cabin_pm2_5_levelB\x13\n\x11_cabin_pm2_5_bandB\x17\n\x15_external_pm2_5_levelB\x16\n\x14_external_pm2_5_bandB#\n!_cabin_air_clean_cycles_remaining*\xad\x01\n\x17\x45numHSWTemperatureLevel\x12*\n&ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED\x10\x00\x12\"\n\x1e\x45NUM_HSW_TEMPERATURE_LEVEL_OFF\x10\x01\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_1\x10\x02\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_2\x10\x03*\xb8\x04\n\x18\x45numSeatClimateIntensity\x12+\n\'ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_SEAT_CLIMATE_INTENSITY_OFF\x10\x01\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1\x10\x02\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1\x10\x03\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2\x10\x04\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2\x10\x05\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3\x10\x06\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3\x10\x07\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4\x10\x08\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4\x10\t\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5\x10\n\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5\x10\x0b*z\n\x10\x45numControlState\x12\"\n\x1e\x45NUM_CONTROL_STATE_UNSPECIFIED\x10\x00\x12 \n\x1c\x45NUM_CONTROL_STATE_INHIBITED\x10\x01\x12 \n\x1c\x45NUM_CONTROL_STATE_AVAILABLE\x10\x02*\xa3\x01\n\x13\x45numSeatClimateArea\x12&\n\"ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_AREA_ALL\x10\x01\x12\"\n\x1e\x45NUM_SEAT_CLIMATE_AREA_CUSHION\x10\x02\x12 \n\x1c\x45NUM_SEAT_CLIMATE_AREA_SQUAB\x10\x03*\xa5\x01\n\x14\x45numSeatClimateState\x12\'\n#ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_STATE_ON\x10\x01\x12\x1f\n\x1b\x45NUM_SEAT_CLIMATE_STATE_OFF\x10\x02\x12#\n\x1f\x45NUM_SEAT_CLIMATE_STATE_INHIBIT\x10\x03*\xa7\x01\n\x19\x45numPreconditionOperation\x12+\n\'ENUM_PRECONDITION_OPERATION_UNSPECIFIED\x10\x00\x12.\n*ENUM_PRECONDITION_OPERATION_OFFBOARD_START\x10\x01\x12-\n)ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP\x10\x02*\xd2\x01\n\x1b\x45numPreConditionCurrentMode\x12.\n*ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED\x10\x00\x12+\n\'ENUM_PRECONDITION_CURRENT_MODE_INACTIVE\x10\x01\x12,\n(ENUM_PRECONDITION_CURRENT_MODE_IMMEDIATE\x10\x02\x12(\n$ENUM_PRECONDITION_CURRENT_MODE_TIMED\x10\x03*\xd0\x02\n\x16\x45numPreConditionStatus\x12%\n!ENUM_PRECONDITION_STS_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_PRECONDITION_STS_OFF\x10\x01\x12\"\n\x1e\x45NUM_PRECONDITION_STS_START_UP\x10\x02\x12%\n!ENUM_PRECONDITION_STS_IN_PROGRESS\x10\x03\x12\"\n\x1e\x45NUM_PRECONDITION_STS_COMPLETE\x10\x04\x12*\n&ENUM_PRECONDITION_STS_PARTIAL_COMPLETE\x10\x05\x12)\n%ENUM_PRECONDITION_STS_ERR_LOW_BATTERY\x10\x06\x12*\n&ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT\x10\x07*w\n\x11\x45numEventTimeType\x12$\n ENUM_EVENT_TIME_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_EVENT_TIME_TYPE_BEGIN\x10\x01\x12\x1c\n\x18\x45NUM_EVENT_TIME_TYPE_END\x10\x02*\xc5\x01\n\x16\x45numApertureLockStatus\x12)\n%ENUM_APERTURE_LOCK_STATUS_UNSPECIFIED\x10\x00\x12&\n\"ENUM_APERTURE_LOCK_STATUS_UNLOCKED\x10\x01\x12+\n\'ENUM_APERTURE_LOCK_STATUS_SINGLE_LOCKED\x10\x02\x12+\n\'ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\x10\x03*\xee\x01\n\x15\x45numCentralLockStatus\x12(\n$ENUM_CENTRAL_LOCK_STATUS_UNSPECIFIED\x10\x00\x12%\n!ENUM_CENTRAL_LOCK_STATUS_UNLOCKED\x10\x01\x12+\n\'ENUM_CENTRAL_LOCK_STATUS_PARTIAL_LOCKED\x10\x02\x12+\n\'ENUM_CENTRAL_LOCK_STATUS_CENTRAL_LOCKED\x10\x03\x12*\n&ENUM_CENTRAL_LOCK_STATUS_DOUBLE_LOCKED\x10\x04*\xaf\x01\n\x0f\x45numAlarmStatus\x12!\n\x1d\x45NUM_ALARM_STATUS_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x45NUM_ALARM_STATUS_ARMED\x10\x01\x12\x1e\n\x1a\x45NUM_ALARM_STATUS_DISARMED\x10\x02\x12\x1f\n\x1b\x45NUM_ALARM_STATUS_TRIGGERED\x10\x03\x12\x1b\n\x17\x45NUM_ALARM_STATUS_FAULT\x10\x04*\x87\x06\n\x17\x45numCabinAirCleanStatus\x12(\n$ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED\x10\x00\x12%\n!ENUM_CABIN_AIR_CLEAN_STS_INACTIVE\x10\x01\x12%\n!ENUM_CABIN_AIR_CLEAN_STS_COMPLETE\x10\x02\x12(\n$ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS\x10\x03\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED\x10\x04\x12,\n(ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY\x10\x05\x12\x33\n/ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE\x10\x06\x12\x39\n5ENUM_CABIN_AIR_CLEAN_STS_ERR_CLEANING_CYCLE_EXHAUSTED\x10\x07\x12-\n)ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT\x10\x08\x12\x39\n5ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_POWER_TRANSITION\x10\t\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE\x10\n\x12/\n+ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED\x10\x0b\x12\x31\n-ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE\x10\x0c\x12\x38\n4ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERVICE_ACTIVE\x10\r\x12\x36\n2ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS\x10\x0e\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'someip_adapter_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_ENUMHSWTEMPERATURELEVEL']._serialized_start=16446
  _globals['_ENUMHSWTEMPERATURELEVEL']._serialized_end=16619
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_start=16622
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_end=17190
  _globals['_ENUMCONTROLSTATE']._serialized_start=17192
  _globals['_ENUMCONTROLSTATE']._serialized_end=17314
  _globals['_ENUMSEATCLIMATEAREA']._serialized_start=17317
  _globals['_ENUMSEATCLIMATEAREA']._serialized_end=17480
  _globals['_ENUMSEATCLIMATESTATE']._serialized_start=17483
  _globals['_ENUMSEATCLIMATESTATE']._serialized_end=17648
  _globals['_ENUMPRECONDITIONOPERATION']._serialized_start=17651
  _globals['_ENUMPRECONDITIONOPERATION']._serialized_end=17818
  _globals['_ENUMPRECONDITIONCURRENTMODE']._serialized_start=17821
  _globals['_ENUMPRECONDITIONCURRENTMODE']._serialized_end=18031
  _globals['_ENUMPRECONDITIONSTATUS']._serialized_start=18034
  _globals['_ENUMPRECONDITIONSTATUS']._serialized_end=18370
  _globals['_ENUMEVENTTIMETYPE']._serialized_start=18372
  _globals['_ENUMEVENTTIMETYPE']._serialized_end=18491
  _globals['_ENUMAPERTURELOCKSTATUS']._serialized_start=18494
  _globals['_ENUMAPERTURELOCKSTATUS']._serialized_end=18691
  _globals['_ENUMCENTRALLOCKSTATUS']._serialized_start=18694
  _globals['_ENUMCENTRALLOCKSTATUS']._serialized_end=18932
  _globals['_ENUMALARMSTATUS']._serialized_start=18935
  _globals['_ENUMALARMSTATUS']._serialized_end=19110
  _globals['_ENUMCABINAIRCLEANSTATUS']._serialized_start=19113
  _globals['_ENUMCABINAIRCLEANSTATUS']._serialized_end=19888
  _globals['_SEATCLIMATEOPERATION']._serialized_start=119
  _globals['_SEATCLIMATEOPERATION']._serialized_end=344
  _globals['_SEATCLIMATEZONESTATE']._serialized_start=347
  _globals['_SEATCLIMATEZONESTATE']._serialized_end=604
  _globals['_DEPARTURETASKPRECONDITIONING']._serialized_start=607
  _globals['_DEPARTURETASKPRECONDITIONING']._serialized_end=887
  _globals['_DEPARTURETASKCABINCLEAN']._serialized_start=889
  _globals['_DEPARTURETASKCABINCLEAN']._serialized_end=914
  _globals['_DEPARTURETASK']._serialized_start=917
  _globals['_DEPARTURETASK']._serialized_end=1088
  _globals['_DEPARTUREEVENT']._serialized_start=1090
  _globals['_DEPARTUREEVENT']._serialized_end=1215
  _globals['_SETCHARGECONTROLREQUEST']._serialized_start=1218
  _globals['_SETCHARGECONTROLREQUEST']._serialized_end=1380
  _globals['_SETCHARGECONTROLRESPONSE']._serialized_start=1382
  _globals['_SETCHARGECONTROLRESPONSE']._serialized_end=1453
  _globals['_GETCHARGESTATEREQUEST']._serialized_start=1455
  _globals['_GETCHARGESTATEREQUEST']._serialized_end=1478
  _globals['_GETCHARGESTATERESPONSE']._serialized_start=1481
  _globals['_GETCHARGESTATERESPONSE']._serialized_end=2574
  _globals['_SETTIMEDCHARGESETTINGSREQUEST']._serialized_start=2577
  _globals['_SETTIMEDCHARGESETTINGSREQUEST']._serialized_end=2946
  _globals['_SETTIMEDCHARGESETTINGSRESPONSE']._serialized_start=2948
  _globals['_SETTIMEDCHARGESETTINGSRESPONSE']._serialized_end=3025
  _globals['_GETCHARGESETTINGSREQUEST']._serialized_start=3027
  _globals['_GETCHARGESETTINGSREQUEST']._serialized_end=3053
  _globals['_GETCHARGESETTINGSRESPONSE']._serialized_start=3056
  _globals['_GETCHARGESETTINGSRESPONSE']._serialized_end=3590
  _globals['_SETBATTERYMAXSTATEOFCHARGEREQUEST']._serialized_start=3592
  _globals['_SETBATTERYMAXSTATEOFCHARGEREQUEST']._serialized_end=3677
  _globals['_SETBATTERYMAXSTATEOFCHARGERESPONSE']._serialized_start=3679
  _globals['_SETBATTERYMAXSTATEOFCHARGERESPONSE']._serialized_end=3760
  _globals['_GETBATTERYCURRENTSTATEOFCHARGEREQUEST']._serialized_start=3762
  _globals['_GETBATTERYCURRENTSTATEOFCHARGEREQUEST']._serialized_end=3801
  _globals['_GETBATTERYCURRENTSTATEOFCHARGERESPONSE']._serialized_start=3804
  _globals['_GETBATTERYCURRENTSTATEOFCHARGERESPONSE']._serialized_end=4045
  _globals['_SETACCHARGERATELIMITREQUEST']._serialized_start=4047
  _globals['_SETACCHARGERATELIMITREQUEST']._serialized_end=4144
  _globals['_SETACCHARGERATELIMITRESPONSE']._serialized_start=4146
  _globals['_SETACCHARGERATELIMITRESPONSE']._serialized_end=4221
  _globals['_SETCHARGEDOOROPERATIONREQUEST']._serialized_start=4224
  _globals['_SETCHARGEDOOROPERATIONREQUEST']._serialized_end=4387
  _globals['_SETCHARGEDOOROPERATIONRESPONSE']._serialized_start=4389
  _globals['_SETCHARGEDOOROPERATIONRESPONSE']._serialized_end=4466
  _globals['_GETCHARGEDOORSTATUSREQUEST']._serialized_start=4468
  _globals['_GETCHARGEDOORSTATUSREQUEST']._serialized_end=4496
  _globals['_GETCHARGEDOORSTATUSRESPONSE']._serialized_start=4499
  _globals['_GETCHARGEDOORSTATUSRESPONSE']._serialized_end=4724
  _globals['_SETCHARGECABLEOPERATIONREQUEST']._serialized_start=4726
  _globals['_SETCHARGECABLEOPERATIONREQUEST']._serialized_end=4826
  _globals['_SETCHARGECABLEOPERATIONRESPONSE']._serialized_start=4828
  _globals['_SETCHARGECABLEOPERATIONRESPONSE']._serialized_end=4906
  _globals['_SETPNCPAYMENTCHOICEREQUEST']._serialized_start=4908
  _globals['_SETPNCPAYMENTCHOICEREQUEST']._serialized_end=4999
  _globals['_SETPNCPAYMENTCHOICERESPONSE']._serialized_start=5001
  _globals['_SETPNCPAYMENTCHOICERESPONSE']._serialized_end=5075
  _globals['_GETPNCSTATUSREQUEST']._serialized_start=5077
  _globals['_GETPNCSTATUSREQUEST']._serialized_end=5098
  _globals['_GETPNCSTATUSRESPONSE']._serialized_start=5101
  _globals['_GETPNCSTATUSRESPONSE']._serialized_end=5295
  _globals['_GETPNCFEATUREAVAILABILITYREQUEST']._serialized_start=5297
  _globals['_GETPNCFEATUREAVAILABILITYREQUEST']._serialized_end=5331
  _globals['_GETPNCFEATUREAVAILABILITYRESPONSE']._serialized_start=5334
  _globals['_GETPNCFEATUREAVAILABILITYRESPONSE']._serialized_end=5493
  _globals['_SETPNCVINSHAREREQUEST']._serialized_start=5495
  _globals['_SETPNCVINSHAREREQUEST']._serialized_end=5571
  _globals['_SETPNCVINSHARERESPONSE']._serialized_start=5573
  _globals['_SETPNCVINSHARERESPONSE']._serialized_end=5642
  _globals['_SETPNCCONTRACTCERTOPERATIONREQUEST']._serialized_start=5645
  _globals['_SETPNCCONTRACTCERTOPERATIONREQUEST']._serialized_end=5814
  _globals['_SETPNCCONTRACTCERTOPERATIONRESPONSE']._serialized_start=5816
  _globals['_SETPNCCONTRACTCERTOPERATIONRESPONSE']._serialized_end=5898
  _globals['_GETPNCCONTRACTSTATUSREQUEST']._serialized_start=5900
  _globals['_GETPNCCONTRACTSTATUSREQUEST']._serialized_end=5929
  _globals['_GETPNCCONTRACTSTATUSRESPONSE']._serialized_start=5932
  _globals['_GETPNCCONTRACTSTATUSRESPONSE']._serialized_end=6106
  _globals['_GETPNCV2GROOTSTATUSREQUEST']._serialized_start=6108
  _globals['_GETPNCV2GROOTSTATUSREQUEST']._serialized_end=6136
  _globals['_GETPNCV2GROOTSTATUSRESPONSE']._serialized_start=6139
  _globals['_GETPNCV2GROOTSTATUSRESPONSE']._serialized_end=6318
  _globals['_GETCHARGESESSIONATTRIBUTESREQUEST']._serialized_start=6320
  _globals['_GETCHARGESESSIONATTRIBUTESREQUEST']._serialized_end=6355
  _globals['_GETCHARGESESSIONATTRIBUTESRESPONSE']._serialized_start=6358
  _globals['_GETCHARGESESSIONATTRIBUTESRESPONSE']._serialized_end=6579
  _globals['_GETBATTERYENERGYDATAREQUEST']._serialized_start=6581
  _globals['_GETBATTERYENERGYDATAREQUEST']._serialized_end=6610
  _globals['_GETBATTERYENERGYDATARESPONSE']._serialized_start=6613
  _globals['_GETBATTERYENERGYDATARESPONSE']._serialized_end=7016
  _globals['_GETBATTERYENERGYLOSSREQUEST']._serialized_start=7018
  _globals['_GETBATTERYENERGYLOSSREQUEST']._serialized_end=7047
  _globals['_GETBATTERYENERGYLOSSRESPONSE']._serialized_start=7050
  _globals['_GETBATTERYENERGYLOSSRESPONSE']._serialized_end=7345
  _globals['_GETCHARGINGSESSIONIDREQUEST']._serialized_start=7347
  _globals['_GETCHARGINGSESSIONIDREQUEST']._serialized_end=7376
  _globals['_GETCHARGINGSESSIONIDRESPONSE']._serialized_start=7378
  _globals['_GETCHARGINGSESSIONIDRESPONSE']._serialized_end=7501
  _globals['_SETSEATCLIMATEREQUEST']._serialized_start=7503
  _globals['_SETSEATCLIMATEREQUEST']._serialized_end=7596
  _globals['_SETSEATCLIMATERESPONSE']._serialized_start=7598
  _globals['_SETSEATCLIMATERESPONSE']._serialized_end=7724
  _globals['_GETSEATCLIMATESTATUSREQUEST']._serialized_start=7726
  _globals['_GETSEATCLIMATESTATUSREQUEST']._serialized_end=7812
  _globals['_GETSEATCLIMATESTATUSRESPONSE']._serialized_start=7815
  _globals['_GETSEATCLIMATESTATUSRESPONSE']._serialized_end=7960
  _globals['_SETHEATEDSTEERINGWHEELREQUEST']._serialized_start=7962
  _globals['_SETHEATEDSTEERINGWHEELREQUEST']._serialized_end=8070
  _globals['_SETHEATEDSTEERINGWHEELRESPONSE']._serialized_start=8072
  _globals['_SETHEATEDSTEERINGWHEELRESPONSE']._serialized_end=8149
  _globals['_GETHEATEDSTEERINGWHEELSTATUSREQUEST']._serialized_start=8151
  _globals['_GETHEATEDSTEERINGWHEELSTATUSREQUEST']._serialized_end=8188
  _globals['_GETHEATEDSTEERINGWHEELSTATUSRESPONSE']._serialized_start=8191
  _globals['_GETHEATEDSTEERINGWHEELSTATUSRESPONSE']._serialized_end=8417
  _globals['_SETVEHICLEPRECONDITIONREQUEST']._serialized_start=8420
  _globals['_SETVEHICLEPRECONDITIONREQUEST']._serialized_end=8611
  _globals['_SETVEHICLEPRECONDITIONRESPONSE']._serialized_start=8613
  _globals['_SETVEHICLEPRECONDITIONRESPONSE']._serialized_end=8690
  _globals['_GETVEHICLEPRECONDITIONSTATEREQUEST']._serialized_start=8692
  _globals['_GETVEHICLEPRECONDITIONSTATEREQUEST']._serialized_end=8728
  _globals['_GETVEHICLEPRECONDITIONSTATERESPONSE']._serialized_start=8731
  _globals['_GETVEHICLEPRECONDITIONSTATERESPONSE']._serialized_end=9012
  _globals['_SETDEPARTURESCHEDULEREQUEST']._serialized_start=9014
  _globals['_SETDEPARTURESCHEDULEREQUEST']._serialized_end=9106
  _globals['_SETDEPARTURESCHEDULERESPONSE']._serialized_start=9108
  _globals['_SETDEPARTURESCHEDULERESPONSE']._serialized_end=9183
  _globals['_GETAPERTURELOCKSTATEREQUEST']._serialized_start=9185
  _globals['_GETAPERTURELOCKSTATEREQUEST']._serialized_end=9214
  _globals['_GETAPERTURELOCKSTATERESPONSE']._serialized_start=9217
  _globals['_GETAPERTURELOCKSTATERESPONSE']._serialized_end=9809
  _globals['_GETVEHICLEALARMSTATEREQUEST']._serialized_start=9811
  _globals['_GETVEHICLEALARMSTATEREQUEST']._serialized_end=9840
  _globals['_GETVEHICLEALARMSTATERESPONSE']._serialized_start=9843
  _globals['_GETVEHICLEALARMSTATERESPONSE']._serialized_end=9978
  _globals['_GETVEHICLECABINAIRCLEANSTATEREQUEST']._serialized_start=9980
  _globals['_GETVEHICLECABINAIRCLEANSTATEREQUEST']._serialized_end=10017
  _globals['_GETVEHICLECABINAIRCLEANSTATERESPONSE']._serialized_start=10020
  _globals['_GETVEHICLECABINAIRCLEANSTATERESPONSE']._serialized_end=10492
  _globals['_NOTIFYCHARGESTATE']._serialized_start=10495
  _globals['_NOTIFYCHARGESTATE']._serialized_end=10980
  _globals['_NOTIFYCHARGESETTINGS']._serialized_start=10983
  _globals['_NOTIFYCHARGESETTINGS']._serialized_end=11512
  _globals['_NOTIFYBATTERYCURRENTSTATEOFCHARGE']._serialized_start=11515
  _globals['_NOTIFYBATTERYCURRENTSTATEOFCHARGE']._serialized_end=11745
  _globals['_NOTIFYCHARGEINPROGRESSDATA']._serialized_start=11748
  _globals['_NOTIFYCHARGEINPROGRESSDATA']._serialized_end=12161
  _globals['_NOTIFYPREDICTEDCHARGE']._serialized_start=12164
  _globals['_NOTIFYPREDICTEDCHARGE']._serialized_end=12495
  _globals['_NOTIFYCHARGEDOORSTATUS']._serialized_start=12498
  _globals['_NOTIFYCHARGEDOORSTATUS']._serialized_end=12718
  _globals['_NOTIFYPNCFEATUREAVAILABILITY']._serialized_start=12721
  _globals['_NOTIFYPNCFEATUREAVAILABILITY']._serialized_end=12875
  _globals['_NOTIFYPNCPAYMENTMETHOD']._serialized_start=12878
  _globals['_NOTIFYPNCPAYMENTMETHOD']._serialized_end=13014
  _globals['_NOTIFYPNCSTATUS']._serialized_start=13016
  _globals['_NOTIFYPNCSTATUS']._serialized_end=13138
  _globals['_NOTIFYPNCCONTRACTSTATUS']._serialized_start=13141
  _globals['_NOTIFYPNCCONTRACTSTATUS']._serialized_end=13310
  _globals['_NOTIFYPNCV2GROOTSTATUS']._serialized_start=13313
  _globals['_NOTIFYPNCV2GROOTSTATUS']._serialized_end=13487
  _globals['_NOTIFYCHARGESESSIONATTRIBUTES']._serialized_start=13490
  _globals['_NOTIFYCHARGESESSIONATTRIBUTES']._serialized_end=13706
  _globals['_NOTIFYBATTERYENERGYDATA']._serialized_start=13709
  _globals['_NOTIFYBATTERYENERGYDATA']._serialized_end=14107
  _globals['_NOTIFYBATTERYENERGYLOSS']._serialized_start=14110
  _globals['_NOTIFYBATTERYENERGYLOSS']._serialized_end=14400
  _globals['_NOTIFYCHARGINGSESSIONID']._serialized_start=14402
  _globals['_NOTIFYCHARGINGSESSIONID']._serialized_end=14520
  _globals['_NOTIFYSEATCLIMATESTATUS']._serialized_start=14523
  _globals['_NOTIFYSEATCLIMATESTATUS']._serialized_end=14663
  _globals['_NOTIFYHEATEDSTEERINGWHEELSTATUS']._serialized_start=14666
  _globals['_NOTIFYHEATEDSTEERINGWHEELSTATUS']._serialized_end=14887
  _globals['_NOTIFYVEHICLEPRECONDITIONSTATE']._serialized_start=14890
  _globals['_NOTIFYVEHICLEPRECONDITIONSTATE']._serialized_end=15166
  _globals['_NOTIFYDEPARTURESCHEDULESTOREDSTATUS']._serialized_start=15168
  _globals['_NOTIFYDEPARTURESCHEDULESTOREDSTATUS']._serialized_end=15250
  _globals['_NOTIFYAPERTURELOCKSTATE']._serialized_start=15253
  _globals['_NOTIFYAPERTURELOCKSTATE']._serialized_end=15840
  _globals['_NOTIFYVEHICLEALARMSTATE']._serialized_start=15843
  _globals['_NOTIFYVEHICLEALARMSTATE']._serialized_end=15973
  _globals['_NOTIFYVEHICLECABINAIRCLEANSTATE']._serialized_start=15976
  _globals['_NOTIFYVEHICLECABINAIRCLEANSTATE']._serialized_end=16443
# @@protoc_insertion_point(module_scope)
