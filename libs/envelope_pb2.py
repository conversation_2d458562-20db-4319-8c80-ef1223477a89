# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: envelope.proto
# Protobuf Python Version: 5.27.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    1,
    '',
    'envelope.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0e\x65nvelope.proto\x12\x0fgateway_service\"u\n\x17SubscribeMessageRequest\x12\x11\n\x04hash\x18\x01 \x01(\tH\x00\x88\x01\x01\x12#\n\x16wait_for_service_in_ms\x18\x02 \x01(\rH\x01\x88\x01\x01\x42\x07\n\x05_hashB\x19\n\x17_wait_for_service_in_ms\"l\n\x18SubscribeMessageResponse\x12\x34\n\x06result\x18\x01 \x01(\x0e\x32$.gateway_service.EnumOperationStatus\x12\x11\n\x04hash\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_hash\"7\n\x19UnsubscribeMessageRequest\x12\x11\n\x04hash\x18\x01 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_hash\"n\n\x1aUnsubscribeMessageResponse\x12\x34\n\x06result\x18\x01 \x01(\x0e\x32$.gateway_service.EnumOperationStatus\x12\x11\n\x04hash\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_hash\"\x8a\x02\n\x0eRequestMessage\x12\x11\n\x04hash\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x0frequest_context\x18\x02 \x01(\rH\x01\x88\x01\x01\x12#\n\x16wait_for_service_in_ms\x18\x03 \x01(\rH\x02\x88\x01\x01\x12 \n\x13response_timeout_ms\x18\x04 \x01(\rH\x03\x88\x01\x01\x12\x1c\n\x0fmessage_payload\x18\x05 \x01(\x0cH\x04\x88\x01\x01\x42\x07\n\x05_hashB\x12\n\x10_request_contextB\x19\n\x17_wait_for_service_in_msB\x16\n\x14_response_timeout_msB\x12\n\x10_message_payload\"\xc7\x01\n\x0fResponseMessage\x12\x34\n\x06result\x18\x01 \x01(\x0e\x32$.gateway_service.EnumOperationStatus\x12\x1c\n\x0frequest_context\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x11\n\x04hash\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x1c\n\x0fmessage_payload\x18\x04 \x01(\x0cH\x02\x88\x01\x01\x42\x12\n\x10_request_contextB\x07\n\x05_hashB\x12\n\x10_message_payload\"\\\n\x0c\x45ventMessage\x12\x11\n\x04hash\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x0fmessage_payload\x18\x02 \x01(\x0cH\x01\x88\x01\x01\x42\x07\n\x05_hashB\x12\n\x10_message_payload\"\x85\x03\n\x11RFCRequestMessage\x12\x1c\n\x0frequest_context\x18\x01 \x01(\rH\x01\x88\x01\x01\x12@\n\x0enetwork_action\x18\x02 \x01(\x0e\x32(.gateway_service.EnumNetworkDemandAction\x12\x1b\n\x0enetwork_demand\x18\x03 \x01(\rH\x02\x88\x01\x01\x12#\n\x16network_demand_time_ms\x18\x04 \x01(\rH\x03\x88\x01\x01\x12\x45\n\x11subscribe_request\x18\x05 \x01(\x0b\x32(.gateway_service.SubscribeMessageRequestH\x00\x12:\n\x0frequest_message\x18\x06 \x01(\x0b\x32\x1f.gateway_service.RequestMessageH\x00\x42\t\n\x07messageB\x12\n\x10_request_contextB\x11\n\x0f_network_demandB\x19\n\x17_network_demand_time_ms\"|\n\x12RFCResponseMessage\x12\x34\n\x06result\x18\x01 \x01(\x0e\x32$.gateway_service.EnumOperationStatus\x12\x1c\n\x0frequest_context\x18\x02 \x01(\rH\x00\x88\x01\x01\x42\x12\n\x10_request_context\"\xcb\x03\n\x15GatewayRequestMessage\x12\x45\n\x11subscribe_request\x18\x01 \x01(\x0b\x32(.gateway_service.SubscribeMessageRequestH\x00\x12I\n\x13unsubscribe_request\x18\x02 \x01(\x0b\x32*.gateway_service.UnsubscribeMessageRequestH\x00\x12:\n\x0frequest_message\x18\x03 \x01(\x0b\x32\x1f.gateway_service.RequestMessageH\x00\x12\x39\n\x0brfc_message\x18\x04 \x01(\x0b\x32\".gateway_service.RFCRequestMessageH\x00\x12\x1a\n\rcreation_time\x18\x05 \x01(\x04H\x01\x88\x01\x01\x12\x19\n\x0ctime_to_live\x18\x06 \x01(\rH\x02\x88\x01\x01\x12\x18\n\x0bsequence_no\x18\x07 \x01(\rH\x03\x88\x01\x01\x12\x11\n\x04vuid\x18\x08 \x01(\tH\x04\x88\x01\x01\x42\t\n\x07messageB\x10\n\x0e_creation_timeB\x0f\n\r_time_to_liveB\x0e\n\x0c_sequence_noB\x07\n\x05_vuid\"\x98\x03\n\x16GatewayResponseMessage\x12G\n\x12subscribe_response\x18\x01 \x01(\x0b\x32).gateway_service.SubscribeMessageResponseH\x00\x12K\n\x14unsubscribe_response\x18\x02 \x01(\x0b\x32+.gateway_service.UnsubscribeMessageResponseH\x00\x12<\n\x10response_message\x18\x03 \x01(\x0b\x32 .gateway_service.ResponseMessageH\x00\x12\x36\n\revent_message\x18\x04 \x01(\x0b\x32\x1d.gateway_service.EventMessageH\x00\x12;\n\x0crfc_response\x18\x05 \x01(\x0b\x32#.gateway_service.RFCResponseMessageH\x00\x12\x19\n\x0ctimestamp_ms\x18\x06 \x01(\x04H\x01\x88\x01\x01\x42\t\n\x07messageB\x0f\n\r_timestamp_ms*\xfd\x03\n\x13\x45numOperationStatus\x12%\n!ENUM_OPERATION_STATUS_UNSPECIFIED\x10\x00\x12\x1c\n\x18\x45NUM_OPERATION_STATUS_OK\x10\x01\x12(\n$ENUM_OPERATION_STATUS_HASH_NOT_FOUND\x10\x02\x12/\n+ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE\x10\x03\x12*\n&ENUM_OPERATION_STATUS_SECURITY_FAILURE\x10\x04\x12\x31\n-ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE\x10\x05\x12)\n%ENUM_OPERATION_STATUS_REQUEST_EXPIRED\x10\x06\x12+\n\'ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT\x10\x07\x12;\n7ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED\x10\x08\x12*\n&ENUM_OPERATION_STATUS_OPERATION_FAILED\x10\t\x12&\n\"ENUM_OPERATION_STATUS_INVALID_VUID\x10\n*\x95\x01\n\x17\x45numNetworkDemandAction\x12*\n&ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED\x10\x00\x12&\n\"ENUM_NETWORK_DEMAND_ACTION_ACQUIRE\x10\x01\x12&\n\"ENUM_NETWORK_DEMAND_ACTION_RELEASE\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envelope_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_ENUMOPERATIONSTATUS']._serialized_start=2390
  _globals['_ENUMOPERATIONSTATUS']._serialized_end=2899
  _globals['_ENUMNETWORKDEMANDACTION']._serialized_start=2902
  _globals['_ENUMNETWORKDEMANDACTION']._serialized_end=3051
  _globals['_SUBSCRIBEMESSAGEREQUEST']._serialized_start=35
  _globals['_SUBSCRIBEMESSAGEREQUEST']._serialized_end=152
  _globals['_SUBSCRIBEMESSAGERESPONSE']._serialized_start=154
  _globals['_SUBSCRIBEMESSAGERESPONSE']._serialized_end=262
  _globals['_UNSUBSCRIBEMESSAGEREQUEST']._serialized_start=264
  _globals['_UNSUBSCRIBEMESSAGEREQUEST']._serialized_end=319
  _globals['_UNSUBSCRIBEMESSAGERESPONSE']._serialized_start=321
  _globals['_UNSUBSCRIBEMESSAGERESPONSE']._serialized_end=431
  _globals['_REQUESTMESSAGE']._serialized_start=434
  _globals['_REQUESTMESSAGE']._serialized_end=700
  _globals['_RESPONSEMESSAGE']._serialized_start=703
  _globals['_RESPONSEMESSAGE']._serialized_end=902
  _globals['_EVENTMESSAGE']._serialized_start=904
  _globals['_EVENTMESSAGE']._serialized_end=996
  _globals['_RFCREQUESTMESSAGE']._serialized_start=999
  _globals['_RFCREQUESTMESSAGE']._serialized_end=1388
  _globals['_RFCRESPONSEMESSAGE']._serialized_start=1390
  _globals['_RFCRESPONSEMESSAGE']._serialized_end=1514
  _globals['_GATEWAYREQUESTMESSAGE']._serialized_start=1517
  _globals['_GATEWAYREQUESTMESSAGE']._serialized_end=1976
  _globals['_GATEWAYRESPONSEMESSAGE']._serialized_start=1979
  _globals['_GATEWAYRESPONSEMESSAGE']._serialized_end=2387
# @@protoc_insertion_point(module_scope)
