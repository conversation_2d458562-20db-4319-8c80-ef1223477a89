#!/usr/bin/env python3
import boto3
from botocore.exceptions import NoCredentialsError, ClientError

"""
These functions use boto3 python library and as it connects with S3, it required below pre-requisites

boto3 library installed to use these functions
AWS CLI with SSO configured locally to run it on local
"""

s3_success = "S3 action successful"
s3_failure = "S3 action failed"
aws_s3 = "s3"
client = boto3.client(aws_s3)


# upload single file to S3 bucket
def upload_to_s3(file_local, bucket, file_s3):
    try:
        client.upload_file(file_local, bucket, file_s3)
        print(s3_success)
        return True
    except (FileNotFoundError, NoCredentialsError) as error:
        print(s3_failure, error)
        return False


# download single file from S3 bucket
def download_from_s3(bucket, file_s3, file_local):
    try:
        client.download_file(bucket, file_s3, file_local)
        print(s3_success)
        return True
    except (FileNotFoundError, NoCredentialsError, ClientError) as error:
        print(s3_failure, error)
        return False


# Delete a file from S3 bucket
def delete_from_s3(bucket, file_s3):
    try:
        client.delete_object(Bucket=bucket, Key=file_s3)
        print(s3_success)
        return True
    except (FileNotFoundError, NoCredentialsError) as error:
        print(s3_failure, error)
        return False
