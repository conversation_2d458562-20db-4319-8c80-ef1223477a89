import seats_common_pb2 as _seats_common_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnumSeatClimateIntensity(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_OFF: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5: _ClassVar[EnumSeatClimateIntensity]
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5: _ClassVar[EnumSeatClimateIntensity]

class EnumSeatClimateArea(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED: _ClassVar[EnumSeatClimateArea]
    ENUM_SEAT_CLIMATE_AREA_ALL: _ClassVar[EnumSeatClimateArea]
    ENUM_SEAT_CLIMATE_AREA_CUSHION: _ClassVar[EnumSeatClimateArea]
    ENUM_SEAT_CLIMATE_AREA_SQUAB: _ClassVar[EnumSeatClimateArea]

class EnumSeatClimateState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED: _ClassVar[EnumSeatClimateState]
    ENUM_SEAT_CLIMATE_STATE_ON: _ClassVar[EnumSeatClimateState]
    ENUM_SEAT_CLIMATE_STATE_OFF: _ClassVar[EnumSeatClimateState]
    ENUM_SEAT_CLIMATE_STATE_INHIBIT: _ClassVar[EnumSeatClimateState]

class EnumHSWTemperatureLevel(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED: _ClassVar[EnumHSWTemperatureLevel]
    ENUM_HSW_TEMPERATURE_LEVEL_OFF: _ClassVar[EnumHSWTemperatureLevel]
    ENUM_HSW_TEMPERATURE_LEVEL_1: _ClassVar[EnumHSWTemperatureLevel]
    ENUM_HSW_TEMPERATURE_LEVEL_2: _ClassVar[EnumHSWTemperatureLevel]
ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_OFF: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5: EnumSeatClimateIntensity
ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_AREA_ALL: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_AREA_CUSHION: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_AREA_SQUAB: EnumSeatClimateArea
ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED: EnumSeatClimateState
ENUM_SEAT_CLIMATE_STATE_ON: EnumSeatClimateState
ENUM_SEAT_CLIMATE_STATE_OFF: EnumSeatClimateState
ENUM_SEAT_CLIMATE_STATE_INHIBIT: EnumSeatClimateState
ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED: EnumHSWTemperatureLevel
ENUM_HSW_TEMPERATURE_LEVEL_OFF: EnumHSWTemperatureLevel
ENUM_HSW_TEMPERATURE_LEVEL_1: EnumHSWTemperatureLevel
ENUM_HSW_TEMPERATURE_LEVEL_2: EnumHSWTemperatureLevel

class SeatClimateOperation(_message.Message):
    __slots__ = ["seat_area", "seat_state", "seat_climate_intensity"]
    SEAT_AREA_FIELD_NUMBER: _ClassVar[int]
    SEAT_STATE_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    seat_area: EnumSeatClimateArea
    seat_state: EnumSeatClimateState
    seat_climate_intensity: EnumSeatClimateIntensity
    def __init__(self, seat_area: _Optional[_Union[EnumSeatClimateArea, str]] = ..., seat_state: _Optional[_Union[EnumSeatClimateState, str]] = ..., seat_climate_intensity: _Optional[_Union[EnumSeatClimateIntensity, str]] = ...) -> None: ...

class SeatClimateZoneState(_message.Message):
    __slots__ = ["seat_selection", "seat_operation", "seat_climate_intensity"]
    SEAT_SELECTION_FIELD_NUMBER: _ClassVar[int]
    SEAT_OPERATION_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_INTENSITY_FIELD_NUMBER: _ClassVar[int]
    seat_selection: _seats_common_pb2.EnumSeatSelection
    seat_operation: _containers.RepeatedCompositeFieldContainer[SeatClimateOperation]
    seat_climate_intensity: EnumSeatClimateIntensity
    def __init__(self, seat_selection: _Optional[_Union[_seats_common_pb2.EnumSeatSelection, str]] = ..., seat_operation: _Optional[_Iterable[_Union[SeatClimateOperation, _Mapping]]] = ..., seat_climate_intensity: _Optional[_Union[EnumSeatClimateIntensity, str]] = ...) -> None: ...
