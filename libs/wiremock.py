import sys
import requests
from json<PERSON>_ng import parse
from robot.api.deco import keyword, not_keyword

sys.path.append("../libs")


@keyword("DELETE requests from request journal")
def delete_requests_from_request_journal(mock_base_url):
    response = requests.request("DELETE", mock_base_url + "__admin/requests")
    return response.status_code, response.text


@keyword("POST to remove specific requests in mock API request journal")
def post_to_remove_specific_requests_in_mock_api_request_journal(mock_base_url, mock_body):
    # Examples of what to include in the body
    # https://wiremock.org/docs/verifying/#criteria-queries &
    # https://wiremock.org/docs/verifying/#by-criteria
    response = requests.request("POST", mock_base_url + "__admin/requests/remove", json=mock_body)
    return response.status_code, response.text


@keyword("POST to find requests in mock API request journal")
def post_to_find_requests_in_mock_api_request_journal(mock_base_url, mock_body):
    # Examples of what to include in the body
    # https://wiremock.org/docs/verifying/#criteria-queries &
    # https://wiremock.org/docs/verifying/#by-criteria
    response = requests.request("POST", mock_base_url + "__admin/requests/find", json=mock_body)
    return response.status_code, response.text


@keyword("POST to find count of requests in mock API request journal")
def post_to_find_count_of_request_in_mock_api_request_journal(mock_base_url, mock_body={}):
    response = requests.request("POST", mock_base_url + "__admin/requests/count", json=mock_body)
    return response.status_code, response.text


# When making a request to the request journal for specific requests, this keyword can be used to assert the amount of requests found
@keyword("read position of last request in journal")
def read_position_of_last_request_in_journal(body):
    match = body["requests"]
    print(f"match = {match}\nlast request position: {len(match)-1}")
    return len(match) - 1


@keyword("Read json_path from journal response body")
def read_path_from_json(json_path, body):
    json_path_expression = parse(json_path)
    match = json_path_expression.find(body)
    value_array = match[0].value
    return value_array


@not_keyword
def create_stub(mock_base_url, request, response):
    stub = {"request": request, "response": response}
    wiremock_response = requests.post(f"{mock_base_url}/__admin/mappings", json=stub, headers={"Content-Type": "application/json"})
    return wiremock_response.status_code, wiremock_response.json().get("uuid")


@not_keyword
def delete_stub(mock_base_url, stub_uuid):
    return requests.delete(mock_base_url + "/__admin/mappings/" + stub_uuid)
