from robot.api.deco import keyword
import commonUtils


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"


serviceUrl = "oneapp-backend-url"


# GET features
@keyword("Get features")
def get_features(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me/features"

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]
