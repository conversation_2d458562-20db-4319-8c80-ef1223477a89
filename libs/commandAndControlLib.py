import forgeRock
import ngtp_adapter_service_pb2 as ngtp
import someip_adapter_service_pb2 as someip
import vehicleProtobuf
import vcdpHiveBroker
import notifyAdaptorProtbuf
import time
import commandAndControlLibOab
import requests
import base64
import os
from robot.api.deco import keyword, not_keyword
import threading
import queue
from pyaml_env import parse_config, BaseConfig
import json
from robot.api import logger
from datetime import datetime, timedelta


__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
VCDP_URI = "vcdp/"
VEHICLE_URI = "vehicle/"
LOG_PREFIX = "publish result: "

system_var_file = "../variables/system.yml"
system_vars = BaseConfig(parse_config(system_var_file))
authType = "Bearer "
request_sent = "Request sent: "
hvac_protobuf_deseralized = "hvacStart request protobuf deserialised: "
heated_stearing_wheel_protobuf_deseralized = "Heated Surface - Steering Wheel request protobuf deserialised: "
heated_seats_protobuf_deseralized = "Heated Seats - request protobuf deserialised: "
hive_topic_tail = "/va/data/stream/pre"
tsMode_path = "/config/mode"
svt_path = "/config/svt"


@not_keyword
def post_command(unique_id, command, vehicleArchitecture="EVA2"):
    system_vars = BaseConfig(parse_config(system_var_file))
    CC = getattr(system_vars, "command-and-control-url") + unique_id
    match command:
        case "lock" | "lockEva25":
            payload = "../json-request-data/c2/rdlReqPayload.json"
            forgeRockUsername = os.environ.get("RDLU_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("RDLU_FR_PASSWORD_PREPROD")
            url = CC + "/door"
        case "unlock" | "unlockEva25":
            payload = "../json-request-data/c2/rduReqPayload.json"
            forgeRockUsername = os.environ.get("RDLU_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("RDLU_FR_PASSWORD_PREPROD")
            url = CC + "/door"

        case "evStart" | "evStartEva25":
            payload = "../json-request-data/c2/evStartReqPayload.json"
            forgeRockUsername = os.environ.get("EVSS_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("EVSS_FR_PASSWORD_PREPROD")
            url = CC + "/evcharge"
        case "evStop" | "evStopEva25":
            payload = "../json-request-data/c2/evStopReqPayload.json"
            forgeRockUsername = os.environ.get("EVSS_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("EVSS_FR_PASSWORD_PREPROD")
            url = CC + "/evcharge"

        case "hvacStart" | "hvacStartEva25":
            payload = "../json-request-data/c2/hvacStartReqPayload.json"
            forgeRockUsername = os.environ.get("HVAC_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("HVAC_FR_PASSWORD_PREPROD")
            url = CC + "/climate"
        case "hvacStop" | "hvacStopEva25":
            payload = "../json-request-data/c2/hvacStopReqPayload.json"
            forgeRockUsername = os.environ.get("HVAC_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("HVAC_FR_PASSWORD_PREPROD")
            url = CC + "/climate"

        case "cacStart" | "cacStartEva25":
            payload = "../json-request-data/c2/cacStartReqPayload.json"
            forgeRockUsername = os.environ.get("CAC_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("CAC_FR_PASSWORD_PREPROD")
            url = CC + "/cabinairclean"

        case "cacStop" | "cacStopEva25":
            payload = "../json-request-data/c2/cacStopReqPayload.json"
            forgeRockUsername = os.environ.get("CAC_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("CAC_FR_PASSWORD_PREPROD")
            url = CC + "/cabinairclean"

        case "msoc" | "msocEva25":
            payload = "../json-request-data/c2/msocReqPayload.json"
            forgeRockUsername = os.environ.get("MSOC_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("MSOC_FR_PASSWORD_PREPROD")
            url = CC + "/evcharge/config/target"

        case "ar" | "arEva25":
            payload = "../json-request-data/c2/arReqPayload.json"
            forgeRockUsername = os.environ.get("AR_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("AR_FR_PASSWORD_PREPROD")
            url = CC + "/alarm"

        case "bnf" | "bnfEva25":
            payload = "../json-request-data/c2/bnfReqPayload.json"
            forgeRockUsername = os.environ.get("BNF_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("BNF_FR_PASSWORD_PREPROD")
            url = CC + "/beepflash"

        case "cbaLeft" | "cbaLeftEva25":
            payload = "../json-request-data/c2/cbaOpenLeftReqPayload.json"
            forgeRockUsername = os.environ.get("CBA_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("CBA_FR_PASSWORD_PREPROD")
            url = CC + "/chargedoor/left"

        case "cbaRight" | "cbaRightEva25":
            payload = "../json-request-data/c2/cbaOpenRightReqPayload.json"
            forgeRockUsername = os.environ.get("CBA_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("CBA_FR_PASSWORD_PREPROD")
            url = CC + "/chargedoor/right"

        case "ccuLock" | "ccuLockEva25":
            payload = "../json-request-data/c2/ccuLockReqPayload.json"
            forgeRockUsername = os.environ.get("CCU_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("CCU_FR_PASSWORD_PREPROD")
            url = CC + "/chargecablelock"

        case "ccuUnlock" | "ccuUnlockEva25":
            payload = "../json-request-data/c2/ccuUnlockReqPayload.json"
            forgeRockUsername = os.environ.get("CCU_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("CCU_FR_PASSWORD_PREPROD")
            url = CC + "/chargecablelock"

        case "hsSwOff" | "hsSwOffEva25":
            payload = "../json-request-data/c2/hsSwOffReq.json"
            forgeRockUsername = os.environ.get("HSSW_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("HSSW_FR_PASSWORD_PREPROD")
            url = CC + "/heatedsurface/steeringwheel"

        case "hsSwOn" | "hsSwOnEva25":
            payload = "../json-request-data/c2/hsSwOnReq.json"
            forgeRockUsername = os.environ.get("HSSW_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("HSSW_FR_PASSWORD_PREPROD")
            url = CC + "/heatedsurface/steeringwheel"

        case "ampLimit" | "ampLimitEva25":
            payload = "../json-request-data/c2/ampLimitReqPayload.json"
            forgeRockUsername = os.environ.get("AMP_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("AMP_FR_PASSWORD_PREPROD")
            url = CC + "/evcharge/config/ampslimit"

        case "wakeup":
            payload = "../json-request-data/c2/eva2WakeupReq.json"
            forgeRockUsername = os.environ.get("WAKEUP_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("WAKEUP_FR_PASSWORD_PREPROD")
            CC = getattr(system_vars, "c2-wakeup-url") + unique_id
            url = CC + "/wake"

        case "transportModeOn":
            payload = "../json-request-data/c2/transportModeOnReq.json"
            forgeRockUsername = os.environ.get("TSMODE_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("TSMODE_FR_PASSWORD_PREPROD")
            url = CC + tsMode_path

        case "transportModeOff":
            payload = "../json-request-data/c2/transportModeOffReq.json"
            forgeRockUsername = os.environ.get("TSMODE_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("TSMODE_FR_PASSWORD_PREPROD")
            url = CC + tsMode_path

        case "serviceModeOn":
            payload = "../json-request-data/c2/serviceModeOnReq.json"
            forgeRockUsername = os.environ.get("TSMODE_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("TSMODE_FR_PASSWORD_PREPROD")
            url = CC + tsMode_path

        case "serviceModeOff":
            payload = "../json-request-data/c2/serviceModeOffReq.json"
            forgeRockUsername = os.environ.get("TSMODE_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("TSMODE_FR_PASSWORD_PREPROD")
            url = CC + tsMode_path

        case "svtImmob":
            payload = "../json-request-data/c2/svtImmobReq.json"
            forgeRockUsername = os.environ.get("SVT_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("SVT_FR_PASSWORD_PREPROD")
            url = CC + svt_path

        case "svtRemob":
            payload = "../json-request-data/c2/svtRemobReq.json"
            forgeRockUsername = os.environ.get("SVT_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("SVT_FR_PASSWORD_PREPROD")
            url = CC + svt_path

        case "hvacV2" | "hvacV2SW" | "hvacV2HSCOOL" | "hvacV2HSHEAT":
            payload = "../json-request-data/c2/hvacV2Req.json"
            # forgeRockUsername = os.environ.get("HVAC_FR_EMAIL_PREPROD")
            # forgeRockPassword = os.environ.get("HVAC_FR_PASSWORD_PREPROD")
            forgeRockUsername = os.environ.get("HVAC_V2_FR_EMAIL_PREPROD")
            forgeRockPassword = os.environ.get("HVAC_V2_FR_PASSWORD_PREPROD")
            url = CC + "/v2/climate"

    if vehicleArchitecture == "EVA25":
        forgeRockUsername = os.environ.get("EVA25_FR_EMAIL_PREPROD")
        forgeRockPassword = os.environ.get("EVA25_FR_PASSWORD_PREPROD")
        token = forgeRock.get_token(forgeRockUsername, forgeRockPassword)
    else:
        token = forgeRock.get_token(forgeRockUsername, forgeRockPassword)

    # token = forgeRock.get_token(forgeRockUsername, forgeRockPassword)
    headers = {"Authorization": authType + token}
    print("FRUSERNAME:" + forgeRockUsername)

    jsonFile = open(payload, "r")
    read = jsonFile.read()
    reqBody = json.loads(read)

    print("Vehicle Architecture: " + vehicleArchitecture)
    print("url:")
    print(url)
    print(reqBody)
    response = requests.request("POST", url, headers=headers, json=reqBody)
    print(request_sent, str(int(time.time() * 1000)))
    print(response.status_code, response.text, response.headers)
    return (response.status_code, response.text, response.headers)


@not_keyword
def post_tsdp_routed_command(unique_id, command):
    CC = getattr(system_vars, "command-and-control-url") + unique_id
    match command:
        case "lock":
            reqData = "../json-request-data/c2/rdlReqPayload.json"
            endpoint = CC + "/door"
        case "unlock":
            reqData = "../json-request-data/c2/rduReqPayload.json"
            endpoint = CC + "/door"

        case "evStart":
            reqData = "../json-request-data/c2/evStartReqPayload.json"
            endpoint = CC + "/evcharge"
        case "evStop":
            reqData = "../json-request-data/c2/evStopReqPayload.json"
            endpoint = CC + "/evcharge"

        case "hvacStart":
            reqData = "../json-request-data/c2/hvacStartReqPayload.json"
            endpoint = CC + "/climate"
        case "hvacStop":
            reqData = "../json-request-data/c2/hvacStopReqPayload.json"
            endpoint = CC + "/climate"

        case "cacStart":
            reqData = "../json-request-data/c2/cacStartReqPayload.json"
            endpoint = CC + "/cabinairclean"
        case "cacStop":
            reqData = "../json-request-data/c2/cacStopReqPayload.json"
            endpoint = CC + "/cabinairclean"

        case "msoc":
            reqData = "../json-request-data/c2/msocReqPayload.json"
            endpoint = CC + "/evcharge/config/target"

        case "ar":
            reqData = "../json-request-data/c2/arReqPayload.json"
            endpoint = CC + "/alarm"

        case "bnf":
            reqData = "../json-request-data/c2/bnfReqPayload.json"
            endpoint = CC + "/beepflash"

        case "cbaLeft":
            reqData = "../json-request-data/c2/cbaOpenLeftReqPayload.json"
            endpoint = CC + "/chargedoor/left"
        case "cbaRight":
            reqData = "../json-request-data/c2/cbaOpenRightReqPayload.json"
            endpoint = CC + "/chargedoor/right"

        case "ccuLock":
            reqData = "../json-request-data/c2/ccuLockReqPayload.json"
            endpoint = CC + "/chargecablelock"
        case "ccuUnlock":
            reqData = "../json-request-data/c2/ccuLockReqPayload.json"
            endpoint = CC + "/chargecablelock"

        case "hsSwOff":
            reqData = "../json-request-data/c2/hsSwOffReq.json"
            endpoint = CC + "/heatedsurface/steeringwheel"

        case "hsSwOn":
            reqData = "../json-request-data/c2/hsSwOnReq.json"
            endpoint = CC + "/heatedsurface/steeringwheel"

        case "ampLimit":
            reqData = "../json-request-data/c2/ampLimitReqPayload.json"
            endpoint = CC + "/evcharge/config/ampslimit"

        case "wakeup":
            reqData = "../json-request-data/c2/eva2WakeupReq.json"
            CC = getattr(system_vars, "c2-wakeup-url") + unique_id
            endpoint = CC + "/wake"

        case "transportModeOn":
            reqData = "../json-request-data/c2/transportModeOnReq.json"
            endpoint = CC + tsMode_path
        case "transportModeOff":
            reqData = "../json-request-data/c2/transportModeOffReq.json"
            endpoint = CC + tsMode_path

        case "serviceModeOn":
            reqData = "../json-request-data/c2/serviceModeOnReq.json"
            endpoint = CC + tsMode_path
        case "serviceModeOff":
            reqData = "../json-request-data/c2/serviceModeOffReq.json"
            endpoint = CC + tsMode_path

    FR_USERNAME = os.environ.get("TSDP_ROUTE_FR_EMAIL_PREPROD")
    FR_PASSWORD = os.environ.get("TSDP_ROUTE_FR_PASSWORD_PREPROD")
    token = forgeRock.get_token(FR_USERNAME, FR_PASSWORD)
    headers = {"Authorization": authType + token}

    jsonFile = open(reqData, "r")
    read = jsonFile.read()
    reqBody = json.loads(read)

    print(endpoint)
    print(reqBody)
    response = requests.request("POST", endpoint, headers=headers, json=reqBody)
    print(response.status_code, response.text)
    return (response.status_code, response.text)


@not_keyword
def post_heated_surface_command(unique_id, command, temperature):
    system_vars = BaseConfig(parse_config(system_var_file))
    CC = getattr(system_vars, "command-and-control-url") + unique_id

    record = "../json-request-data/c2/hSeatsEmpty.json"
    payload_to_post = payload_builder(record, command, temperature)

    url = CC + "/heatedsurface/seats"
    forgeRockUsername = os.environ.get("HSEATS_FR_EMAIL_PREPROD")
    forgeRockPassword = os.environ.get("HSEATS_FR_PASSWORD_PREPROD")
    token = forgeRock.get_token(forgeRockUsername, forgeRockPassword)
    headers = {"Authorization": authType + token}

    payloadToInsert = json.loads(payload_to_post)

    print(url)
    # print(reqBody)
    response = requests.request("POST", url, headers=headers, json=payloadToInsert)
    print(request_sent, str(int(time.time() * 1000)))
    print(response.status_code, response.text)
    return (response.status_code, response.text)


@not_keyword
def deserialise(protobuf, service):
    # deserialize it to check
    match service:
        case "lock" | "unlock":
            request = ngtp.SetApertureLockStateRequest()
            request.ParseFromString(protobuf)
            return request
        case "evStart" | "evStop":
            request = someip.SetChargeControlRequest()
            request.ParseFromString(protobuf)
            return request
        case "hvacStart" | "hvacStop":
            request = someip.SetVehiclePreconditionRequest()
            request.ParseFromString(protobuf)
            return request
        case "cacStart" | "cacStop":
            request = ngtp.SetVehicleCabinAirCleanRequest()
            request.ParseFromString(protobuf)
            return request
        case "msoc":
            request = someip.SetBatteryMaxStateOfChargeRequest()
            request.ParseFromString(protobuf)
            return request
        case "ar":
            request = ngtp.SetVehicleAlarmRequest()
            request.ParseFromString(protobuf)
            return request
        case "bnf":
            request = ngtp.SetBeepAndFlashRequest()
            request.ParseFromString(protobuf)
            return request
        case "cbaLeft" | "cbaRight":
            request = someip.SetChargeDoorOperationRequest()
            request.ParseFromString(protobuf)
            return request
        case "ccuLock" | "ccuUnlock":
            request = someip.SetChargeCableOperationRequest()
            request.ParseFromString(protobuf)
            return request
        case "hsSwOff" | "hsSwOn":
            request = someip.SetHeatedSteeringWheelRequest()
            request.ParseFromString(protobuf)
            return request
        case "hSeatsFrontLeftCool" | "hSeatsFrontLeftHeat" | "hSeatsFrontLeftOff":
            request = someip.SetSeatClimateRequest()
            request.ParseFromString(protobuf)
            return request
        case "hSeatsFrontRightCool" | "hSeatsFrontRightHeat" | "hSeatsFrontRightOff":
            request = someip.SetSeatClimateRequest()
            request.ParseFromString(protobuf)
            return request
        case "ampLimit":
            request = someip.SetACChargeRateLimitRequest()
            request.ParseFromString(protobuf)
            return request


@not_keyword
def create_command_and_control_response_protobuf(command):
    """
    ToDo:  create protobuf, works locally, but not in CI/CD.  Hardcoded response to b'\x08\x01'
    Error:
    File "/builds/D9/testing/vcdp-e2e-test-hw-inloop/robot/libs/commandAndControlLib.py",
    line 54, in createCommandAndControlResponseProtobuf
    response.aperture_selection = "ENUM_APERTURE_SELECTION_DRIVER_DOOR"
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "/usr/local/lib/python3.11/site-packages/google/protobuf/internal/python_message.py", line 690, in field_setter
    raise TypeError(TypeError: Cannot set jlr.protobuf.doorlockunlock.SetApertureLockStateRequest.aperture_selection to
    'ENUM_APERTURE_SELECTION_DRIVER_DOOR': 'ENUM_APERTURE_SELECTION_DRIVER_DOOR' has type <class 'str'>,
    but expected one of: (<class 'int'>,)
    """
    match command:
        case "hSeatsFrontLeftCool":
            protobufString = b"\x08\x01\x10\x01"
        case "hSeatsFrontLeftHeat":
            protobufString = b"\x08\x01\x10\x01"
        case "hSeatsFrontLeftOff":
            protobufString = b"\x08\x01\x10\x01"
        case "hSeatsFrontRightCool":
            protobufString = b"\x08\x02\x10\x01"
        case "hSeatsFrontRightHeat":
            protobufString = b"\x08\x02\x10\x01"
        case "hSeatsFrontRightOff":
            protobufString = b"\x08\x02\x10\x01"
        case "svtImmob":
            protobufString = b"\x08\xd2\t\x10\x05\x18\x03"
        case "svtRemob":
            protobufString = b"\x08\xd2\t\x10\x05\x18\x01"
        case _:
            protobufString = b"\x08\x01"

    print("Protobuff response::  ", protobufString)
    return protobufString


@not_keyword
def create_request(command):
    payload = json.loads(vcdpHiveBroker.SubsData)
    print("subsdata:")
    protobuf = payload.get("payload")
    rawProto = base64.b64decode(protobuf)
    request = deserialise(rawProto, command)
    print(request)
    print("Protobuf deserialised: ", str(int(time.time() * 1000)))
    return request


@not_keyword
def match_command(unique_id, command):
    match command:
        case "evStart" | "evStop":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setChargeControlRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setChargeControlResponse"
            query = "Q7105004"
            signal = "S00101"
        case "lock" | "unlock":
            requestTopic = VCDP_URI + unique_id + "/rvc/ctrl/setApertureLockStateRequest"
            responseTopic = VEHICLE_URI + unique_id + "/rvc/sts/setApertureLockStateResponse"
            query = "Q710201006"
            signal = "VA-SIG-CentralLockStatus"
            # signal = "S00080"
        case "hvacStart" | "hvacStop":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setVehiclePreconditionRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setVehiclePreconditionResponse"
            query = "Q7103013"
            signal = "VA-SIG-FrontSystemOnCmd"
        case "cacStart" | "cacStop":
            requestTopic = VCDP_URI + unique_id + "/rvc/ctrl/setVehicleCabinAirCleanRequest"
            responseTopic = VEHICLE_URI + unique_id + "/rvc/sts/setVehicleCabinAirCleanResponse"
            query = "Q7103004"
            signal = "VA-SIG-CabinCleaningActivStat"
        case "msoc":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setBatteryMaxStateOfChargeRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setBatteryMaxStateOfChargeResponse"
            query = "Q7105002"
            signal = "VA-SIG-BulkSoCTargetDisp"
        case "ar":
            requestTopic = VCDP_URI + unique_id + "/rvc/ctrl/setVehicleAlarmRequest"
            responseTopic = VEHICLE_URI + unique_id + "/rvc/sts/setVehicleAlarmResponse"
            query = "Q7102003"
            signal = "VA-SIG-AlarmModeMS"
        case "bnf":
            requestTopic = VCDP_URI + unique_id + "/rvc/ctrl/setBeepAndFlashRequest"
            responseTopic = VEHICLE_URI + unique_id + "/rvc/sts/setBeepAndFlashResponse"
            query = None
            signal = None
        case "cbaLeft" | "cbaRight":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setChargeDoorOperationRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setChargeDoorOperationResponse"
            query = None
            signal = None
        case "ccuLock" | "ccuUnlock":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setChargeCableOperationRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setChargeCableOperationResponse"
            query = None
            signal = None
        case "hsSwOff" | "hsSwOn":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setHeatedSteeringWheelRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setHeatedSteeringWheelResponse"
            query = "Q7103023"
            signal = "VA-SIG-HeatedSteerWheelStatus"
        case "ampLimit":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setACChargeRateLimitRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setACChargeRateLimitResponse"
            query = "Q711501003"
            signal = "S00308"
        case "svtImmob" | "svtRemob":
            requestTopic = VCDP_URI + unique_id + "_tcua/svt/ctrl/svtImmobilizeReq"
            responseTopic = VEHICLE_URI + unique_id + "_tcua/svt/sts/svtImmobilizeResp"
            query = None
            signal = None
        case "hvacV2":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setVehiclePreconditionRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setVehiclePreconditionResponse"
            query = "Q7103013"
            signal = "VA-SIG-FrontSystemOnCmd"
        case _:
            print("Unexpected command")

            query = None
    return (requestTopic, responseTopic, query, signal)


@not_keyword
def match_command_eva25(unique_id, command):
    print("Confirming EVA25 data")
    match command:
        case "evStartEva25" | "evStopEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setChargeControlRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setChargeControlResponse"
            query = "Q7105004"
            signal = "S00101"
        case "lockEva25" | "unlockEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setApertureLockStateRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setApertureLockStateResponse"
            query = "Q710201006"
            signal = "VA-SIG-CentralLockStatus"
        case "hvacStartEva25" | "hvacStopEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setVehiclePreconditionRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setVehiclePreconditionResponse"
            query = "Q7103013"
            signal = "VA-SIG-FrontSystemOnCmd"
        case "cacStartEva25" | "cacStopEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setVehicleCabinAirCleanRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setVehicleCabinAirCleanResponse"
            query = "Q7103004"
            signal = "VA-SIG-CabinCleaningActivStat"
        case "msocEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setBatteryMaxStateOfChargeRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setBatteryMaxStateOfChargeResponse"
            query = "Q7105002"
            signal = "VA-SIG-BulkSoCTargetDisp"
        case "arEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setVehicleAlarmRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setVehicleAlarmResponse"
            query = "Q7102003"
            signal = "VA-SIG-AlarmModeMS"
        case "bnfEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setBeepAndFlashRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setBeepAndFlashResponse"
            query = None
            signal = None
        case "cbaLeft" | "cbaRight" | "cbaLeftEva25" | "cbaRightEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setChargeDoorOperationRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setChargeDoorOperationResponse"
            query = None
            signal = None
        case "ccuLock" | "ccuUnlock" | "ccuLockEva25" | "ccuUnlockEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setChargeCableOperationRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setChargeCableOperationResponse"
            query = None
            signal = None
        case "hsSwOffEva25" | "hsSwOnEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setHeatedSteeringWheelRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setHeatedSteeringWheelResponse"
            query = "Q7103023"
            signal = "VA-SIG-HeatedSteerWheelStatus"
        case "ampLimitEva25":
            requestTopic = VCDP_URI + unique_id + "_vcm/sga/ctrl/request/setACChargeRateLimitRequest"
            responseTopic = VEHICLE_URI + unique_id + "_vcm/sga/data/response/setACChargeRateLimitResponse"
            query = "Q711501003"
            signal = "S00308"
        case _:
            print("Unexpected command")

            query = None

    # requestTopic = "vcdp/" + unique_id + "/sga/ctrl/request"
    # responseTopic = "vehicle/" + unique_id + "/sga/data/response"

    return (requestTopic, responseTopic, query, signal)


@not_keyword
def match_heated_surface_command(unique_id, command):
    class PartMatchString(str):  # This class overrides the default full string match in match case and uses contains
        def __eq__(self, other):
            return self.__contains__(other)

    part_match_request = PartMatchString(command)

    match part_match_request:
        case "hSeatsFrontLeft":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setSeatClimateRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setSeatClimateResponse"
            query = "Q7103016"
            signal = "VA-SIG-HeatedSeatFLModeRequest"
            query2 = "Q7103018"
            signal2 = "VA-SIG-HeatedSeatFLRequest"
        case "hSeatsFrontRight":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setSeatClimateRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setSeatClimateResponse"
            query = "Q7103019"
            signal = "VA-SIG-HeatedSeatFRModeRequest"
            query2 = "Q7103021"
            signal2 = "VA-SIG-HeatedSeatFRRequest"
        case _:
            print("Unexpected command")

    return (requestTopic, responseTopic, query, signal, query2, signal2)


@not_keyword
def match_hvacV2_command(unique_id, command):
    class PartMatchString(str):  # This class overrides the default full string match in match case and uses contains
        def __eq__(self, other):
            return self.__contains__(other)

    part_match_request = PartMatchString(command)

    match part_match_request:
        case "hvacV2":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setVehiclePreconditionRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setVehiclePreconditionResponse"
            query = "Q7103013"
            signal = "VA-SIG-FrontSystemOnCmd"
        case "hSeatsFrontRight":
            requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setSeatClimateRequest"
            responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setSeatClimateResponse"
            query = "Q7103019"
            signal = "VA-SIG-HeatedSeatFRModeRequest"
            query2 = "Q7103021"
            signal2 = "VA-SIG-HeatedSeatFRRequest"
        case _:
            print("Unexpected command")

    return (requestTopic, responseTopic, query, signal, query2, signal2)


@not_keyword
def get_signal_value(request, command):
    class PartMatchString(str):  # This class overrides the default full string match in match case and uses contains
        def __eq__(self, other):
            return self.__contains__(other)

    part_match_request = PartMatchString(request)

    print("Request param: ")
    print(request)

    # BNF
    if "bnf" in command:
        print("No data in present request topic: ", str(int(time.time() * 1000)))
        vaValue = "None"
        return vaValue

    elif "hvacV2" in command:
        vaValue = 1
        return vaValue
    elif "hvacV2SW" in command:
        vaValue = 1
        return vaValue
    elif "hvacV2HSCOOL" in command:
        vaValue = 1
        return vaValue
    elif "hvacV2HSHEAT" in command:
        vaValue = 1
        return vaValue

    match part_match_request:
        # evStart
        case "ENUM_CHARGE_CONTROL_OPERATION_START":
            print("EvStart request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 3
        # evStop
        case "ENUM_CHARGE_CONTROL_OPERATION_STOP":
            print("EvStop request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 4
        # RDL
        case "ENUM_APERTURE_OPERATION_EXTERNAL_SINGLELOCK":
            print("Lock request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 3
        # RDU
        case "ENUM_APERTURE_OPERATION_EXTERNAL_UNLOCK":
            print("Unlock request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 0
        # hvacStart
        case "ENUM_PRECONDITION_OPERATION_OFFBOARD_START":
            print(hvac_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 1
        # hvacStop
        case "ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP":
            print(hvac_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 0
        # cacStart
        case "ENUM_CABIN_AIR_CLEAN_OPERATION_START":
            print("cacStart request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 1
        # cacStop
        case "ENUM_CABIN_AIR_CLEAN_OPERATION_STOP":
            print("cacStop request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 3
        # MSOC
        case "max_battery_soc":
            print("msoc request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 40
        # AlarmReset
        case "ENUM_ALARM_OPERATION_STOP":
            print("alarm reset request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 0
        # CBALEFT
        case "ENUM_CHARGE_DOOR_LEFT":
            print("CBA Left request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = "None"
        # CBARIGHT
        case "ENUM_CHARGE_DOOR_RIGHT":
            print("CBA Right request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = "None"
        # CCULOCK
        case "ENUM_CHARGE_CABLE_OPERATION_LOCK":
            print("Ccu Lock  request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = "None"
        # CUUNLOCK
        case "ENUM_CHARGE_CABLE_OPERATION_UNLOCK":
            print("Ccu Unlock request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = "None"
        # HSSW - OFF
        case "ENUM_HSW_TEMPERATURE_LEVEL_OFF":
            print(heated_stearing_wheel_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 0
        # HSSW - ON
        case "ENUM_HSW_TEMPERATURE_LEVEL":
            print(heated_stearing_wheel_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 1
        # hSeats - COOL
        case "ENUM_SEAT_CLIMATE_INTENSITY_COOL":
            print(heated_seats_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 0
        # hSeats - HEAT
        case "ENUM_SEAT_CLIMATE_INTENSITY_HEAT":
            print(heated_seats_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 0
        # hSeats - OFF
        case "ENUM_SEAT_CLIMATE_INTENSITY_OFF":
            print(heated_seats_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue = 0
        # ampLimit
        case "48":
            print("AmpLimit request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue = 48
        case _:
            return "Unexpected decoded protobuf"

    return vaValue


@not_keyword
def get_second_signal_value(request, command):
    class PartMatchString(str):  # This class overrides the default full string match in match case and uses contains
        def __eq__(self, other):
            return self.__contains__(other)

    part_match_request = PartMatchString(request)

    # BNF
    if "bnf" in command:
        print("No data in present request topic: ", str(int(time.time() * 1000)))
        vaValue2 = "None"
        return vaValue2

    match part_match_request:
        # evStart
        case "ENUM_CHARGE_CONTROL_OPERATION_START":
            print("EvStart request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # evStop
        case "ENUM_CHARGE_CONTROL_OPERATION_STOP":
            print("EvStop request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # RDL
        case "ENUM_APERTURE_OPERATION_EXTERNAL_LOCK":
            print("Lock request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # RDU
        case "ENUM_APERTURE_OPERATION_UNLOCK":
            print("Unlock request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # hvacStart
        case "ENUM_PRECONDITION_OPERATION_START":
            print(hvac_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = None
        # hvacStop
        case "ENUM_PRECONDITION_OPERATION_STOP":
            print(hvac_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = None
        # cacStart
        case "ENUM_CABIN_AIR_CLEAN_OPERATION_START":
            print("cacStart request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # cacStop
        case "ENUM_CABIN_AIR_CLEAN_OPERATION_STOP":
            print("cacStop request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # MSOC
        case "max_battery_soc":
            print("msoc request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # AlarmReset
        case "ENUM_ALARM_OPERATION_STOP":
            print("alarm reset request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # CBALEFT
        case "ENUM_CHARGE_DOOR_LEFT":
            print("CBA Left request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # CBARIGHT
        case "ENUM_CHARGE_DOOR_RIGHT":
            print("CBA Right request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # CCULOCK
        case "ENUM_CHARGE_CABLE_OPERATION_LOCK":
            print("Ccu Lock  request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # CUUNLOCK
        case "ENUM_CHARGE_CABLE_OPERATION_UNLOCK":
            print("Ccu Unlock request protobuf deserialised: ", str(int(time.time() * 1000)))
            vaValue2 = None
        # HSSW - OFF
        case "ENUM_HSW_TEMPERATURE_LEVEL_OFF":
            print(heated_stearing_wheel_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = None
        # HSSW - ON
        case "ENUM_HSW_TEMPERATURE_LEVEL":
            print(heated_stearing_wheel_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = None
        # hSeats - COOL
        case "ENUM_SEAT_CLIMATE_INTENSITY_COOL":
            print(heated_seats_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = 6
        # hSeats - HEAT
        case "ENUM_SEAT_CLIMATE_INTENSITY_HEAT":
            print(heated_seats_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = 5
        # hSeats - OFF
        case "ENUM_SEAT_CLIMATE_INTENSITY_OFF":
            print(heated_seats_protobuf_deseralized, str(int(time.time() * 1000)))
            vaValue2 = 0
        case _:
            return "Unexpected decoded protobuf"
    return vaValue2


@not_keyword
def hive_broker_thread_for_c2_command(
    queue, unique_id, brokerUserRead, brokerClientSecretRead, brokerUserWrite, brokerClientSecretWrite, certfile, ca_certs, keyfile, command
):
    clientIdRead = "-e2eTest-" + str(int(time.time() * 1000))
    clientRead = vcdpHiveBroker.connect_to_broker(
        unique_id, brokerUserRead, brokerClientSecretRead, certfile, ca_certs, keyfile, client_id_suffix=clientIdRead
    )

    clientIdWrite = "-e2eTest-" + str(int(time.time() * 1000))
    clientWrite = vcdpHiveBroker.connect_to_broker(
        unique_id, brokerUserWrite, brokerClientSecretWrite, certfile, ca_certs, keyfile, client_id_suffix=clientIdWrite
    )
    clientRead.on_message = vcdpHiveBroker.on_message
    clientRead.loop_start()  # start the loop

    if "hSeats" in command:
        requestTopic, responseTopic, query, signal, query2, signal2 = match_heated_surface_command(unique_id, command)
    elif "Eva25" in command:
        requestTopic, responseTopic, query, signal = match_command_eva25(unique_id, command)
    else:
        requestTopic, responseTopic, query, signal = match_command(unique_id, command)

    commandsWithNoVA = [
        "bnf",
        "bnfEva25",
        "cbaLeft",
        "cbaRight",
        "cbaLeftEva25",
        "cbaRightEva25",
        "ccuUnlock",
        "ccuLock",
        "ccuLockEva25",
        "ccuUnlockEva25",
        "svtImmob",
        "svtRemob",
    ]

    print("Variables created: ", str(int(time.time() * 1000)))

    print("Subscribing to topic:: " + requestTopic)
    clientRead.subscribe(requestTopic, qos=1)
    print("Subscribed to request topic: ", str(int(time.time() * 1000)))
    timeout = 0
    print("Waiting for command......")

    while type(vcdpHiveBroker.SubsData) is int:
        time.sleep(0.1)
        timeout = timeout + 1
        if timeout == 100:  # 10 seconds
            print("timmed out")
            queue.put(-1)
            return ()

    timeGotCommandinHive = int(time.time() * 1000)

    if vcdpHiveBroker.SubsData == -1:
        return vcdpHiveBroker.SubsData

    request = create_request(command)
    vaValue = get_signal_value(request, command)
    vaValue2 = get_second_signal_value(request, command)

    responseProtobuf = create_command_and_control_response_protobuf(command)
    print("Response protobuf created: ", str(int(time.time() * 1000)))
    timePublishAckFromHive = int(time.time() * 1000)

    print("Command ack from vehicle")
    result = clientWrite.publish(responseTopic, payload=responseProtobuf, qos=1, retain=False)
    print(LOG_PREFIX + str(result))
    print("Response protobuf published: ", str(int(time.time() * 1000)))

    print("va signal sent... check vehicle shadow")
    if "hSeats" in command:
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query, signal, vaValue)
        vaData2 = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query2, signal2, vaValue2)
        topic = VEHICLE_URI + unique_id + hive_topic_tail
        result = clientWrite.publish(topic, payload=vaData2[1], qos=0, retain=False)
        result = clientWrite.publish(topic, payload=vaData[1], qos=0, retain=False)
        print(LOG_PREFIX + str(result))
    elif any(keyword in command for keyword in commandsWithNoVA):
        print("No VA data to publish")

    elif "evStart" in command:
        notifyTopic = "vehicle/" + unique_id + "/evt/data/notifyChargeState"
        notifyData = notifyAdaptorProtbuf.notify_charge_state(
            "ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS",
            "ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED",
            "ENUM_CHARGING_INLET_STATE_PLUGGED",
            "ENUM_CHARGE_CABLE_OPERATION_LOCK",
            "ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING",
        )
        notifyPayload = notifyData[0]
        print("Notify Data:")
        print(notifyData)

        print("Notify Payload:")
        print(notifyPayload)

        result = clientWrite.publish(notifyTopic, payload=notifyPayload, qos=0, retain=False)

    elif "evStop" in command:
        notifyTopic = "vehicle/" + unique_id + "/evt/data/notifyChargeState"
        notifyData = notifyAdaptorProtbuf.notify_charge_state(
            "ENUM_CHARGE_STATE_WAITING_TO_CHARGE",
            "ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED",
            "ENUM_CHARGING_INLET_STATE_PLUGGED",
            "ENUM_CHARGE_CABLE_OPERATION_LOCK",
            "ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING",
        )
        notifyPayload = notifyData[0]
        print("Notify Data:")
        print(notifyData)

        print("Notify Payload:")
        print(notifyPayload)

        result = clientWrite.publish(notifyTopic, payload=notifyPayload, qos=0, retain=False)
    else:
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query, signal, vaValue)
        topic = VEHICLE_URI + unique_id + hive_topic_tail
        result = clientWrite.publish(topic, payload=vaData[1], qos=0, retain=False)
        print("Published VA data")
        print(LOG_PREFIX + str(result))

    # topic = VEHICLE_URI + unique_id + hive_topic_tail
    # result = clientWrite.publish(topic, payload=vaData[1], qos=0, retain=False)
    # print(LOG_PREFIX + str(result))

    clientRead.disconnect()
    clientRead.loop_stop()
    clientWrite.disconnect()
    clientWrite.loop_stop()
    hiveTimes = [timeGotCommandinHive, timePublishAckFromHive]
    print("Command sent and va signal updated")
    queue.put(hiveTimes)


@not_keyword
def hive_broker_thread_hvacV2_command(
    queue, unique_id, brokerUserRead, brokerClientSecretRead, brokerUserWrite, brokerClientSecretWrite, certfile, ca_certs, keyfile, command
):
    clientIdRead = "-e2eTest-" + str(int(time.time() * 1000))
    clientRead = vcdpHiveBroker.connect_to_broker(
        unique_id, brokerUserRead, brokerClientSecretRead, certfile, ca_certs, keyfile, client_id_suffix=clientIdRead
    )

    clientIdWrite = "-e2eTest-" + str(int(time.time() * 1000))
    clientWrite = vcdpHiveBroker.connect_to_broker(
        unique_id, brokerUserWrite, brokerClientSecretWrite, certfile, ca_certs, keyfile, client_id_suffix=clientIdWrite
    )
    clientRead.on_message = vcdpHiveBroker.on_message
    clientRead.loop_start()  # start the loop

    print("HVAC V2!!")
    # Climate topics
    requestTopic = VCDP_URI + unique_id + "/evt/ctrl/setVehiclePreconditionRequest"
    responseTopic = VEHICLE_URI + unique_id + "/evt/sts/setVehiclePreconditionResponse"
    query = "Q7103013"
    signal = "VA-SIG-FrontSystemOnCmd"
    # SteeringWheel topics
    requestTopic2 = VCDP_URI + unique_id + "/evt/ctrl/setHeatedSteeringWheelRequest"
    responseTopic2 = VEHICLE_URI + unique_id + "/evt/sts/setHeatedSteeringWheelResponse"
    query2 = "Q7103023"
    signal2 = "VA-SIG-HeatedSteerWheelStatus"
    # Seats topics
    requestTopic3 = VCDP_URI + unique_id + "/evt/ctrl/setSeatClimateRequest"
    responseTopic3 = VEHICLE_URI + unique_id + "/evt/sts/setSeatClimateResponse"
    query3 = "Q7103016"
    signal3 = "VA-SIG-HeatedSeatFLModeRequest"
    query4 = "Q7103018"
    signal4 = "VA-SIG-HeatedSeatFLRequest"
    query5 = "Q7103019"
    signal5 = "VA-SIG-HeatedSeatFRModeRequest"
    query6 = "Q7103021"
    signal6 = "VA-SIG-HeatedSeatFRRequest"

    print("Variables created: ", str(int(time.time() * 1000)))

    print("Subscribing to topic:: " + requestTopic)
    clientRead.subscribe(requestTopic, qos=1)
    clientRead.subscribe(requestTopic2, qos=1)
    clientRead.subscribe(requestTopic3, qos=1)
    print("Subscribed to request topic: ", str(int(time.time() * 1000)))
    timeout = 0
    print("Waiting for command......")
    while type(vcdpHiveBroker.SubsData) is int:
        time.sleep(0.1)
        timeout = timeout + 1
        if timeout == 100:  # 10 seconds
            print("timmed out")
            queue.put(-1)
            return ()

    timeGotCommandinHive = int(time.time() * 1000)

    if vcdpHiveBroker.SubsData == -1:
        return vcdpHiveBroker.SubsData

    request = create_request(command)
    vaValue = get_signal_value(request, command)
    responseProtobuf = create_command_and_control_response_protobuf(command)
    responseProtobuf2 = create_command_and_control_response_protobuf("hSeatsFrontLeftCool")
    print("Response protobuf created: ", str(int(time.time() * 1000)))
    timePublishAckFromHive = int(time.time() * 1000)

    print("Command ack from vehicle")
    result = clientWrite.publish(responseTopic, payload=responseProtobuf, qos=1, retain=False)
    time.sleep(5)
    result = clientWrite.publish(responseTopic2, payload=responseProtobuf, qos=1, retain=False)
    time.sleep(5)
    result = clientWrite.publish(responseTopic3, payload=responseProtobuf2, qos=1, retain=False)
    print(LOG_PREFIX + str(result))
    print("Response protobuf published: ", str(int(time.time() * 1000)))

    print("va signal sent... check vehicle shadow")

    if command == "hvacV2":
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query, signal, vaValue)
    if command == "hvacV2SW":
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query2, signal2, vaValue)
    if command == "hvacV2HSCOOL":
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query3, signal3, 0)
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query4, signal4, 6)
    if command == "hvacV2HSHEAT":
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query5, signal5, 0)
        vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query6, signal6, 5)

    topic = VEHICLE_URI + unique_id + hive_topic_tail
    result = clientWrite.publish(topic, payload=vaData[1], qos=0, retain=False)
    print(LOG_PREFIX + str(result))

    notifyTopic = "vehicle/" + unique_id + "/evt/data/notifySeatClimateStatus"
    notifyData = notifyAdaptorProtbuf.notify_seats_climate_state(
        "someip",
        "ENUM_SEAT_SELECTION_SECOND_ROW_LEFT",
        "1",
        "ENUM_SEAT_CLIMATE_AREA_ALL",
        "ENUM_SEAT_CLIMATE_STATE_ON",
        "ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3",
    )
    notifyData2 = notifyAdaptorProtbuf.notify_seats_climate_state(
        "someip",
        "ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT",
        "1",
        "ENUM_SEAT_CLIMATE_AREA_ALL",
        "ENUM_SEAT_CLIMATE_STATE_ON",
        "ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3",
    )
    notifyPayload = notifyData[0]
    notifyPayload2 = notifyData2[0]
    print("Notify Data:")
    print(notifyData)

    print("Notify Payload:")
    print(notifyPayload)

    result = clientWrite.publish(notifyTopic, payload=notifyPayload, qos=0, retain=False)
    result = clientWrite.publish(notifyTopic, payload=notifyPayload2, qos=0, retain=False)

    clientRead.disconnect()
    clientRead.loop_stop()
    clientWrite.disconnect()
    clientWrite.loop_stop()
    hiveTimes = [timeGotCommandinHive, timePublishAckFromHive]
    print("Command sent and va signal updated")
    queue.put(hiveTimes)


@keyword("remote vehicle command to vehicle")
def send_remote_vehicle_command(
    unique_id,
    command,
    vehicleArchitecture,
    brokerUserRead,
    brokerClientSecretRead,
    brokerUserWrite,
    brokerClientSecretWrite,
    certfile,
    ca_certs,
    keyfile,
    useOneAppBackend=False,
    latencyTest=False,
):
    # start a thread
    q = queue.Queue()
    if "hvacV2" in command:
        thread_function = hive_broker_thread_hvacV2_command
    else:
        thread_function = hive_broker_thread_for_c2_command
    x = threading.Thread(
        target=thread_function,
        args=(q, unique_id, brokerUserRead, brokerClientSecretRead, brokerUserWrite, brokerClientSecretWrite, certfile, ca_certs, keyfile, command),
        daemon=True,
    )
    x.start()
    x.join(5)
    # send command
    print(vehicleArchitecture)
    print("Send Command....")
    lockCommandSent = int(time.time() * 1000)
    if useOneAppBackend is False:
        response = post_command(unique_id, command, vehicleArchitecture)
    else:
        response = commandAndControlLibOab.command_one_app_back_end(unique_id, command, vehicleArchitecture)

    print(response)
    lockCommandResponse = int(time.time() * 1000)
    x.join(0)
    result = q.get()
    print("thread result:: " + str(result))

    if latencyTest:
        logger.info(response)
        logger.info(result)
        return (response[0], lockCommandSent, lockCommandResponse, result[0], result[1], result[2])
    else:
        return (result, response[0], response[1], response[2])  # returns the timestamp of the va message


@keyword("send wake up request whilst publishing into heartbeat Hive Topic")
def send_wake_up_request_whilst_subscribed_to_heartbeat_topic(
    uniqueID, UUID, heartbeat, heartbeatValue, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile
):
    """
    Publishes into specfic heartbeat topic for UUID
    """
    if heartbeat == "true":
        topic = "vehicle/" + UUID + "/obg/sts/connection"
        print("UUID: " + UUID)
        payload = create_heartbeat_payload(UUID, heartbeatValue)
        payload2 = json.dumps(payload)
        print("Heartbeat Hive payload: " + payload2)
        client = vcdpHiveBroker.connect_to_broker(uniqueID, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, client_id_suffix="")
        client.loop_start()
        print("Publishing to topic: " + topic)
        client.publish(topic, payload=payload2, qos=1, retain=True)
        statusCode = post_command(UUID, "wakeup")
        time.sleep(0.5)
        client.loop_stop()
        client.disconnect()

        print("rawproto subsData:")
        return statusCode[0], statusCode[1]
    else:
        print("No heartbeat")


@keyword("send tsdp command to vehicle")
def send_tsdp_command_to_vehicle(unique_id, command):
    response = post_tsdp_routed_command(unique_id, command)
    return (response[0], response[1])  # returns the timestamp of the va message


@keyword("heated surface remote vehicle command to vehicle")
def send_heated_surface_remote_vehicle_command(
    unique_id,
    command,
    temperature,
    brokerUserRead,
    brokerClientSecretRead,
    brokerUserWrite,
    brokerClientSecretWrite,
    certfile,
    ca_certs,
    keyfile,
    useOneAppBackend=False,
    latencyTest=False,
):
    # start a thread
    que = queue.Queue()
    thread_function = hive_broker_thread_for_c2_command
    t = threading.Thread(
        target=thread_function,
        args=(que, unique_id, brokerUserRead, brokerClientSecretRead, brokerUserWrite, brokerClientSecretWrite, certfile, ca_certs, keyfile, command),
        daemon=True,
    )
    t.start()
    t.join(5)
    # send command
    print("Send Command....")
    commandSent = int(time.time() * 1000)
    if useOneAppBackend is False:
        response = post_heated_surface_command(unique_id, command, temperature)
    else:
        response = commandAndControlLibOab.post_heated_surface_command_oab(unique_id, command, temperature)

    print(response)
    commandResponse = int(time.time() * 1000)
    t.join(0)
    result = que.get()
    print("thread result:: " + str(result))

    if latencyTest:
        return (
            response[0],
            commandSent,
            commandResponse,
            result[0],
            result[1],
        )
    else:
        return (result, response[0], response[1])  # returns the timestamp of the va message


def hive_broker_thread_for_c2_command_fr_auth(
    queue, unique_id, frToken, certfile, ca_certs, keyfile, command, timeoutInSecs=15, calledAsBinaryFile=False
):
    client = vcdpHiveBroker.connect_to_broker(
        uniqueId=unique_id,
        certfile=certfile,
        ca_certs=ca_certs,
        keyfile=keyfile,
        auth=True,
        tls=True,
        frAuth=True,
        frToken=frToken,
        brokerUser="",
        brokerClientSecret="",
        calledAsBinaryFile=calledAsBinaryFile,
    )

    client.on_message = vcdpHiveBroker.on_message
    client.loop_start()  # start the loop

    requestTopic, responseTopic, query, signal = match_command(unique_id, command)

    print("Variables created: ", str(int(time.time() * 1000)))

    print("Subscribing to topic:: " + requestTopic)
    client.subscribe(requestTopic, qos=1)
    print("Subscribed to request topic: ", str(int(time.time() * 1000)))
    timeoutInSecs = timeoutInSecs * 10
    timeout = 0
    print("Waiting for command......")
    while type(vcdpHiveBroker.SubsData) is int:
        time.sleep(0.1)
        timeout = timeout + 1
        if timeout == timeoutInSecs:  # 10 seconds
            print("timmed out")
            queue.put(-1)
            return ()

    if vcdpHiveBroker.SubsData == -1:
        return vcdpHiveBroker.SubsData

    request = create_request(command)
    vaValue = get_signal_value(request, command)

    responseProtobuf = create_command_and_control_response_protobuf(command)
    print("Response protobuf created: ", str(int(time.time() * 1000)))

    print("Command ack from vehicle")
    result = client.publish(responseTopic, payload=responseProtobuf, qos=1, retain=False)
    print(LOG_PREFIX + str(result))
    print("Response protobuf published: ", str(int(time.time() * 1000)))

    print("va lock signal sent... check vehicle shadow")
    vaData = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query, signal, vaValue)

    topic = VEHICLE_URI + unique_id + hive_topic_tail
    result = client.publish(topic, payload=vaData[1], qos=1, retain=False)
    print(LOG_PREFIX + str(result))

    client.disconnect()
    client.loop_stop()
    print("Command sent and va signal updated")
    queue.put(vaData[2])


@not_keyword
def payload_builder(record, requestService, temperature):
    # This class overrides the default full string match in match case and uses contains
    class PartMatchString(str):
        def __eq__(self, other):
            return self.__contains__(other)

    part_match_request = PartMatchString(requestService)

    record = open(record, "r")
    y = record.read()
    record = json.loads(y)

    match part_match_request:
        case "hSeatsFrontLeft":
            record["seats"]["FIRST_ROW_LEFT"] = {"temperatureLevel": temperature}
        case "hSeatsFrontMid":
            record["seats"]["FIRST_ROW_MIDDLE"] = {"temperatureLevel": temperature}
        case "hSeatsFrontRight":
            record["seats"]["FIRST_ROW_RIGHT"] = {"temperatureLevel": temperature}
        case "hSeatsFront":
            record["seats"]["FIRST_ROW_LEFT"] = {"temperatureLevel": temperature}
            record["seats"]["FIRST_ROW_RIGHT"] = {"temperatureLevel": temperature}
            record["seats"]["FIRST_ROW_MIDDLE"] = {"temperatureLevel": temperature}

        case "hSeatsMidLeft":
            record["seats"]["SECOND_ROW_LEFT"] = {"temperatureLevel": temperature}
        case "hSeatsMidMid":
            record["seats"]["SECOND_ROW_CENTRE"] = {"temperatureLevel": temperature}
        case "hSeatsMidRight":
            record["seats"]["SECOND_ROW_RIGHT"] = {"temperatureLevel": temperature}
        case "hSeatsMid":
            record["seats"]["SECOND_ROW_LEFT"] = {"temperatureLevel": temperature}
            record["seats"]["SECOND_ROW_RIGHT"] = {"temperatureLevel": temperature}
            record["seats"]["SECOND_ROW_CENTRE"] = {"temperatureLevel": temperature}

        case "hSeatsBackLeft":
            record["seats"]["THIRD_ROW_LEFT"] = {"temperatureLevel": temperature}
        case "hSeatsBackMid":
            record["seats"]["THIRD_ROW_CENTRE"] = {"temperatureLevel": temperature}
        case "hSeatsBackRight":
            record["seats"]["THIRD_ROW_RIGHT"] = {"temperatureLevel": temperature}
        case "hSeatsBack":
            record["seats"]["THIRD_ROW_LEFT"] = {"temperatureLevel": temperature}
            record["seats"]["THIRD_ROW_RIGHT"] = {"temperatureLevel": temperature}
            record["seats"]["THIRD_ROW_CENTRE"] = {"temperatureLevel": temperature}

    if requestService == "hSeatsAll":
        record = "../json/hSeats/hSeatsRequestAllPayload.json"
        record = open(record, "r")
        y = record.read()
        record = json.loads(y)

    for seat in record.get("seats").values():
        if "temperatureLevel" in seat:
            seat["temperatureLevel"] = temperature

    record = json.dumps(record, indent=4, separators=(",", ":"))
    print(record)
    return record


@keyword("clear retained messages from heartbeat topic")
def clear_retained_msg_from_heartbeat_topic(uniqueID, UUID, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile):
    """
    Connects to VCDP test broker and send am empty payload to topic:\n
    To remove all retained messages
    """
    topic = "vehicle/" + UUID + "/obg/sts/connection"
    client = vcdpHiveBroker.connect_to_broker(uniqueID, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, client_id_suffix="")
    client.loop_start()
    client.publish(topic, payload="", qos=0, retain=True)
    time.sleep(1)
    print("published!!")
    client.disconnect()
    client.loop_stop()


@not_keyword
def create_heartbeat_payload(UUID, heartbeat):
    curr_time = round(time.time() * 1000)
    print(type(curr_time))
    print("Milliseconds since epoch: ", curr_time)
    current_time = datetime.now()
    adj_time = current_time - timedelta(seconds=60)
    curr_time = int(adj_time.timestamp() * 1000)

    payload = {"vehicleId": UUID, "status": heartbeat, "created": curr_time}

    print(payload)
    return payload
