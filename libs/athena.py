import boto3
import time
from botocore.exceptions import NoCredentialsError, ParamValidationError
from robot.api.deco import keyword

athena_client = boto3.client("athena", region_name="eu-west-2")


# Custom exception for failed DB queries
class DatabaseQueryException(Exception):
    def __init__(self, message="DB query exception has occured"):
        self.message = message
        super().__init__(self.message)


def get_data_catalog(data_catalog_name):
    return athena_client.get_data_catalog(Name=data_catalog_name)


def get_database(catalog_name, database_name):
    return athena_client.get_database(CatalogName=catalog_name, DatabaseName=database_name)


def get_query_results(query_execution_id):
    response = athena_client.get_query_results(QueryExecutionId=query_execution_id)
    return response.get("ResultSet").get("Rows")


def start_query_execution(query, database_name, catalog_name, output_location):
    print("Starting query execution")
    try:
        response = athena_client.start_query_execution(
            QueryString=query,
            QueryExecutionContext={"Database": database_name, "Catalog": catalog_name},
            ResultConfiguration={
                "OutputLocation": output_location,
            },
        )

        return (response.get("QueryExecutionId"), "QUEUED")

    except (NoCredentialsError, ParamValidationError) as e:
        print("Error is")
        print(e)


def get_query_execution_status(query_execution_id):
    print(f"Athena Client - getting execution status for query {query_execution_id}")
    response = athena_client.get_query_execution(QueryExecutionId=query_execution_id)
    return response.get("QueryExecution", {}).get("Status", {}).get("State")


@keyword("Get Athena query results")
def query_athena(query, dbName, catalog_name, output_location):
    try:
        query_execution_id, query_state = start_query_execution(query, dbName, catalog_name, output_location)
        print(f"Athena Client - Query {query_execution_id} in state {query_state}")

        if query_execution_id:
            max_execution = 200
            while max_execution > 0 and query_state in ["RUNNING", "QUEUED"]:
                query_state = get_query_execution_status(query_execution_id)
                print(f"Athena client - query {query_execution_id} in state {query_state}")

                max_execution -= 1
                if query_state == "FAILED":
                    raise DatabaseQueryException(f"Failed execution for query {query_execution_id}")
                elif query_state == "SUCCEEDED":
                    print(f"Query execution successful for query {query_execution_id}")
                    response = get_query_results(query_execution_id)
                    response = response[1:]

                    # Fetch the results
                    result_paginator = athena_client.get_paginator("get_query_results")
                    result_iterator = result_paginator.paginate(QueryExecutionId=query_execution_id)

                    # Extract column names
                    column_info = result_iterator.build_full_result()["ResultSet"]["ResultSetMetadata"]["ColumnInfo"]
                    column_names = [col["Name"] for col in column_info]

                    # Extract rows of data
                    rows = []
                    for result in result_iterator:
                        rows += result["ResultSet"]["Rows"][1:]  # Skip the first row if it contains headers

                    # Replace type key with col names
                    data = []
                    for row in rows:
                        data.append({column_names[i]: col.get("VarCharValue", None) for i, col in enumerate(row["Data"])})

                    return data
                time.sleep(1)
            raise TimeoutError("Timeout trying to get query results")
        else:
            print("Error")
            raise DatabaseQueryException("Error while executing the query")
    except DatabaseQueryException as error:
        print(f"Athena client - Error while querying the data base {dbName}. {error}")
        raise
