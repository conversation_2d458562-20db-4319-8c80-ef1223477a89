# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: HeaderSurfacesSeatsRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+HeaderSurfacesSeatsRawProtobufMessage.proto\x12!jlr.protobuf.heatedsurfaces.seats\x1a\x10\x45numStatus.proto\"\x8b\x02\n\x14SeatClimateOperation\x12I\n\tseat_area\x18\x01 \x01(\x0e\x32\x36.jlr.protobuf.heatedsurfaces.seats.EnumSeatClimateArea\x12K\n\nseat_state\x18\x02 \x01(\x0e\x32\x37.jlr.protobuf.heatedsurfaces.seats.EnumSeatClimateState\x12[\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32;.jlr.protobuf.heatedsurfaces.seats.EnumSeatClimateIntensity\"\x93\x02\n\x15SetSeatClimateRequest\x12L\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x34.jlr.protobuf.heatedsurfaces.seats.EnumSeatSelection\x12O\n\x0eseat_operation\x18\x02 \x03(\x0b\x32\x37.jlr.protobuf.heatedsurfaces.seats.SeatClimateOperation\x12[\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32;.jlr.protobuf.heatedsurfaces.seats.EnumSeatClimateIntensity\"\x97\x01\n\x16SetSeatClimateResponse\x12L\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x34.jlr.protobuf.heatedsurfaces.seats.EnumSeatSelection\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\"k\n\x1bGetSeatClimateStatusRequest\x12L\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x34.jlr.protobuf.heatedsurfaces.seats.EnumSeatSelection\"\xcb\x02\n\x1cGetSeatClimateStatusResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12L\n\x0eseat_selection\x18\x02 \x01(\x0e\x32\x34.jlr.protobuf.heatedsurfaces.seats.EnumSeatSelection\x12O\n\x0eseat_operation\x18\x03 \x03(\x0b\x32\x37.jlr.protobuf.heatedsurfaces.seats.SeatClimateOperation\x12[\n\x16seat_climate_intensity\x18\x04 \x01(\x0e\x32;.jlr.protobuf.heatedsurfaces.seats.EnumSeatClimateIntensity\"\xc6\x02\n\x17NotifySeatClimateStatus\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12L\n\x0eseat_selection\x18\x02 \x01(\x0e\x32\x34.jlr.protobuf.heatedsurfaces.seats.EnumSeatSelection\x12O\n\x0eseat_operation\x18\x03 \x03(\x0b\x32\x37.jlr.protobuf.heatedsurfaces.seats.SeatClimateOperation\x12[\n\x16seat_climate_intensity\x18\x04 \x01(\x0e\x32;.jlr.protobuf.heatedsurfaces.seats.EnumSeatClimateIntensity*\xb8\x04\n\x18\x45numSeatClimateIntensity\x12+\n\'ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_SEAT_CLIMATE_INTENSITY_OFF\x10\x01\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1\x10\x02\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1\x10\x03\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2\x10\x04\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2\x10\x05\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3\x10\x06\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3\x10\x07\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4\x10\x08\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4\x10\t\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5\x10\n\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5\x10\x0b*\xc9\x01\n\x13\x45numSeatClimateArea\x12&\n\"ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED\x10\x00\x12$\n ENUM_SEAT_CLIMATE_AREA_DONT_CARE\x10\x01\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_AREA_ALL\x10\x02\x12\"\n\x1e\x45NUM_SEAT_CLIMATE_AREA_CUSHION\x10\x03\x12 \n\x1c\x45NUM_SEAT_CLIMATE_AREA_SQUAB\x10\x04*\xa5\x01\n\x14\x45numSeatClimateState\x12\'\n#ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_STATE_ON\x10\x01\x12\x1f\n\x1b\x45NUM_SEAT_CLIMATE_STATE_OFF\x10\x02\x12#\n\x1f\x45NUM_SEAT_CLIMATE_STATE_INHIBIT\x10\x03*\xac\x03\n\x11\x45numSeatSelection\x12#\n\x1f\x45NUM_SEAT_SELECTION_UNSPECIFIED\x10\x00\x12&\n\"ENUM_SEAT_SELECTION_FIRST_ROW_LEFT\x10\x01\x12\'\n#ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT\x10\x02\x12(\n$ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE\x10\x03\x12\'\n#ENUM_SEAT_SELECTION_SECOND_ROW_LEFT\x10\x04\x12(\n$ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT\x10\x05\x12)\n%ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE\x10\x06\x12&\n\"ENUM_SEAT_SELECTION_THIRD_ROW_LEFT\x10\x07\x12\'\n#ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT\x10\x08\x12(\n$ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE\x10\tBt\nIcom.jaguarlandrover.commandandcontrolprotobuflibrary.heatedsurfaces.seatsB%HeatedSurfacesSeatsRawProtobufMessageP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'HeaderSurfacesSeatsRawProtobufMessage_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\nIcom.jaguarlandrover.commandandcontrolprotobuflibrary.heatedsurfaces.seatsB%HeatedSurfacesSeatsRawProtobufMessageP\001'
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_start=1575
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_end=2143
  _globals['_ENUMSEATCLIMATEAREA']._serialized_start=2146
  _globals['_ENUMSEATCLIMATEAREA']._serialized_end=2347
  _globals['_ENUMSEATCLIMATESTATE']._serialized_start=2350
  _globals['_ENUMSEATCLIMATESTATE']._serialized_end=2515
  _globals['_ENUMSEATSELECTION']._serialized_start=2518
  _globals['_ENUMSEATSELECTION']._serialized_end=2946
  _globals['_SEATCLIMATEOPERATION']._serialized_start=101
  _globals['_SEATCLIMATEOPERATION']._serialized_end=368
  _globals['_SETSEATCLIMATEREQUEST']._serialized_start=371
  _globals['_SETSEATCLIMATEREQUEST']._serialized_end=646
  _globals['_SETSEATCLIMATERESPONSE']._serialized_start=649
  _globals['_SETSEATCLIMATERESPONSE']._serialized_end=800
  _globals['_GETSEATCLIMATESTATUSREQUEST']._serialized_start=802
  _globals['_GETSEATCLIMATESTATUSREQUEST']._serialized_end=909
  _globals['_GETSEATCLIMATESTATUSRESPONSE']._serialized_start=912
  _globals['_GETSEATCLIMATESTATUSRESPONSE']._serialized_end=1243
  _globals['_NOTIFYSEATCLIMATESTATUS']._serialized_start=1246
  _globals['_NOTIFYSEATCLIMATESTATUS']._serialized_end=1572
# @@protoc_insertion_point(module_scope)
