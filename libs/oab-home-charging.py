from robot.api.deco import keyword
import commonUtils
import json


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
serviceUrl = "oneapp-backend-url-2"
HcUpdatedName = "OAB Home Charger"


# POST to link
@keyword("Link home charger")
def link_charger(user_email, user_password, productKey, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + "/users/me/charging/home/<USER>"
    requestBody = {"product_key": productKey}
    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    print(response[0])
    return response[0], response[1]


# DELETE to unlink
@keyword("Unlink home charger")
def unlink_charger(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = f"{url}/home-chargers/{chargerId}"
    response = commonUtils.delete_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]


# GET charge point status
@keyword("Get status of home charger")
def get_charger_status(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}/status"
    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]


# GET users linked chargers
@keyword("Get users linked chargers")
def get_linked_chargers(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + "/users/me/charging/home/<USER>"
    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]


# GET charger details
@keyword("Get details of home charger")
def get_charger_details(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}"
    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# PATCH to update charger name
@keyword("Update name of home charger")
def update_charger_name(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}"
    requestBody = {"name": HcUpdatedName}
    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    print(response[0])
    return response[0], response[1]


# Method used in test setup to ensure unlinked chargers caught in failing runs are unlinked.
@keyword("Get ID from first of returned chargers")
def get_value_from_first_object(array, key):
    array = json.loads(array)
    if isinstance(array, list) and len(array) > 0 and isinstance(array[0], dict):
        return array[0].get(key)
    else:
        return None


# Get charger default schedule for users account
@keyword("Get charger default schedule")
def get_default_schedule(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}/schedules/default"
    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]


# Create charger default schedule for users account
@keyword("Create charger default schedule")
def create_default_schedule(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}/schedules/default"
    requestBody = {"default_schedule": {"base_fixed_schedule": {"start_time": "20:00:00", "end_time": "21:00:00"}}}
    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    print(response[0])
    return response[0], response[1]


# Update charger default schedule for users account
@keyword("Update charger default schedule")
def update_default_schedule(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}/schedules/default"
    requestBody = {"default_schedule": {"base_fixed_schedule": {"start_time": "22:00:00", "end_time": "23:00:00"}}}
    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    print(response[0])
    return response[0], response[1]


# Delete charger default schedule for users account
@keyword("Delete charger default schedule")
def delete_default_schedule(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f"/users/me/charging/home/<USER>/{chargerId}/schedules/default"
    response = commonUtils.delete_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]


# Get charger tariffs for users account
@keyword('Get charger tariff')
def get_tariff(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f'/users/me/charging/home/<USER>/{chargerId}/tariffs'
    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]


# Create charger tariffs for users account
@keyword('Create charger tariff')
def create_tariff(user_email, user_password, requestBody, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f'/users/me/charging/home/<USER>/{chargerId}/tariffs'
    print("body", requestBody)
    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    print(response[0])
    return response[0], response[1]


# Update charger tariff for users account
@keyword('Update charger tariff')
def update_tariff(user_email, user_password, requestBody, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f'/users/me/charging/home/<USER>/{chargerId}/tariffs'
    print("body", requestBody)
    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    print(response[0])
    return response[0], response[1]


# Delete charger tariff for users account
@keyword('Delete charger tariff')
def delete_tariff(user_email, user_password, chargerId, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)
    endpoint = url + f'/users/me/charging/home/<USER>/{chargerId}/tariffs'
    response = commonUtils.delete_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    print(response[0])
    return response[0], response[1]
