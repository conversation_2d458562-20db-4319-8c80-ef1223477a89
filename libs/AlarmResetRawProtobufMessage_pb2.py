# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AlarmResetRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n"AlarmResetRawProtobufMessage.proto\x12\x17jlr.protobuf.alarmreset\x1a\x10\x45numStatus.proto"^\n\x16SetVehicleAlarmRequest\x12\x44\n\x0f\x61larm_operation\x18\x01 \x01(\x0e\x32+.jlr.protobuf.alarmreset.EnumAlarmOperation"J\n\x17SetVehicleAlarmResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x8a\x01\n\x17NotifyVehicleAlarmState\x12>\n\x0c\x61larm_status\x18\x01 \x01(\x0e\x32(.jlr.protobuf.alarmreset.EnumAlarmStatus\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*Y\n\x12\x45numAlarmOperation\x12$\n ENUM_ALARM_OPERATION_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_ALARM_OPERATION_STOP\x10\x01*\xaf\x01\n\x0f\x45numAlarmStatus\x12!\n\x1d\x45NUM_ALARM_STATUS_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x45NUM_ALARM_STATUS_ARMED\x10\x01\x12\x1e\n\x1a\x45NUM_ALARM_STATUS_DISARMED\x10\x02\x12\x1f\n\x1b\x45NUM_ALARM_STATUS_TRIGGERED\x10\x03\x12\x1b\n\x17\x45NUM_ALARM_STATUS_FAULT\x10\x04\x42\x61\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.alarmresetB\x1c\x41larmResetRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "AlarmResetRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.alarmresetB\034AlarmResetRawProtobufMessageP\001"
    _globals["_ENUMALARMOPERATION"]._serialized_start = 394
    _globals["_ENUMALARMOPERATION"]._serialized_end = 483
    _globals["_ENUMALARMSTATUS"]._serialized_start = 486
    _globals["_ENUMALARMSTATUS"]._serialized_end = 661
    _globals["_SETVEHICLEALARMREQUEST"]._serialized_start = 81
    _globals["_SETVEHICLEALARMREQUEST"]._serialized_end = 175
    _globals["_SETVEHICLEALARMRESPONSE"]._serialized_start = 177
    _globals["_SETVEHICLEALARMRESPONSE"]._serialized_end = 251
    _globals["_NOTIFYVEHICLEALARMSTATE"]._serialized_start = 254
    _globals["_NOTIFYVEHICLEALARMSTATE"]._serialized_end = 392
# @@protoc_insertion_point(module_scope)
