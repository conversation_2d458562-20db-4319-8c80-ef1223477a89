# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: scheduling_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import climate_common_pb2 as climate__common__pb2
import scheduling_common_pb2 as scheduling__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18scheduling_service.proto\x12\x12scheduling_service\x1a\x14\x63limate_common.proto\x1a\x17scheduling_common.proto\"\x98\x02\n\x1c\x44\x65partureTaskPreconditioning\x12,\n\x1fprecondition_target_temperature\x18\x01 \x01(\x02H\x00\x88\x01\x01\x12?\n\x11seat_climate_zone\x18\x02 \x03(\x0b\x32$.climate_common.SeatClimateZoneState\x12K\n\x15hsw_temperature_level\x18\x03 \x01(\x0e\x32\'.climate_common.EnumHSWTemperatureLevelH\x01\x88\x01\x01\x42\"\n _precondition_target_temperatureB\x18\n\x16_hsw_temperature_level\"\x19\n\x17\x44\x65partureTaskCabinClean\"\xa9\x01\n\rDepartureTask\x12K\n\x0fpreconditioning\x18\x01 \x01(\x0b\x32\x30.scheduling_service.DepartureTaskPreconditioningH\x00\x12\x42\n\x0b\x63\x61\x62in_clean\x18\x02 \x01(\x0b\x32+.scheduling_service.DepartureTaskCabinCleanH\x00\x42\x07\n\x05value\"|\n\x0e\x44\x65partureEvent\x12\x1b\n\x0e\x64\x65parture_time\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12:\n\x0f\x64\x65parture_tasks\x18\x02 \x03(\x0b\x32!.scheduling_service.DepartureTaskB\x11\n\x0f_departure_time\"\x81\x02\n\x14WakeUpRequestDetails\x12>\n\x0f\x65vent_time_type\x18\x01 \x01(\x0e\x32%.scheduling_service.EnumEventTimeType\x12\x17\n\nevent_time\x18\x02 \x01(\x04H\x00\x88\x01\x01\x12#\n\x16\x65vent_duration_seconds\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x41\n\rwakeup_reason\x18\x04 \x01(\x0e\x32*.scheduling_common.EnumVehicleWakeUpReasonB\r\n\x0b_event_timeB\x19\n\x17_event_duration_seconds\"\x97\x02\n\x15WakeUpResponseDetails\x12.\n\x06result\x18\x01 \x01(\x0e\x32\x1e.scheduling_service.EnumStatus\x12\x1b\n\x0estart_time_utc\x18\x02 \x01(\x04H\x00\x88\x01\x01\x12!\n\x14start_time_localtime\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x19\n\x0c\x65nd_time_utc\x18\x04 \x01(\x04H\x02\x88\x01\x01\x12\x1f\n\x12\x65nd_time_localtime\x18\x05 \x01(\x04H\x03\x88\x01\x01\x42\x11\n\x0f_start_time_utcB\x17\n\x15_start_time_localtimeB\x0f\n\r_end_time_utcB\x15\n\x13_end_time_localtime\"[\n\x1bSetDepartureScheduleRequest\x12<\n\x10\x64\x65parture_events\x18\x01 \x03(\x0b\x32\".scheduling_service.DepartureEvent\"N\n\x1cSetDepartureScheduleResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.scheduling_service.EnumStatus\"Y\n\x15SetWakeUpTimesRequest\x12@\n\x0ewakeup_details\x18\x01 \x03(\x0b\x32(.scheduling_service.WakeUpRequestDetails\"\x90\x01\n\x16SetWakeUpTimesResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.scheduling_service.EnumStatus\x12\x46\n\x13wakeup_resp_details\x18\x02 \x03(\x0b\x32).scheduling_service.WakeUpResponseDetails\"!\n\x1f\x43learChargingWakeUpTimesRequest\"R\n ClearChargingWakeUpTimesResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.scheduling_service.EnumStatus\"\x1e\n\x1cGetNextDepartureEventRequest\"\x8c\x01\n\x1dGetNextDepartureEventResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.scheduling_service.EnumStatus\x12;\n\x0f\x64\x65parture_event\x18\x02 \x01(\x0b\x32\".scheduling_service.DepartureEvent*\x98\x03\n\nEnumStatus\x12\x1b\n\x17\x45NUM_STATUS_UNSPECIFIED\x10\x00\x12\x12\n\x0e\x45NUM_STATUS_OK\x10\x01\x12\x1d\n\x19\x45NUM_STATUS_DATA_DEGRADED\x10\x02\x12\x1f\n\x1b\x45NUM_STATUS_DATA_UNRELIABLE\x10\x03\x12 \n\x1c\x45NUM_STATUS_DATA_UNAVAILABLE\x10\x04\x12+\n\'ENUM_STATUS_ERROR_INVALID_SERVICE_STATE\x10\x05\x12+\n\'ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE\x10\x06\x12/\n+ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION\x10\x07\x12)\n%ENUM_STATUS_ERROR_MISSING_INPUT_FIELD\x10\x08\x12)\n%ENUM_STATUS_ERROR_INVALID_INPUT_FIELD\x10\t\x12\x16\n\x12\x45NUM_STATUS_NOT_OK\x10\n*w\n\x11\x45numEventTimeType\x12$\n ENUM_EVENT_TIME_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_EVENT_TIME_TYPE_BEGIN\x10\x01\x12\x1c\n\x18\x45NUM_EVENT_TIME_TYPE_END\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'scheduling_service_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ENUMSTATUS']._serialized_start=1951
  _globals['_ENUMSTATUS']._serialized_end=2359
  _globals['_ENUMEVENTTIMETYPE']._serialized_start=2361
  _globals['_ENUMEVENTTIMETYPE']._serialized_end=2480
  _globals['_DEPARTURETASKPRECONDITIONING']._serialized_start=96
  _globals['_DEPARTURETASKPRECONDITIONING']._serialized_end=376
  _globals['_DEPARTURETASKCABINCLEAN']._serialized_start=378
  _globals['_DEPARTURETASKCABINCLEAN']._serialized_end=403
  _globals['_DEPARTURETASK']._serialized_start=406
  _globals['_DEPARTURETASK']._serialized_end=575
  _globals['_DEPARTUREEVENT']._serialized_start=577
  _globals['_DEPARTUREEVENT']._serialized_end=701
  _globals['_WAKEUPREQUESTDETAILS']._serialized_start=704
  _globals['_WAKEUPREQUESTDETAILS']._serialized_end=961
  _globals['_WAKEUPRESPONSEDETAILS']._serialized_start=964
  _globals['_WAKEUPRESPONSEDETAILS']._serialized_end=1243
  _globals['_SETDEPARTURESCHEDULEREQUEST']._serialized_start=1245
  _globals['_SETDEPARTURESCHEDULEREQUEST']._serialized_end=1336
  _globals['_SETDEPARTURESCHEDULERESPONSE']._serialized_start=1338
  _globals['_SETDEPARTURESCHEDULERESPONSE']._serialized_end=1416
  _globals['_SETWAKEUPTIMESREQUEST']._serialized_start=1418
  _globals['_SETWAKEUPTIMESREQUEST']._serialized_end=1507
  _globals['_SETWAKEUPTIMESRESPONSE']._serialized_start=1510
  _globals['_SETWAKEUPTIMESRESPONSE']._serialized_end=1654
  _globals['_CLEARCHARGINGWAKEUPTIMESREQUEST']._serialized_start=1656
  _globals['_CLEARCHARGINGWAKEUPTIMESREQUEST']._serialized_end=1689
  _globals['_CLEARCHARGINGWAKEUPTIMESRESPONSE']._serialized_start=1691
  _globals['_CLEARCHARGINGWAKEUPTIMESRESPONSE']._serialized_end=1773
  _globals['_GETNEXTDEPARTUREEVENTREQUEST']._serialized_start=1775
  _globals['_GETNEXTDEPARTUREEVENTREQUEST']._serialized_end=1805
  _globals['_GETNEXTDEPARTUREEVENTRESPONSE']._serialized_start=1808
  _globals['_GETNEXTDEPARTUREEVENTRESPONSE']._serialized_end=1948
# @@protoc_insertion_point(module_scope)
