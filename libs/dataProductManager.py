from pyaml_env import parse_config, BaseConfig
import requests
import json
from robot.api.deco import keyword, not_keyword
import random

from commonUtils import get_keycloak_token

DATAPRODUCT_DELTA_URI = "/data-product/delta"
GET_DATAPRODUCT_URI = "/data-product/"
DATAPRODUCT_REQUESTS_URI = "/data-product-requests/"
STATUS_URI = "/status"
SIGNAL_ID = "|S00154|_"
updatedSignal = "|S00670|_"
EMPTY_BODY = "No response body returned"
systemVarFile = "../variables/system.yml"
realDplist = "../jsonschema/list-of-real-data-products.json"
softDelete = "?softDelete=true"
hardDelete = "?softDelete=false"

dataProductID = "Dp_" + str(random.randint(100, 999))
query1ID = str(random.randint(100, 999))
contentType = "application/json"
authType = "Bearer "
headers = {"accept": "*/*", "Content-Type": contentType}

dataProductRequestName = "TEST_" + str(random.randint(100, 999))


@not_keyword
def data_product_handler_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "data-product-handler-url")


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "data-product-manager-url")


@keyword("Get all data products")
def get_data_product_deltas(user, pw):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    url = get_url()
    getAllDataProducts = url + DATAPRODUCT_DELTA_URI
    print(getAllDataProducts)

    response = requests.request("GET", getAllDataProducts, headers=header)
    return response.text, response.status_code, dataProductID


@keyword("Get data product by ID")
def get_data_product_by_id(user, pw, invalidId=False):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    if invalidId is False:
        product_id = str(dataProductID)
    elif invalidId is True:
        product_id = "Dp_Xxx"
    else:
        product_id = invalidId

    payload = [product_id]

    url = get_url()
    getDeltasById = url + GET_DATAPRODUCT_URI + product_id
    print(getDeltasById)
    response = requests.request("GET", getDeltasById, headers=header, json=payload)
    return response.text, response.status_code


@keyword("Send data product")
def send_data_product(data, user, pw):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    print(dataProductID)
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["dp_id"] = dataProductID
    jsonRequestBody["elements"][0]["dp_id"] = dataProductID
    jsonRequestBody["delta"]["dp_id"] = dataProductID
    jsonRequestBody["elements"][0]["query_id"] = "Q" + query1ID
    jsonRequestBody["delta"]["queries"][0]["id"] = "Q" + query1ID
    jsonRequestBody["elements"][0]["key_code"] = "Q9" + dataProductID + query1ID + SIGNAL_ID

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    url = get_url()
    post = url + "/data-product/upload"

    response = requests.request("POST", post, headers=header, data=payload)
    return response.text, response.status_code


@keyword("Update data product")
def update_data_product(data, user, pw, iteration):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["dp_id"] = dataProductID
    jsonRequestBody["dp_iteration"] = iteration
    jsonRequestBody["elements"][0]["dp_id"] = dataProductID
    jsonRequestBody["elements"][0]["dp_iteration"] = iteration
    jsonRequestBody["delta"]["dp_id"] = "Dp_" + dataProductID
    jsonRequestBody["elements"][0]["query_id"] = "Q" + query1ID
    jsonRequestBody["delta"]["queries"][0]["id"] = "Q" + query1ID
    jsonRequestBody["elements"][0]["key_code"] = "Q9" + dataProductID + query1ID + updatedSignal

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    url = get_url()
    put = url + "/data-product/update"
    print(payload)
    response = requests.request("PUT", put, headers=header, data=payload)
    return response.text, response.status_code


@keyword("Delete data product")
def delete_data_product(user, pw, dpId=""):
    if dpId == "":
        lDpId = str(dataProductID)
    else:
        lDpId = dpId

    realdpFile = realdp_file()
    print(lDpId)
    response = check_real_dataProduct(user, pw, lDpId, hardDelete, realdpFile)
    return response


@keyword("Create a data product request")
def create_data_product_request(data, user, pw):
    token = get_keycloak_token(user, pw)
    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["justification"]["name"] = dataProductRequestName
    jsonRequestBody.update(jsonRequestBody)
    payload = json.dumps(jsonRequestBody)

    url = data_product_handler_url()
    DataProductRequest = url + "/data-product-requests"
    response = requests.request("POST", DataProductRequest, headers=header, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return EMPTY_BODY, response.status_code


@keyword("Update status of data product request")
def update_data_product_request_status(data, user, pw, status, dataProductRequestId):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    if status == "APPROVED":
        jsonRequestBody["status"] = "APPROVED"
        jsonRequestBody["note"] = "Approved For Testing"
    elif status == "AMEND_REQUIRED":
        jsonRequestBody["status"] = "AMEND_REQUIRED"
        jsonRequestBody["note"] = "Amend For Testing"
    elif status == "PENDING":
        jsonRequestBody["status"] = "PENDING"
        jsonRequestBody["note"] = "In Pending For Testing"
    elif status == "DECLINED":
        jsonRequestBody["status"] = "DECLINED"
        jsonRequestBody["note"] = "Declined For Testing"

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)
    url = data_product_handler_url()
    dataProductRequestIdstr = "".join(dataProductRequestId)
    put = url + DATAPRODUCT_REQUESTS_URI + dataProductRequestIdstr + STATUS_URI
    print(put)
    response = requests.request("PUT", put, headers=header, data=payload)
    return response.text, response.status_code


@keyword("Submit a data product request")
def submit_data_product_request(user, pw, dataProductRequestId):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    url = data_product_handler_url()
    dataProductRequestIdstr = "".join(dataProductRequestId)
    submitDataProductRequest = url + DATAPRODUCT_REQUESTS_URI + dataProductRequestIdstr + "/submit"

    response = requests.request("POST", submitDataProductRequest, headers=header)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return EMPTY_BODY, response.status_code


@keyword("Get data product created by ID generated")
def get_data_product_by_request_id(user, pw, DpId):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }
    DpIdstr = str(DpId)
    payload = [DpIdstr]
    url = get_url()
    getDeltasById = url + DATAPRODUCT_DELTA_URI

    response = requests.request("POST", getDeltasById, headers=header, json=payload)
    return response.text, response.status_code


@keyword("Without note Update status of data product request")
def update_data_product_request_status_without(data, user, pw, status, dataProductRequestId):
    token = get_keycloak_token(user, pw)

    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    if status == "APPROVED":
        jsonRequestBody["status"] = "APPROVED"
        jsonRequestBody["note"] = None
    elif status == "AMEND_REQUIRED":
        jsonRequestBody["status"] = "AMEND_REQUIRED"
        jsonRequestBody["note"] = None
    elif status == "PENDING":
        jsonRequestBody["status"] = "PENDING"
        jsonRequestBody["note"] = None
    elif status == "DECLINED":
        jsonRequestBody["status"] = "DECLINED"
        jsonRequestBody["note"] = None

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)
    url = data_product_handler_url()
    dataProductRequestIdstr = "".join(dataProductRequestId)
    put = url + DATAPRODUCT_REQUESTS_URI + dataProductRequestIdstr + STATUS_URI
    print(put)
    response = requests.request("PUT", put, headers=header, data=payload)
    return response.text, response.status_code


@keyword("Adding Generic Note")
def add_generic_note(data, user, pw, note, dataProductRequestId):
    token = get_keycloak_token(user, pw)
    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    if note == "NOTEMPTY":
        jsonRequestBody["value"] = "Generic note for testing"
    elif note == "EMPTY":
        jsonRequestBody["value"] = ""

    jsonRequestBody.update(jsonRequestBody)
    payload = json.dumps(jsonRequestBody)

    url = data_product_handler_url()
    dataProductRequestIdstr = "".join(dataProductRequestId)
    post = url + DATAPRODUCT_REQUESTS_URI + dataProductRequestIdstr + "/notes"
    response = requests.request("POST", post, headers=header, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return EMPTY_BODY, response.status_code


@keyword("change status of data product")
def dp_status(data, user, pw, dpId):
    token = get_keycloak_token(user, pw)

    headers = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot", "Authorization": authType + token}

    url = get_url()

    dpIDString = str(dpId)
    print(type(dpIDString))
    print(dpIDString)

    dpIDStriped = dpIDString.strip("['']")

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    payload = json.dumps(jsonRequestBody)
    dpStatus = url + "/data-products/" + dpIDStriped + STATUS_URI

    print(dpStatus)
    response = requests.request("PUT", dpStatus, headers=headers, data=payload)

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return EMPTY_BODY, response.status_code


@keyword("Create data product with eligibility criteria")
def create_dp_with_criteria(data, user, pw, criteria):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": contentType, "Authorization": authType + token}

    global dataProductID
    print("Data Product ID:", dataProductID)
    realdpFile = realdp_file()
    while dataProductID in realdpFile:
        print(dataProductID, "Is a real dataProduct and it can't be created")
        print("List of real data procucts:", realdpFile)
        dataProductID = "Dp_" + str(random.randint(100, 999))
    print("New Data Product ID Generated:", dataProductID)
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["dp_id"] = dataProductID
    jsonRequestBody["elements"][0]["dp_id"] = dataProductID
    jsonRequestBody["delta"]["dp_id"] = dataProductID
    jsonRequestBody["elements"][0]["query_id"] = "Q" + query1ID
    jsonRequestBody["delta"]["queries"][0]["id"] = "Q" + query1ID
    jsonRequestBody["elements"][0]["key_code"] = "Q9" + dataProductID + query1ID + SIGNAL_ID

    if criteria == "SINGLERR":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER"],
                "modelRange": ["L460"],
                "modelYear": ["2023"],
                "fuelType": ["DIESEL"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "SINGLEJAG":
        eligibilityCriteria = {
            "assets": {
                "brand": ["JAGUAR"],
                "modelRange": ["L4601"],
                "modelYear": ["2025"],
                "fuelType": ["PETROL"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "NOBRAND":
        eligibilityCriteria = {
            "assets": {
                "brand": None,
                "modelRange": ["L460"],
                "modelYear": ["2023"],
                "fuelType": ["DIESEL"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "NORANGE":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER"],
                "modelRange": None,
                "modelYear": ["2023"],
                "fuelType": ["DIESEL"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "NOYEAR":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER"],
                "modelRange": ["L460"],
                "modelYear": None,
                "fuelType": ["DIESEL"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "NOFUELTYPE":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER"],
                "modelRange": ["L460"],
                "modelYear": ["2023"],
                "fuelType": None,
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "MULTI":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER", "JAGUAR"],
                "modelRange": ["L460", "L4601"],
                "modelYear": ["2023", "2025"],
                "fuelType": ["DIESEL", "PETROL"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "FLEETMLAP":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER"],
                "modelRange": ["L460"],
                "modelYear": ["2023"],
                "fuelType": ["DIESEL"],
                "fleetId": ["MLAP"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }
    elif criteria == "DIFFLEET":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER"],
                "modelRange": ["L460"],
                "modelYear": ["2023"],
                "fuelType": ["DIESEL"],
                "fleetId": ["TESTFLEET"],
                "vehicleArchitecture": ["EVA_2"],
            },
            "tags": {"tags": None},
        }

    jsonRequestBody["delta"]["eligibilityCriteria"] = eligibilityCriteria
    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)
    print(payload)
    url = get_url()
    post = url + "/data-product/upload"

    response = requests.request("POST", post, headers=header, data=payload)
    return response.text, response.status_code, dataProductID


@keyword("Delete data product teardown")
def delete_dp(user, pw):
    realdpFile = realdp_file()

    print(dataProductID)

    response = check_real_dataProduct(user, pw, dataProductID, hardDelete, realdpFile)
    return response


@keyword("Soft Delete data product")
def soft_delete_dp(user, pw, dpId=""):
    if dpId == "":
        lDpId = str(dataProductID)
    else:
        lDpId = dpId

    realdpFile = realdp_file()

    print(lDpId)

    response = check_real_dataProduct(user, pw, lDpId, softDelete, realdpFile)
    return response


def realdp_file():
    open_json_file = open(realDplist, "r")
    read_json_data = open_json_file.read()
    actual_file = json.loads(read_json_data)
    str_json = str(actual_file)
    return str_json


def check_real_dataProduct(user, pw, dataProduct, deleteURI, str_json):
    token = get_keycloak_token(user, pw)
    header = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Requester": "Robot",
        "Authorization": authType + token,
    }
    if dataProduct in str_json:
        print(dataProduct, "Is a real dataProduct and it should not be deleted")
        print(str_json)
        responseText = dataProduct + "Is a real dataProduct and it should not be deleted"
        responseCode = 200
        return [responseText, responseCode]
    else:
        url = get_url()
        delete = url + "/data-product/" + dataProduct + deleteURI
        print(delete)
        response = requests.request("DELETE", delete, headers=header)
        return response.text, response.status_code
