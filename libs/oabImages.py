import commonUtils
from robot.api.deco import keyword
import requests

systemVarFile = "../variables/system.yml"
CONTENT_TYPE = "application/json"
serviceUrl = "oneapp-backend-url"


# GET vehicle image by order id
@keyword("Get vehicle image by order id")
def get_image_by_order(orderId, viewAngle, tenantHeader=False, tenantId=None):
    url = commonUtils.get_url(serviceUrl)

    if tenantHeader:
        headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "x-tenant-id": tenantId}
    else:
        headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE}
    print(f"Header:{headers}")
    endpoint = url + orderId

    queryParams = {"viewAngle": viewAngle}

    response = requests.request("GET", endpoint, headers=headers, params=queryParams)
    print(response)
    return response.text, response.status_code


# GET vehicle image generic
@keyword("Get vehicle image generic")
def get_image_generic(model, colour, viewAngle, tenantHeader=False, tenantId=None):
    url = commonUtils.get_url(serviceUrl)

    if tenantHeader:
        headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "x-tenant-id": tenantId}
    else:
        headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE}

    endpoint = url + "generic"

    queryParams = {"model": model, "colour": colour, "viewAngle": viewAngle}

    response = requests.request("GET", endpoint, headers=headers, params=queryParams)
    print(response)
    return response.text, response.status_code
