#!/usr/bin python3
"""
this script contains functions to aid in
the testing of the scheduling service
"""
import base64
import hashlib
import json
import os
import subprocess
import uuid
from pyaml_env import parse_config, BaseConfig
import requests
from robot.api.deco import keyword, not_keyword
from kafka import KafkaProducer
from kafka.errors import KafkaError
from kafkaConsumerLib import MSKTokenProvider
import digitalVehicleApiCaching

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"


# choose environment to run test in
SYSTEM_VAR_FILE_DEV = "../variables/system-dev.yml"
SYSTEM_VAR_FILE_PRE_PROD = "../variables/system-pre-prod.yml"


@not_keyword
def get_var_from_file(var_file, var_name):
    """
    get a variable from a yml file.
    Example:
    | get_var_from_file
    | path to system.yml file
    | name of variable as defined in system.yml file
    """
    sys_vars = BaseConfig(parse_config(var_file))
    return getattr(sys_vars, var_name)


@keyword("load json file in digital vehicle system project")
def load_json_file(json_file_location):
    """
    This function loads a json file and returns a
    json data object.
    Examples:
    | load_json_file | ...location/file.json |
    """
    with open(json_file_location, encoding="utf-8") as file:
        json_object = json.load(file)
        print("\n", "json file from: ", json_file_location, "  \n")
    return json_object


def connect_kafka_sota_producer(timeout_s, environment="dev"):
    SYSTEM_VAR_FILE_DV_SYS = SYSTEM_VAR_FILE_DEV if environment.lower() == "dev" else SYSTEM_VAR_FILE_PRE_PROD

    tp = MSKTokenProvider()
    kafka_bootstrap_servers = get_var_from_file(SYSTEM_VAR_FILE_DV_SYS, "kafka-bootstrap-servers")
    kafka_bootstrap_servers = kafka_bootstrap_servers.replace("9094", "9098")
    print(kafka_bootstrap_servers)
    timeout_ms = int(timeout_s) * 1000
    sota_clientid = "e2e-automated-test-" + (str(uuid.uuid4().fields[-1])[:8])
    try:
        producer = KafkaProducer(
            client_id=sota_clientid,
            bootstrap_servers=kafka_bootstrap_servers,
            security_protocol="SASL_SSL",
            sasl_mechanism="OAUTHBEARER",
            sasl_oauth_token_provider=tp,
            request_timeout_ms=timeout_ms,
        )

    except KafkaError as kafka_error:
        print("kafka.errors." + str(kafka_error) + ":: " + str(kafka_error))
        return -1

    except Exception as ex:
        print("kafka FAILED")
        print(str(ex))
        return -1

    print("Connected to Kafka")
    return producer


@keyword("send sota dt even to kafka resync topic")
def send_sota_dt_event(
    topic,
    vin,
    event_message,
    timeout_s,
    environment="dev",
):
    """
    used to send a SOTA event to kafka.
    Example:
    | send_sota_dt_event
    | name of kafka topic
    | kafka message or event
    | timeout in seconds
    | environment counld be "dev" or "pre-prod"
    """

    sota_producer = connect_kafka_sota_producer(timeout_s, environment=environment)
    sota_payload = json.dumps(event_message)
    print(sota_payload, type(sota_payload))
    sota_producer.send(topic, key=str.encode(vin), value=sota_payload.encode("utf-8"))
    sota_producer.flush()


@keyword("send new vkms event for a vehicle")
def send_new_vkms_event_for_a_vehicle(kc_username, kc_password, vin, vehile_data_file, tcu_serial_num="", environment="dev", auth=True):
    # choose which variable file to choose based on environment
    SYSTEM_VAR_FILE_DV_SYS = SYSTEM_VAR_FILE_PRE_PROD if environment.lower() == "pre-prod" else SYSTEM_VAR_FILE_DEV

    if auth is not True:
        token = "abcdef12345"
    else:
        token = digitalVehicleApiCaching.get_dv_keycloak_token_for_vlc(kc_username, kc_password, environment=environment)
    print("token")
    print(token)

    url = get_var_from_file(SYSTEM_VAR_FILE_DV_SYS, "vkms_new_event")
    print(url)

    digest = hashlib.sha256(vin.encode("utf-8")).digest()
    b64_encoded = base64.b64encode(digest).decode("utf-8")
    hvin = b64_encoded.replace("+", "-").replace("/", "_").rstrip("=")

    headers = {"Content-Type": "application/json", "Authorization": "Bearer " + token}

    new_vehicle = load_json_file(vehile_data_file)
    new_vehicle["eventPayload"]["metadata"]["vehicleInfo"]["vin"] = vin
    new_vehicle["eventPayload"]["metadata"]["vehicleInfo"]["hvin"] = hvin
    if not tcu_serial_num:
        # This assumes the first ecu in the list of ecus is either TCU or TCUA_A
        list_of_ecus = new_vehicle["eventPayload"]["metadata"]["ecus"]
        for index, ecu in enumerate(list_of_ecus):
            if ecu["name"] == "TCU" or ecu["name"] == "TCUA_A":
                print(new_vehicle["eventPayload"]["metadata"]["ecus"][index]["serNo"])
                new_vehicle["eventPayload"]["metadata"]["ecus"][index]["serNo"] = tcu_serial_num
                new_vehicle["eventPayload"]["metadata"]["ecus"][index]["serNo"]

    response = requests.request("POST", url, headers=headers, data=json.dumps(new_vehicle))

    print("Status: ", response.status_code)
    print("response:", response.text)
    # print(response.text)
    return response.status_code


@keyword("get global cert for database access")
def get_global_cert_for_database(url, command="wget", destination_directory="../../"):
    # get url and command
    endpoint = url
    command = [command, "-P", destination_directory, endpoint]
    # Run the command
    result = subprocess.run(command, capture_output=True, text=True)

    # Check the result
    if result.returncode == 0:
        print("Download successful")
        print(f"Download successful, file saved to {destination_directory}")
    else:
        print("Error:", result.stderr)


@keyword("delete global cert for database access after use")
def delete_global_cert_after_use(cert_path):
    # Check if the file exists
    if os.path.exists(cert_path):
        # Delete the file
        os.remove(cert_path)
        print(f"{cert_path} has been deleted")
    else:
        print(f"{cert_path} does not exist")


@keyword("get next available dv test vin")
def get_next_available_test_vin(db_username, db_password, db_host, tls_cert, collections_name, database_name, vehicle_arch, vkmsEnabled, sotaEnabled):
    col, client = digitalVehicleApiCaching.connect_to_dv_database_collection(
        db_username, db_password, db_host, tls_cert, collections_name, database_name
    )
    # #Find the document that was previously written
    document = col.find_one({})
    test_vins = document["data"]["testVins"]
    for vehicle in test_vins:
        print(vehicle)
        if (
            (vehicle["available"] is True)
            and (vehicle["architecture"] == vehicle_arch.upper())
            and (vehicle["vkmsEnabled"] is vkmsEnabled)
            and (vehicle["sotaEnabled"] is sotaEnabled)
        ):
            col.update_one(
                {"data.testVins.vin": vehicle["vin"]},
                {"$set": {"data.testVins.$.available": False}},
            )
            client.close()
            return vehicle["vin"]
    client.close()
    vehicle["vin"] = ""
    return vehicle["vin"]


@keyword("make test vin available again for use")
def reset_test_vin_to_avaible(db_username, db_password, db_host, tls_cert, vin, collections_name, database_name):
    col, client = digitalVehicleApiCaching.connect_to_dv_database_collection(
        db_username, db_password, db_host, tls_cert, collections_name, database_name
    )
    # #Find the document that was previously written
    result = col.update_one({"data.testVins.vin": vin}, {"$set": {"data.testVins.$.available": True}})
    client.close()
    return result.modified_count


def add_new_vehicle_to_list_of_digital_system_test_vins(db_username, db_password, db_host, tls_cert, vehicle, collections_name, database_name):
    col, client = digitalVehicleApiCaching.connect_to_dv_database_collection(
        db_username, db_password, db_host, tls_cert, collections_name, database_name
    )

    result = col.update_one({}, {"$push": {"data.testVins": vehicle}})  # Add a filter here if needed to target a specific document

    # Check if the update was successful
    if result.modified_count > 0:
        print("New VIN entry added successfully.")
    else:
        print("No document updated.")
    client.close()


def delete_vehicle_from_list_of_digital_system_test_vins(db_username, db_password, db_host, tls_cert, vin_to_delete, collections_name, database_name):
    col, client = digitalVehicleApiCaching.connect_to_dv_database_collection(
        db_username, db_password, db_host, tls_cert, collections_name, database_name
    )
    result = col.update_one({}, {"$pull": {"data.testVins": {"vin": vin_to_delete}}})

    # Check if the deletion was successful
    if result.modified_count > 0:
        print(f"VIN {vin_to_delete} deleted successfully.")
    else:
        print("No matching VIN found or no update made.")
    client.close()
    return result.modified_count
