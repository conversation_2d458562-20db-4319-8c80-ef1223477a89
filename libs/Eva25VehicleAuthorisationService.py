#!/usr/bin/env python3

"""
EVA25 Vehicle Authentication and MQTT Credential Retrieval Library
This module provides functionality to authenticate EVA25 vehicles using
ForgeRock and retrieve MQTT credentials required to connect to the Hive platform.

Main Function:
--------------
- get_eva25_token_passphrase_mqtt_credentials(vehicle_vin: str, called_as_binary: bool = False)
    This is the primary function that orchestrates the following:
    1. Generates a signed JWT using vehicle-specific credentials.
    2. Requests an OAuth2 access token from ForgeRock using the JWT.
    3. Uses the access token to retrieve MQTT credentials (passphrase, client ID, and certificates).
    4. Writes the retrieved MQTT client certificate and key to specified file paths.

Supporting Functions:
---------------------
- get_hvin(vin): Generates a hashed VIN (HVIN) using SHA-256 and Base64 URL-safe encoding.
- update_claim(claim, client_id, audience): Updates JWT claims with standard fields.
- get_signed_jwt(claim, key, alg): Signs a JWT using the specified algorithm.
- get_eva25_forgerock_token(...): Retrieves an access token from ForgeRock using the client assertion.
- get_vehicle_mqtt_credentials(...): Retrieves MQTT credentials using the access token.

Note:
-----
Ensure that the system configuration file and all referenced keys, certificates, and claim templates
are correctly set up before invoking the main function.
"""


import os
import time
import uuid
import json
import base64
import hashlib
import requests
from datetime import datetime as dt
from json import JSONDecodeError
from pyaml_env import BaseConfig, parse_config
from authlib.jose import jwt

X5C = ""
X5T = ""
ISS_PREFIX = "JLROBG_VCMID-"


def get_hvin(vin: str) -> str:
    """Generate a hashed VIN (HVIN)"""
    sha256_hash = hashlib.sha256(vin.encode()).digest()
    b64_encoded = base64.b64encode(sha256_hash).decode()
    return b64_encoded.replace("+", "-").replace("/", "_").rstrip("=")


def setup_globals(called_as_binary: bool):
    """Set up global paths based on execution context"""
    global systemFile
    base_path = os.path.dirname(__file__) if called_as_binary else ".."
    systemFile = os.path.abspath(os.path.join(base_path, "variables/system.yml"))


def read_json_file(path: str) -> dict:
    with open(path, "r") as f:
        return json.load(f)


def update_claim(claim: dict, client_id: str, audience: str) -> dict:
    now = int(dt.now().timestamp())
    claim.update({"iat": now, "nbf": now, "exp": now + 600, "iss": client_id, "sub": client_id, "aud": audience, "jti": str(uuid.uuid4())})
    return claim


def get_signed_jwt(claim: dict, key: dict, alg: str) -> str:
    return jwt.encode({"alg": alg}, claim, key)


def get_eva25_signed_jwt(claim: dict, key: dict) -> str:
    headers = {"typ": "JWT", "x5t#s256": X5T, "x5c": [X5C], "alg": "PS256"}
    return jwt.encode(payload=claim, key=key, header=headers)


def get_timestamp() -> str:
    return str(int(time.time() * 1000))


def get_eva25_client_assertion(vehicle_vin: str, called_as_binary: bool = True) -> str:
    setup_globals(called_as_binary)
    system_vars = BaseConfig(parse_config(systemFile))
    vehicle_key = read_json_file(str(system_vars.vehicle_private_keyfile))
    jwt_claims = read_json_file(str(system_vars.jwt_claims))
    audience = str(system_vars.eva25_aud_url)
    updated_claims = update_claim(jwt_claims, ISS_PREFIX + get_hvin(vehicle_vin), audience)
    return get_eva25_signed_jwt(updated_claims, vehicle_key)


def get_eva25_forgerock_token(
    api_endpoint: str, vehicle_vin: str, header_path: str, body_path: str, certfile: str = "", keyfile: str = "", called_as_binary: bool = False
) -> str:
    headers = read_json_file(header_path)
    payload = read_json_file(body_path)
    payload["client_assertion"] = get_eva25_client_assertion(vehicle_vin, called_as_binary)

    response = requests.post(url=api_endpoint, headers=headers, data=payload, cert=(certfile, keyfile))
    print(response.text)

    try:
        return json.loads(response.content)["access_token"]
    except (KeyError, JSONDecodeError) as error:
        print(f"Token retrieval error: {error}")
        return ""


def get_vehicle_mqtt_credentials(api_endpoint: str, access_token: str, certfile: str, keyfile: str):
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url=api_endpoint, headers=headers, cert=(certfile, keyfile))
    print(response.text)

    data = json.loads(response.content)
    return (data["passphrase"], data["id"], base64.b64decode(data["mqtt_client_cert"]), base64.b64decode(data["mqtt_client_key"]))


def get_eva25_token_passphrase_mqtt_credentials(vehicle_vin: str, called_as_binary: bool = False):
    """
    Retrieve token, passphrase, and MQTT credentials for EVA25 vehicle
    """
    setup_globals(called_as_binary)
    system_vars = BaseConfig(parse_config(systemFile))

    certfile = str(system_vars.vehicle_certfile)
    keyfile = str(system_vars.vehicle_keyfile)
    header_path = str(system_vars.eva25_token_request_header)
    body_path = str(system_vars.eva25_token_request_body)
    token_url = str(system_vars.eva25_forgerock_token_url)
    mqtt_url = str(system_vars.eva25_mqtt_credentialse_url)
    cert_out = str(system_vars.eva25_hive_cert_file_path)
    key_out = str(system_vars.eva25_hive_key_file_path)

    token = get_eva25_forgerock_token(token_url, vehicle_vin, header_path, body_path, certfile, keyfile)
    print(token)

    passphrase, client_id, cert_bytes, key_bytes = get_vehicle_mqtt_credentials(mqtt_url, token, certfile, keyfile)

    with open(cert_out, "wb") as cert_file:
        cert_file.write(cert_bytes)

    with open(key_out, "wb") as key_file:
        key_file.write(key_bytes)

    return token, passphrase, client_id
