# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: EVChargingProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)
import EnumStatus_pb2 as EnumStatus__pb2

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1f\x45VChargingProtobufMessage.proto\x12\x17jlr.protobuf.evcharging\x1a\x10\x45numStatus.proto"n\n\x17SetChargeControlRequest\x12S\n\x16\x63harge_control_request\x18\x01 \x01(\x0e\x32\x33.jlr.protobuf.evcharging.EnumChargeControlOperation"K\n\x18SetChargeControlResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x17\n\x15GetChargeStateRequest"\x8c\x01\n\x16GetChargeStateResponse\x12\x41\n\x0f\x63harging_status\x18\x01 \x01(\x0e\x32(.jlr.protobuf.evcharging.EnumChargeState\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x91\x01\n\x18SetChargeSettingsRequest\x12<\n\x0b\x63harge_type\x18\x01 \x01(\x0e\x32\'.jlr.protobuf.evcharging.EnumChargeType\x12\x1b\n\x13off_peak_start_time\x18\x02 \x01(\r\x12\x1a\n\x12off_peak_stop_time\x18\x03 \x01(\r"L\n\x19SetChargeSettingsResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x1a\n\x18GetChargeSettingsRequest"\xc3\x01\n\x19GetChargeSettingsResponse\x12<\n\x0b\x63harge_type\x18\x01 \x01(\x0e\x32\'.jlr.protobuf.evcharging.EnumChargeType\x12\x1b\n\x13off_peak_start_time\x18\x02 \x01(\r\x12\x1a\n\x12off_peak_stop_time\x18\x03 \x01(\r\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x87\x01\n\x11NotifyChargeState\x12\x41\n\x0f\x63harging_status\x18\x01 \x01(\x0e\x32(.jlr.protobuf.evcharging.EnumChargeState\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\xbe\x01\n\x14NotifyChargeSettings\x12<\n\x0b\x63harge_type\x18\x01 \x01(\x0e\x32\'.jlr.protobuf.evcharging.EnumChargeType\x12\x1b\n\x13off_peak_start_time\x18\x02 \x01(\r\x12\x1a\n\x12off_peak_stop_time\x18\x03 \x01(\r\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\x9c\x01\n\x1a\x45numChargeControlOperation\x12-\n)ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED\x10\x00\x12\'\n#ENUM_CHARGE_CONTROL_OPERATION_START\x10\x01\x12&\n"ENUM_CHARGE_CONTROL_OPERATION_STOP\x10\x02*\xf3\x01\n\x0f\x45numChargeState\x12!\n\x1d\x45NUM_CHARGE_STATE_UNSPECIFIED\x10\x00\x12!\n\x1d\x45NUM_CHARGE_STATE_DISCHARGING\x10\x01\x12\'\n#ENUM_CHARGE_STATE_WAITING_TO_CHARGE\x10\x02\x12(\n$ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS\x10\x03\x12#\n\x1f\x45NUM_CHARGE_STATE_FULLY_CHARGED\x10\x04\x12"\n\x1e\x45NUM_CHARGE_STATE_CHARGE_ERROR\x10\x05*\xed\x01\n\x0e\x45numChargeType\x12 \n\x1c\x45NUM_CHARGE_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_CHARGE_TYPE_IMMEDIATE\x10\x01\x12#\n\x1f\x45NUM_CHARGE_TYPE_FIXED_SCHEDULE\x10\x02\x12"\n\x1e\x45NUM_CHARGE_TYPE_SCHEDULE_PLUS\x10\x03\x12#\n\x1f\x45NUM_CHARGE_TYPE_SMART_SCHEDULE\x10\x04\x12+\n\'ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE\x10\x05\x42^\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.evchargingB\x19\x45VChargingProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "EVChargingProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.evchargingB\031EVChargingProtobufMessageP\001"
    _globals["_ENUMCHARGECONTROLOPERATION"]._serialized_start = 1219
    _globals["_ENUMCHARGECONTROLOPERATION"]._serialized_end = 1375
    _globals["_ENUMCHARGESTATE"]._serialized_start = 1378
    _globals["_ENUMCHARGESTATE"]._serialized_end = 1621
    _globals["_ENUMCHARGETYPE"]._serialized_start = 1624
    _globals["_ENUMCHARGETYPE"]._serialized_end = 1861
    _globals["_SETCHARGECONTROLREQUEST"]._serialized_start = 78
    _globals["_SETCHARGECONTROLREQUEST"]._serialized_end = 188
    _globals["_SETCHARGECONTROLRESPONSE"]._serialized_start = 190
    _globals["_SETCHARGECONTROLRESPONSE"]._serialized_end = 265
    _globals["_GETCHARGESTATEREQUEST"]._serialized_start = 267
    _globals["_GETCHARGESTATEREQUEST"]._serialized_end = 290
    _globals["_GETCHARGESTATERESPONSE"]._serialized_start = 293
    _globals["_GETCHARGESTATERESPONSE"]._serialized_end = 433
    _globals["_SETCHARGESETTINGSREQUEST"]._serialized_start = 436
    _globals["_SETCHARGESETTINGSREQUEST"]._serialized_end = 581
    _globals["_SETCHARGESETTINGSRESPONSE"]._serialized_start = 583
    _globals["_SETCHARGESETTINGSRESPONSE"]._serialized_end = 659
    _globals["_GETCHARGESETTINGSREQUEST"]._serialized_start = 661
    _globals["_GETCHARGESETTINGSREQUEST"]._serialized_end = 687
    _globals["_GETCHARGESETTINGSRESPONSE"]._serialized_start = 690
    _globals["_GETCHARGESETTINGSRESPONSE"]._serialized_end = 885
    _globals["_NOTIFYCHARGESTATE"]._serialized_start = 888
    _globals["_NOTIFYCHARGESTATE"]._serialized_end = 1023
    _globals["_NOTIFYCHARGESETTINGS"]._serialized_start = 1026
    _globals["_NOTIFYCHARGESETTINGS"]._serialized_end = 1216
# @@protoc_insertion_point(module_scope)
