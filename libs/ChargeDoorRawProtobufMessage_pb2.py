# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ChargeDoorRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n"ChargeDoorRawProtobufMessage.proto\x12\x17jlr.protobuf.chargedoor\x1a\x10\x45numStatus.proto"\xb3\x01\n\x1dSetChargeDoorOperationRequest\x12\x41\n\x10\x63harge_door_side\x18\x01 \x01(\x0e\x32\'.jlr.protobuf.chargedoor.EnumChargeDoor\x12O\n\x15\x63harge_door_operation\x18\x02 \x01(\x0e\x32\x30.jlr.protobuf.chargedoor.EnumChargeDoorOperation"Q\n\x1eSetChargeDoorOperationResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"l\n\x1eSetChargeCableOperationRequest\x12J\n\x0f\x63harge_cable_op\x18\x01 \x01(\x0e\x32\x31.jlr.protobuf.chargedoor.EnumChargeCableOperation"R\n\x1fSetChargeCableOperationResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\xf0\x01\n\x16NotifyChargeDoorStatus\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12Q\n\x17left_charge_door_status\x18\x02 \x01(\x0e\x32\x30.jlr.protobuf.chargedoor.EnumChargeDoorOperation\x12R\n\x18right_charge_door_status\x18\x03 \x01(\x0e\x32\x30.jlr.protobuf.chargedoor.EnumChargeDoorOperation*i\n\x0e\x45numChargeDoor\x12 \n\x1c\x45NUM_CHARGE_DOOR_UNSPECIFIED\x10\x00\x12\x19\n\x15\x45NUM_CHARGE_DOOR_LEFT\x10\x01\x12\x1a\n\x16\x45NUM_CHARGE_DOOR_RIGHT\x10\x02*\xe2\x01\n\x17\x45numChargeDoorOperation\x12*\n&ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_CHARGE_DOOR_OPERATION_OPEN\x10\x01\x12$\n ENUM_CHARGE_DOOR_OPERATION_CLOSE\x10\x02\x12*\n&ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS\x10\x03\x12$\n ENUM_CHARGE_DOOR_OPERATION_ERROR\x10\x04*\xbc\x01\n\x18\x45numChargeCableOperation\x12+\n\'ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED\x10\x00\x12&\n"ENUM_CHARGE_CABLE_OPERATION_UNLOCK\x10\x01\x12$\n ENUM_CHARGE_CABLE_OPERATION_LOCK\x10\x02\x12%\n!ENUM_CHARGE_CABLE_OPERATION_ERROR\x10\x03\x42\x61\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.chargedoorB\x1c\x43hargeDoorRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "ChargeDoorRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n?com.jaguarlandrover.commandandcontrolprotobuflibrary.chargedoorB\034ChargeDoorRawProtobufMessageP\001"
    _globals["_ENUMCHARGEDOOR"]._serialized_start = 783
    _globals["_ENUMCHARGEDOOR"]._serialized_end = 888
    _globals["_ENUMCHARGEDOOROPERATION"]._serialized_start = 891
    _globals["_ENUMCHARGEDOOROPERATION"]._serialized_end = 1117
    _globals["_ENUMCHARGECABLEOPERATION"]._serialized_start = 1120
    _globals["_ENUMCHARGECABLEOPERATION"]._serialized_end = 1308
    _globals["_SETCHARGEDOOROPERATIONREQUEST"]._serialized_start = 82
    _globals["_SETCHARGEDOOROPERATIONREQUEST"]._serialized_end = 261
    _globals["_SETCHARGEDOOROPERATIONRESPONSE"]._serialized_start = 263
    _globals["_SETCHARGEDOOROPERATIONRESPONSE"]._serialized_end = 344
    _globals["_SETCHARGECABLEOPERATIONREQUEST"]._serialized_start = 346
    _globals["_SETCHARGECABLEOPERATIONREQUEST"]._serialized_end = 454
    _globals["_SETCHARGECABLEOPERATIONRESPONSE"]._serialized_start = 456
    _globals["_SETCHARGECABLEOPERATIONRESPONSE"]._serialized_end = 538
    _globals["_NOTIFYCHARGEDOORSTATUS"]._serialized_start = 541
    _globals["_NOTIFYCHARGEDOORSTATUS"]._serialized_end = 781
# @@protoc_insertion_point(module_scope)
