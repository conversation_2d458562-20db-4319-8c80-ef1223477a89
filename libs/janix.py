#!/usr/bin/env python3
import time
from serial import Serial
from serial.serialutil import SerialException


__version__ = "0.0.6"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
EXECPTION_MESSAGE = "Incorrect CAN Frame number ID must be 0 to 7"


# Custom exception for invalid can messages
class CanMessageException(Exception):
    def __init__(self, message="Can message exception has occured"):
        self.message = message
        super().__init__(self.message)


def open_serial_port(SerialPort):
    try:
        ser = Serial(SerialPort, 115200, timeout=1)
    except SerialException:
        print("Janix serial exception")
        ser = -1

    return ser


def set_power_mode(SerialPort, powerMode):
    """
    Connects to the Janix via the serial port and send the command for setting the Power Mode.
    Power modes can be 0, 2, 4, 6, 9 or 7

    Examples:
    | set_power_mode | /dev/ttyUSB0 | 7 |
    """
    result = 0
    ser = open_serial_port(SerialPort)
    if ser != -1:
        print(powerMode)
        ser.write(b"\x4a\x4e\x58\x31\x00\x02\x40" + int(powerMode).to_bytes(1, "big"))
        time.sleep(0.5)
        ser.close()
    else:
        print("no serial port:: " + SerialPort)
        result = -1

    return result


def brake_fluid_level_warning(SerialPort, state):
    """
    Connects to the Janix via the serial port and toggles the brakeFluid warning state.

    Examples:
    | BrakeFluidLevelWarning | /dev/ttyUSB0 | True |

    """
    ser = open_serial_port(SerialPort)

    print(state)
    if state == "True":
        janixMessage = b"\x69\x43\x41\x4e\x0f\x01\x01\x02\x03\xA7\x00\x20\x00\x00\x00\x00\x00\x00"
    else:
        janixMessage = b"\x69\x43\x41\x4e\x0f\x01\x01\x02\x03\xA7\x00\x00\x00\x00\x00\x00\x00\x00"

    print(janixMessage)
    ser.write(janixMessage)
    time.sleep(0.5)
    ser.close()


def reset_janix(SerialPort):
    """
    Resets Janix via the serial port.

    Examples:
    | Reset Janix | /dev/ttyUSB0 |

    """
    ser = open_serial_port(SerialPort)
    ser.write(b"\x4a\x4e\x58\x31\x00\x01\x20")
    time.sleep(5)
    ser.close()


def load_config_file(SerialPort, configFile="VDC_Test.JNX1rig"):
    """
    Resets Janix via the serial port.
    config file defaults to VDC_Test.JNX1rig

    Examples:
    | Reset Janix | /dev/ttyUSB0 | ConfigA.JNX1rig |

    """

    configFileBytes = str.encode(configFile)
    print((configFileBytes.hex()))
    dataLength = len(configFile) + 2

    ser = open_serial_port(SerialPort)
    janixMessage = b"\x4a\x4e\x58\x31\x00" + int(dataLength).to_bytes(1, "big") + b"\x60\x01" + configFileBytes
    ser.write(janixMessage)
    time.sleep(3)
    ser.close()


def enable_can(SerialPort, CanState):
    """
    Set the status of sending CAN.
    1 byte = 0 (CAN Off), 1 (CAN On), 2 (CAN Sleep).

    Examples:
    | Enable CAN | /dev/ttyUSB0 | 1 |

    """

    states = [0, 1, 2]
    if CanState not in states:
        raise CanMessageException("Incorrect CAN State")

    janixMessage = b"\x4a\x4e\x58\x31\x00\x02\x41" + int(CanState).to_bytes(1, "big")
    ser = open_serial_port(SerialPort)
    ser.write(janixMessage)
    time.sleep(0.5)
    ser.close()


def turn_off_can_message(SerialPort, frame_number, can_bus_number):
    """
    Turn off a can message. on a particular bus numer i.e. bus 1 or 1
    1 byte = 0x2FE.

    Examples:
    | Enable CAN | /dev/ttyUSB0 | 0x2FE | 1

    """

    if frame_number < 0 or frame_number > 7:
        raise CanMessageException(EXECPTION_MESSAGE)

    janixMessage = b"\x4a\x4e\x58\x31\x00\x03" + b"\x0e" + int(can_bus_number).to_bytes(1, "big") + int(frame_number).to_bytes(1, "big")
    print(janixMessage)
    ser = open_serial_port(SerialPort)
    ser.write(janixMessage)
    time.sleep(0.5)
    ser.close()


def turn_on_can_message(serial_port, frame_number, can_bus_number):
    """
    Turn on a can message. on a particular bus numer i.e. bus 1 or 1
    1 byte = 0x2FE.

    Examples:
    | Turn on CAN message| /dev/ttyUSB0 | 0x2FE | 1

    """

    if frame_number < 0 or frame_number > 7:
        raise CanMessageException(EXECPTION_MESSAGE)

    janixMessage = b"\x4a\x4e\x58\x31\x00\x03" + b"\x0d" + int(can_bus_number).to_bytes(1, "big") + int(frame_number).to_bytes(1, "big")
    ser = open_serial_port(serial_port)
    print(janixMessage)
    ser.write(janixMessage)
    time.sleep(0.5)
    ser.close()


def report_current_can_data(serial_port, can_bus_number):
    """
    *****  not supported on Janix ********
    Report all the can data being transmitted on a bus channel
    i.e. 1 or x01 for can bus 1
        2 or x02 for can bus 2
    Example:
    | /dev/ttyUSB0 | 0x01|
    """
    janixMessage = b"\x69\x43\x41\x4E" + b"\x10" + int(can_bus_number).to_bytes(1, "big")
    print(janixMessage)
    ser = open_serial_port(serial_port)
    result = ser.write(janixMessage)
    print(result)
    time.sleep(0.5)
    ser.close()
    return result


def send_periodic_can_message(serial_port, can_message_id, can_message, period, frame_number, can_bus_number):
    """
    Connects to the Janix via the serial port and send a CAN message.
    CanMessageID defined as dec
    CanMessage defined as integer. eg 0x0000030000000000  or 3298534883328
    frame_number defined as dec

    Examples:
    | Send Periodic CAN Message | /dev/ttyUSB0 | 0x935 | 0xafbfcf33efeaadff| 2 | 5 | 1 |

    """
    message = can_message.to_bytes(8, byteorder="big")
    print("0x" + message.hex())

    message_id = can_message_id.to_bytes(2, byteorder="big")
    print("0x" + message_id.hex())

    if frame_number < 0 or frame_number > 7:
        raise CanMessageException(EXECPTION_MESSAGE)

    if len(message_id) != 2:
        raise CanMessageException("Incorrect CAN Message ID")

    print(len(message))
    if len(message) != 8:
        raise CanMessageException("Incorrect CAN Message - too long or short")

    janixMessage = (
        b"\x69\x43\x41\x4E"
        + b"\x0f"
        + can_bus_number.to_bytes(1, "big")
        + frame_number.to_bytes(1, "big")
        + period.to_bytes(1, "big")
        + message_id
        + message
    )

    print(janixMessage.hex())
    ser = open_serial_port(serial_port)
    ser.write(janixMessage)

    time.sleep(0.5)
    turn_on_can_message(serial_port, frame_number, can_bus_number)
    ser.close()
