import requests
import json
import forgerockOTP
import oabUsers
import time
import uuid
import CreateVehicleDigitalLambda
import oabVehicle
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig

systemVarFile = "../variables/system.yml"


@not_keyword
def get_url(url):
    systemVars = BaseConfig(parse_config(systemVarFile))
    return getattr(systemVars, url)


@keyword("Create User and Vehicle")
def create_user_and_vehicle(
    password, country, request_body, request_body_post_user, vroom_client_id_oab, vroom_client_secret_oab, timeout=120, interval=10
):

    try:
        # Create the user
        email, first_name, last_name = forgerockOTP.createForgeRockUser(password, country)
        print(f"User created: {email}, {first_name} {last_name}")

        # Post customer details
        response_status_code = oabUsers.post_customer_details_via_GW(email, password, request_body_post_user, approov=True, auth=True)
        if response_status_code[1] != 200:
            raise Exception(f"Failed to create user details. Status Code: {response_status_code[1]}")
        decodedToken = response_status_code[2]

        mgd_attributes = decodedToken["Payload"]["mgd-identifiers"][1]
        print(f"This is the decoded token: {decodedToken}")
        print(f"This is the mgd_attributes: {mgd_attributes}")

        # Create vehicle and bind it to user
        vuid = create_vehicle_and_bind(email, password, request_body, vroom_client_id_oab, vroom_client_secret_oab, mgd_attributes, timeout, interval)
        print(f"Vehicle bound successfully. VUID: {vuid}")

        return vuid

    except Exception as e:
        print(f"Error during user and vehicle creation: {str(e)}")
        raise  # Ensures the failure propagates correctly


@keyword("Create vehicle in DV bind to FR user and get the vehicle VUID")
def create_vehicle_and_bind(user, password, requestBody, vroomClientIdOAB, vroomClientSecretOAB, mgd_attributes, timeout=120, interval=10):

    try:
        # Step 1: Create a vehicle and retrieve VIN
        vehicle_data = CreateVehicleDigitalLambda.create_vehicle_calling_dv_lambda()
        if not vehicle_data or len(vehicle_data) < 2:
            print("Error: Vehicle creation failed or unexpected response format.")
            return None

        randomVin = vehicle_data[1]
        print(f"Generated VIN: {randomVin}")

        # Step 2: Generate authentication token
        token = generate_keycloak_token_for_vehicle_binding(vroomClientIdOAB, vroomClientSecretOAB)
        if not token:
            print("Error: Failed to generate authentication token.")
            return None

        # Step 3: Bind vehicle to user
        bind_vehicle_to_FR_user(user, password, token, randomVin, requestBody, mgd_attributes)

        # Step 4: Poll for vehicle registration instead of using sleep
        obj = poll_for_vehicle(user, password, randomVin, timeout, interval)
        if not obj:
            print("Error: Vehicle not found after polling.")
            return None

        vuid = obj.get("vid")
        if not vuid:
            print("Error: VUID missing in retrieved vehicle object.")
            return None

        print(f"Successfully retrieved VUID: {vuid}")
        return vuid

    except json.JSONDecodeError:
        print("Error: Failed to parse API response JSON.")
    except Exception as e:
        print(f"Unexpected error: {e}")
    return None


def generate_keycloak_token_for_vehicle_binding(vroomClientIdOAB, vroomClientSecretOAB):

    payload = {"client_id": vroomClientIdOAB, "client_secret": vroomClientSecretOAB, "grant_type": "client_credentials"}

    url = get_url("keycloak-url-vehicle-binding")

    try:
        response = requests.post(url, data=payload)
        response.raise_for_status()  # Raises an HTTPError for bad responses (4xx, 5xx)

        json_response = response.json()  # Automatically parses JSON response
        access_token = json_response.get("access_token")

        if not access_token:
            print("Error: Access token missing in response.")
            return None

        return access_token

    except requests.exceptions.RequestException as e:
        print(f"API Request Failed: {e}")
        return None
    except json.JSONDecodeError:
        print("Error: Failed to parse JSON response.")
        return None


@not_keyword
def bind_vehicle_to_FR_user(user, password, token, vin, requestBody, mgd_attributes):

    url = get_url("vroom-binding-endpoint")

    correlation_id = str(uuid.uuid4())[3:]  # Generates unique correlation ID

    # Read and parse the JSON request body
    with open(requestBody, "r") as file:
        jsonRequestBody = json.load(file)

    # Update JSON payload with VIN
    jsonRequestBody["vin"] = vin

    jsonRequestBody["userId"] = mgd_attributes

    print(f"This is the jsonRequestBody: {jsonRequestBody}")

    # Prepare headers
    headers = {"Content-Type": "application/json", "Correlation-Id": correlation_id, "Authorization": token}

    # Send POST request
    response = requests.post(url, json=jsonRequestBody, headers=headers)

    print(response)
    print(response.status_code)

    return response, response.status_code


# Function to find and print the matching object
def find_object(data, key, value):
    for obj in data:
        if isinstance(obj, dict) and obj.get(key) == value:
            return obj  # Stop searching after finding the first match
    print("No matching object found.")
    return None


def poll_for_vehicle(user, password, expected_vin, timeout=120, interval=10):

    start_time = time.time()

    while time.time() - start_time < timeout:
        response = oabVehicle.get_user_vehicle(user, password, approov=True)
        if not response or not isinstance(response, tuple) or len(response) < 1:
            print("Error: Unexpected API response format.")
            return None

        try:
            response_data = json.loads(response[0])
            if isinstance(response_data, list):
                obj = find_object(response_data, "vin", expected_vin)
                if obj:
                    print("Vehicle found!")
                    return obj  # Return the vehicle object
        except json.JSONDecodeError:
            print("Error: Failed to parse JSON response.")

        print(f"VIN {expected_vin} not found yet. Retrying in {interval} seconds...")
        time.sleep(interval)  # Wait before retrying

    print("Timeout reached. VIN not found.")
    return None
