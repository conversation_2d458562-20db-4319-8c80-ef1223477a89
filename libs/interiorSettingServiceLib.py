import interior_setting_service_pb2 as interiorSettingServiceProto
import notifyAdaptorProtbuf
from robot.api.deco import keyword


@keyword("Create serialised protobuf message for Child Presence Detection")
def ChildPresenceDetection(cpdValue):
    testdata = interiorSettingServiceProto.NotifyChildPresenceDetectionStatus()
    testdata.status = 1
    testdata.child_presence_status = int(cpdValue)
    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)
    # pack it in the envelope
    protobuf_envelope, now = notifyAdaptorProtbuf.envelope("interior_setting_service.NotifyChildPresenceDetectionStatus", protobuf_testdata)
    return (protobuf_envelope, now)
