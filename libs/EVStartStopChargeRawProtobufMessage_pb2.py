# flake8: noqa
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: EVStartStopChargeRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n)EVStartStopChargeRawProtobufMessage.proto\x12\x18jlr.protobuf.evstartstop\x1a\x10\x45numStatus.proto"o\n\x17SetChargeControlRequest\x12T\n\x16\x63harge_control_request\x18\x01 \x01(\x0e\x32\x34.jlr.protobuf.evstartstop.EnumChargeControlOperation"K\n\x18SetChargeControlResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x17\n\x15GetChargeStateRequest"\x8d\x01\n\x16GetChargeStateResponse\x12\x42\n\x0f\x63harging_status\x18\x01 \x01(\x0e\x32).jlr.protobuf.evstartstop.EnumChargeState\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x88\x01\n\x11NotifyChargeState\x12\x42\n\x0f\x63harging_status\x18\x01 \x01(\x0e\x32).jlr.protobuf.evstartstop.EnumChargeState\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\x9c\x01\n\x1a\x45numChargeControlOperation\x12-\n)ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED\x10\x00\x12\'\n#ENUM_CHARGE_CONTROL_OPERATION_START\x10\x01\x12&\n"ENUM_CHARGE_CONTROL_OPERATION_STOP\x10\x02*\xf3\x01\n\x0f\x45numChargeState\x12!\n\x1d\x45NUM_CHARGE_STATE_UNSPECIFIED\x10\x00\x12!\n\x1d\x45NUM_CHARGE_STATE_DISCHARGING\x10\x01\x12\'\n#ENUM_CHARGE_STATE_WAITING_TO_CHARGE\x10\x02\x12(\n$ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS\x10\x03\x12#\n\x1f\x45NUM_CHARGE_STATE_FULLY_CHARGED\x10\x04\x12"\n\x1e\x45NUM_CHARGE_STATE_CHARGE_ERROR\x10\x05*\xed\x01\n\x0e\x45numChargeType\x12 \n\x1c\x45NUM_CHARGE_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_CHARGE_TYPE_IMMEDIATE\x10\x01\x12#\n\x1f\x45NUM_CHARGE_TYPE_FIXED_SCHEDULE\x10\x02\x12"\n\x1e\x45NUM_CHARGE_TYPE_SCHEDULE_PLUS\x10\x03\x12#\n\x1f\x45NUM_CHARGE_TYPE_SMART_SCHEDULE\x10\x04\x12+\n\'ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE\x10\x05\x42i\<EMAIL>#EVStartStopChargeRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "EVStartStopChargeRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\<EMAIL>#EVStartStopChargeRawProtobufMessageP\001"
    _globals["_ENUMCHARGECONTROLOPERATION"]._serialized_start = 588
    _globals["_ENUMCHARGECONTROLOPERATION"]._serialized_end = 744
    _globals["_ENUMCHARGESTATE"]._serialized_start = 747
    _globals["_ENUMCHARGESTATE"]._serialized_end = 990
    _globals["_ENUMCHARGETYPE"]._serialized_start = 993
    _globals["_ENUMCHARGETYPE"]._serialized_end = 1230
    _globals["_SETCHARGECONTROLREQUEST"]._serialized_start = 89
    _globals["_SETCHARGECONTROLREQUEST"]._serialized_end = 200
    _globals["_SETCHARGECONTROLRESPONSE"]._serialized_start = 202
    _globals["_SETCHARGECONTROLRESPONSE"]._serialized_end = 277
    _globals["_GETCHARGESTATEREQUEST"]._serialized_start = 279
    _globals["_GETCHARGESTATEREQUEST"]._serialized_end = 302
    _globals["_GETCHARGESTATERESPONSE"]._serialized_start = 305
    _globals["_GETCHARGESTATERESPONSE"]._serialized_end = 446
    _globals["_NOTIFYCHARGESTATE"]._serialized_start = 449
    _globals["_NOTIFYCHARGESTATE"]._serialized_end = 585
# @@protoc_insertion_point(module_scope)
