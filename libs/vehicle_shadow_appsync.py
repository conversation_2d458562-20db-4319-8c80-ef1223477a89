import boto3
from botocore.exceptions import (
    ClientError,
    ParamValidationError,
    NoCredentialsError,
    EndpointConnectionError,
    WaiterError,
)
import logging


class AppSyncException(
    ClientError,
    ValueError,
    ParamValidationError,
    NoCredentialsError,
    EndpointConnectionError,
    WaiterError,
):  # pragma: no cover
    pass


class AppSync:
    """
    Mainly wrapper methods for AWS boto3 sdk dynamodb resource
    """

    def __init__(self, *args):
        self.appsync_client = boto3.client("appsync", region_name="eu-west-2")

    def get_api_id(self, api_name):
        """
        Returns the api
        :param api_name: name of the api to get the api id
        """
        response = self.appsync_client.graphql_apis()

        for api in response["graphqlApis"]:
            if api_name == api["name"]:
                return api
        return None

    def execute_query(self, appsync_apiname, query):
        """
        Returns results of an aws appysync query executed
        :param appsync_apiname: api on which to run the query
        :param query: actual query to execute on appsync

        """
        api = self.get_api_id(api_name=appsync_apiname)

        request_paramenters = {"apiId": api["id"], "query": query}

        try:
            response = self.appsync_client.start_query_execution(**request_paramenters)
            query_execution_id = response["queryExecutionId"]

            result = self.appsync_client.get_query_results(apiId=api["id"], queryExecutionId=query_execution_id)

            return result

        except AppSyncException as error:
            logging.info(f"AppSync Client error for querying {appsync_apiname} with {query}. {error}")
            raise
