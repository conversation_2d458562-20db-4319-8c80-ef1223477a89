# flake8: noqa
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: MaxStateOfChargeRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(MaxStateOfChargeRawProtobufMessage.proto\x12\x1djlr.protobuf.maxstateofcharge\x1a\x10\x45numStatus.proto"<\n!SetBatteryMaxStateOfChargeRequest\x12\x17\n\x0fmax_battery_soc\x18\x01 \x01(\r"U\n"SetBatteryMaxStateOfChargeResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\'\n%GetBatteryCurrentStateOfChargeRequest"v\n&GetBatteryCurrentStateOfChargeResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12\x1b\n\x13\x63urrent_battery_soc\x18\x02 \x01(\r"q\n!NotifyBatteryCurrentStateOfCharge\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus\x12\x1b\n\x13\x63urrent_battery_soc\x18\x02 \x01(\rBm\nEcom.jaguarlandrover.commandandcontrolprotobuflibrary.maxstateofchargeB"MaxStateOfChargeRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "MaxStateOfChargeRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b'\nEcom.jaguarlandrover.commandandcontrolprotobuflibrary.maxstateofchargeB"MaxStateOfChargeRawProtobufMessageP\001'
    )
    _globals["_SETBATTERYMAXSTATEOFCHARGEREQUEST"]._serialized_start = 93
    _globals["_SETBATTERYMAXSTATEOFCHARGEREQUEST"]._serialized_end = 153
    _globals["_SETBATTERYMAXSTATEOFCHARGERESPONSE"]._serialized_start = 155
    _globals["_SETBATTERYMAXSTATEOFCHARGERESPONSE"]._serialized_end = 240
    _globals["_GETBATTERYCURRENTSTATEOFCHARGEREQUEST"]._serialized_start = 242
    _globals["_GETBATTERYCURRENTSTATEOFCHARGEREQUEST"]._serialized_end = 281
    _globals["_GETBATTERYCURRENTSTATEOFCHARGERESPONSE"]._serialized_start = 283
    _globals["_GETBATTERYCURRENTSTATEOFCHARGERESPONSE"]._serialized_end = 401
    _globals["_NOTIFYBATTERYCURRENTSTATEOFCHARGE"]._serialized_start = 403
    _globals["_NOTIFYBATTERYCURRENTSTATEOFCHARGE"]._serialized_end = 516
# @@protoc_insertion_point(module_scope)
