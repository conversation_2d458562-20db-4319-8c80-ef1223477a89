from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class EnumStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_STATUS_UNSPECIFIED: _ClassVar[EnumStatus]
    ENUM_STATUS_OK: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_DEGRADED: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_UNRELIABLE: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_UNAVAILABLE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_SERVICE_STATE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_MISSING_INPUT_FIELD: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_INPUT_FIELD: _ClassVar[EnumStatus]
    ENUM_STATUS_NOT_OK: _ClassVar[EnumStatus]

ENUM_STATUS_UNSPECIFIED: EnumStatus
ENUM_STATUS_OK: EnumStatus
ENUM_STATUS_DATA_DEGRADED: EnumStatus
ENUM_STATUS_DATA_UNRELIABLE: EnumStatus
ENUM_STATUS_DATA_UNAVAILABLE: EnumStatus
ENUM_STATUS_ERROR_INVALID_SERVICE_STATE: EnumStatus
ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE: EnumStatus
ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION: EnumStatus
ENUM_STATUS_ERROR_MISSING_INPUT_FIELD: EnumStatus
ENUM_STATUS_ERROR_INVALID_INPUT_FIELD: EnumStatus
ENUM_STATUS_NOT_OK: EnumStatus
