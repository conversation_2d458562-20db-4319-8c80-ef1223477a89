from pyaml_env import parse_config, BaseConfig
import requests
import json
import vcdpHiveBroker
import base64
import dvS<PERSON><PERSON><PERSON>ler
from robot.api.deco import keyword, not_keyword

from commonUtils import get_keycloak_token

systemVarFile = "../variables/system.yml"

vin = "VIN01234567891234"
contentType = "application/json"
authType = "Bearer "
diagnosticConfigEndpoint = "/api/dictionaries/diagnostic/config"
EMPTYBODY = "No response body returned"
json_response_file = "../json-request-data/vehicle-reponse-config/get-response-vehicle.json"
vehicle_config_file = "../json-request-data/vehicle-reponse-config/vehicle-config-file.json"
diagnostic_file = "../json-request-data/vehicle-reponse-config/diagnostic-data.json"
signal_file = "../json-request-data/vehicle-reponse-config/signal-data.json"
updated_manifest_file = "../json-request-data/vehicle-config-initialisation/updated-manifest.json"
udr_signal_check = "../json-request-data/vehicle-reponse-config/udr-signal-check.json"


@not_keyword
def load_json(jsonFilename):
    with open(jsonFilename, "r") as jsonFile:
        return json.dumps(json.load(jsonFile))


@not_keyword
def get_url(urlName):
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, urlName)


@keyword("Get diagnostic config")
def get_diagnostic_config(authUsername, authPassword):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dictionary-service-url")
    getDiagnosticConfigUrl = baseUrl + diagnosticConfigEndpoint

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", getDiagnosticConfigUrl, headers=headers)
    return response.text, response.status_code


@keyword("Get diagnostic config with args")
def get_diagnostic_config_with_args(authUsername, authPassword, pageNumber, pageSize):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dictionary-service-url")
    getDiagnosticConfigUrl = baseUrl + "/api/dictionaries/diagnostic/config?pageNumber=" + pageNumber + "&pageSize=" + pageSize

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", getDiagnosticConfigUrl, headers=headers)
    return response.text, response.status_code


@keyword("Get diagnostic config with VIN")
def get_diagnostic_config_with_vin(authUsername, authPassword):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
        "VIN": "VIN01234567891234",
    }

    baseUrl = get_url("dictionary-service-url")
    getDiagnosticConfigUrl = baseUrl + diagnosticConfigEndpoint

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", getDiagnosticConfigUrl, headers=headers)
    return response.text, response.status_code


@keyword("Get network config")
def get_network_config(authUsername, authPassword):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dictionary-service-url")
    getNetworkConfigUrl = baseUrl + "/api/dictionaries/network/config"

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", getNetworkConfigUrl, headers=headers)
    return response.text, response.status_code


@keyword("Get network config with args")
def get_network_config_with_args(authUsername, authPassword, pageNumber, pageSize):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dictionary-service-url")
    getNetworkConfigUrl = baseUrl + "/api/dictionaries/network/config?pageNumber=" + pageNumber + "&pageSize=" + pageSize

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("GET", getNetworkConfigUrl, headers=headers)
    return response.text, response.status_code


@keyword("Get Number Of Dictionary Config Records")
def get_number_of_dictionary_config_records(configDictionaryResponse):
    jsonReponse = json.loads(configDictionaryResponse)
    return len(jsonReponse["dataDefinition"])


@keyword("Create vehicle:Dictionary Service")
def create_vehicle(authUsername, authPassword, createVehicleJson):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dv-state-handler-url")
    createVehicleUrl = baseUrl + "/vehicles/create"

    payload = load_json(createVehicleJson)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("POST", createVehicleUrl, headers=headers, data=payload)
    return response.text, response.status_code


def update_vehicle(authUsername, authPassword, updateVehicleJson):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dv-state-handler-url")
    patchVehicleUrl = baseUrl + "/vehicles/updateVehicle/" + vin

    payload = load_json(updateVehicleJson)

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("PATCH", patchVehicleUrl, headers=headers, data=payload)
    return response.status_code


@keyword("Delete vehicle:Dictionary Service")
def delete_vehicle(authUsername, authPassword):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {
        "accept": "*/*",
        "Content-Type": contentType,
        "Authorization": authType + token,
    }

    baseUrl = get_url("dv-state-handler-url")
    deleteVehicleUrl = baseUrl + "/vehicles/" + vin

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("DELETE", deleteVehicleUrl, headers=headers)
    return response.text, response.status_code


@keyword("Delete vehicle specific diagnostic dictionary")
def delete_vehicle_specific_diagnostic_dictionary(authUsername, authPassword):
    token = get_keycloak_token(authUsername, authPassword)

    headers = {"Authorization": authType + token, "VIN": vin}

    baseUrl = get_url("dictionary-service-url")
    deleteDictionaryUrl = baseUrl + diagnosticConfigEndpoint

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = requests.request("DELETE", deleteDictionaryUrl, headers=headers)
    return response.text, response.status_code


@keyword("Diagnostic Configuration from dictionary service")
def Diagnostic_configuration(user, pw, vin, parameter):
    token = get_keycloak_token(user, pw)

    vin_string = str(vin)
    print(type(vin_string))
    print(vin_string)

    vin_striped = vin_string.strip("['']")
    header = {"accept": "*/*", "VIN": vin_striped, "Content-Type": contentType, "Requester": "Robot", "Authorization": authType + token}

    print(vin)
    url = get_url("dictionary-service-url")
    dictionary = url + "/api/dictionaries/diagnostic/config?udrCompliant=true"
    print(dictionary)
    response = requests.request("GET", dictionary, headers=header)
    print(type(response.text))
    print("this is response :", response.text)
    file = open(json_response_file, "w")
    responsestr = str(response.text)
    file.write(responsestr)
    file.close()
    label = udr_validation(parameter)
    return label


def udr_validation(parameter):
    if parameter == "DIAGNOSTIC":
        open_json_file = open(json_response_file, "r")
        read_json_data = open_json_file.read()
        actual_file = json.loads(read_json_data)
        str_json = str(actual_file)
        if "VA-DTC-BCCM-0x8F" in str_json:
            response = "DIAGNOSTIC UDR LABEL"
        else:
            response = "DIAGNOSTIC LEGACY LABEL"
    elif parameter == "NETWORK":
        open_json_file = open(signal_file, "r")
        read_json_data = open_json_file.read()
        actual_file = json.loads(read_json_data)
        str_json = str(actual_file)
        if "VA-SIG-A2_STAT" in str_json:
            response = "NETWORK UDR LABEL"
        else:
            response = "NETWORK LEGACY LABEL"

    print("this is label status:", response)
    return response


@keyword("Network Configuration from dictionary service")
def network_configuration(uniqueId, brokerUser, brokerClientSecret, parameter, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False):
    hive_vehicle_config(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False)
    diagnosticFile = open(diagnostic_file, "w")
    signalFile = open(signal_file, "w")
    openJsonData = open(vehicle_config_file, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    for i in range(3):
        if jsonRequestBody[i]["type"] == "DIAGNOSTIC_DICTIONARY":
            diagPosition = i
            print("DIAGNOSTIC_DICTIONARY POSITION:", diagPosition)
        elif jsonRequestBody[i]["type"] == "SIGNAL_DICTIONARY":
            sigPosition = i
            print("SIGNAL_DICTIONARY POSITION:", sigPosition)
    diagnosticData = jsonRequestBody[diagPosition]["payload"]
    diagnosticDataStr = str(diagnosticData)
    diagnosticFile.write(diagnosticDataStr)
    diagnosticFile.close()
    b64_decode(diagnostic_file)
    signalData = jsonRequestBody[sigPosition]["payload"]
    signalDataStr = str(signalData)
    signalFile.write(signalDataStr)
    signalFile.close()
    b64_decode(signal_file)
    label = udr_validation(parameter)
    return label


@keyword("Vechicle configuration check")
def vehicle_config_validation(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False):
    hive_vehicle_config(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False)
    open_json_file = open(vehicle_config_file, "r")
    read_json_data = open_json_file.read()
    str_json = str(read_json_data)

    if "payload" in str_json:
        if "SIGNAL_DICTIONARY" in str_json or "DIAGNOSTIC_DICTIONARY" in str_json:
            response = "INVALID VEHICLE CONFIG CONTAINS SIGNAL DICTIONARY AND DIAGNOSTIC DICTIONARY"
        else:
            response = "VALID VEHICLE CONFIG CONTAINING ONLY QUERY"
    else:
        response = "NO DATA"
    print("this is respnse:", response)
    return response


@not_keyword
def b64_decode(data_file):
    # Decode diagnostic dictionary and signal dictionary
    File = open(data_file, "r+")
    Data = File.read()
    decodedData = base64.b64decode(Data)
    DataStr = str(decodedData)
    DataStrip = DataStr.strip("b'")
    File.seek(0)
    File.write(DataStrip)
    File.truncate()
    File.close()


@keyword("Updated dictionaries version manifest")
def update_manifest(data, user, pw, unique_id):
    # updating dictionaries new versions in manifest

    diagFile = open(diagnostic_file, "r")
    diagData = diagFile.read()
    diagBody = json.loads(diagData)
    diagVersion = diagBody["version"]
    print("Diagnostic Dictionary Version:", diagVersion)
    sigFile = open(signal_file, "r")
    sigData = sigFile.read()
    sigBody = json.loads(sigData)
    sigVersion = sigBody["version"]
    print("Signal Dictionary Version:", sigVersion)
    manifestFile = open(updated_manifest_file, "r+")
    manifestData = manifestFile.read()
    manifestBody = json.loads(manifestData)
    manifestBody["DIAGNOSTICS_DICTIONARY"] = diagVersion
    manifestBody["SIGNAL_DICTIONARY"] = sigVersion
    manifestBody.update(manifestBody)
    json.dumps(manifestBody)
    manifestFile.seek(0)
    manifestBodyStr = str(manifestBody)
    manifestBodyStrUpdated = manifestBodyStr.replace("'", '"')
    print("updated test:", manifestBodyStrUpdated)
    manifestFile.write(manifestBodyStrUpdated)
    manifestFile.truncate()
    manifestFile.close()
    dvStateHandler.activate_config(data, user, pw, unique_id)

    return "MANIFEST SENT WITH UPDATED DICTIONARY VERSIONS"


@not_keyword
def hive_vehicle_config(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False):
    uniqueIdString = str(uniqueId)
    uniqueIdStriped = uniqueIdString.strip("['']")
    print("uniqueid", uniqueIdStriped)
    topic = "vcdp/" + uniqueIdStriped + "/va/ctrl/query"
    print("topic::", topic)
    vehicleConfiguration = vcdpHiveBroker.subscribe_to_topic(
        uniqueIdStriped, topic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, writeToFile
    )
    print("this is vehicle configuration :", vehicleConfiguration)
    file = open(vehicle_config_file, "w")
    configStr = str(vehicleConfiguration)
    configStrip = configStr.strip("(b'', 1)")
    file.write(configStrip)
    file.close()


@keyword("SIGNAL UDR VALIDATION")
def signal_udr_check(parameter):
    vehicleConfigFile = open(vehicle_config_file, "r+")
    vehicleConfigData = vehicleConfigFile.read()
    vehicleConfigData2 = vehicleConfigData.replace("'", '"')
    vcdata = vehicleConfigData2.strip("[]")
    vcdata1 = json.loads(vcdata)
    ConfigType = vcdata1["type"]
    print("config type:", ConfigType)
    if ConfigType == parameter:
        print(ConfigType)
        ConfigPayload = vcdata1["payload"]
        print("payload", ConfigPayload)
        ConfigPayloadStr = str(ConfigPayload)
        print("payload type:", type(ConfigPayloadStr))
        decodedPayload = base64.b64decode(ConfigPayloadStr)
        print("decoded:", decodedPayload)
        string = str(decodedPayload)
        PayloadStrip = string.strip("b''")
        print("Payload stripp:", PayloadStrip)
        file = open(udr_signal_check, "w")
        file.write(PayloadStrip)
        file.close()
        open_json_file = open(udr_signal_check, "r")
        read_json_data = open_json_file.read()
        actual_file = json.loads(read_json_data)
        str_json = str(actual_file)
        if "VA-SIG-" in str_json:
            response = "SIGNALS UDR LABEL"
        else:
            response = "SIGNALS LEGACY LABEL"

    print("response:", response)
    return response
