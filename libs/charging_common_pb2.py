# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: charging_common.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x63harging_common.proto\x12\x0f\x63harging_common\"]\n\x13PredictedChargeData\x12\x15\n\rbattery_level\x18\x01 \x01(\r\x12\x17\n\x0fpredicted_range\x18\x02 \x01(\r\x12\x16\n\x0epredicted_time\x18\x03 \x01(\r*\x9e\x03\n\nEnumStatus\x12\x1b\n\x17\x45NUM_STATUS_UNSPECIFIED\x10\x00\x12\x12\n\x0e\x45NUM_STATUS_OK\x10\x01\x12\x1d\n\x19\x45NUM_STATUS_DATA_DEGRADED\x10\x02\x12\x1f\n\x1b\x45NUM_STATUS_DATA_UNRELIABLE\x10\x03\x12 \n\x1c\x45NUM_STATUS_DATA_UNAVAILABLE\x10\x04\x12+\n\'ENUM_STATUS_ERROR_INVALID_SERVICE_STATE\x10\x05\x12+\n\'ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE\x10\x06\x12/\n+ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION\x10\x07\x12)\n%ENUM_STATUS_ERROR_MISSING_INPUT_FIELD\x10\x08\x12)\n%ENUM_STATUS_ERROR_INVALID_INPUT_FIELD\x10\t\x12\x16\n\x12\x45NUM_STATUS_NOT_OK\x10\n\"\x04\x08\x0b\x10\x64*\xc6\x01\n\x1a\x45numChargeControlOperation\x12-\n)ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED\x10\x00\x12\'\n#ENUM_CHARGE_CONTROL_OPERATION_START\x10\x01\x12&\n\"ENUM_CHARGE_CONTROL_OPERATION_STOP\x10\x02\x12(\n$ENUM_CHARGE_CONTROL_OPERATION_REVERT\x10\x03*v\n\x11\x45numChargeContext\x12#\n\x1f\x45NUM_CHARGE_CONTEXT_UNSPECIFIED\x10\x00\x12\x1c\n\x18\x45NUM_CHARGE_CONTEXT_USER\x10\x01\x12\x1e\n\x1a\x45NUM_CHARGE_CONTEXT_SYSTEM\x10\x02*\xf3\x04\n\x0f\x45numChargeState\x12!\n\x1d\x45NUM_CHARGE_STATE_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_CHARGE_STATE_DEFAULT\x10\x01\x12\"\n\x1e\x45NUM_CHARGE_STATE_INITIALIZING\x10\x02\x12\'\n#ENUM_CHARGE_STATE_WAITING_TO_CHARGE\x10\x03\x12\x30\n,ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION\x10\x04\x12(\n$ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS\x10\x05\x12$\n ENUM_CHARGE_STATE_CHARGE_STOPPED\x10\x06\x12%\n!ENUM_CHARGE_STATE_CHARGE_COMPLETE\x10\x07\x12\"\n\x1e\x45NUM_CHARGE_STATE_CHARGE_ERROR\x10\x08\x12*\n&ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE\x10\t\x12+\n\'ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS\x10\n\x12(\n$ENUM_CHARGE_STATE_DISCHARGE_COMPLETE\x10\x0b\x12%\n!ENUM_CHARGE_STATE_DISCHARGE_ERROR\x10\x0c\x12/\n+ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS\x10\r\x12)\n%ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT\x10\x0e*\xb4\x04\n\x13\x45numChargeErrorMode\x12&\n\"ENUM_CHARGE_ERROR_MODE_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_CHARGE_ERROR_MODE_NO_ERROR\x10\x01\x12\'\n#ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR\x10\x02\x12/\n+ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR\x10\x03\x12*\n&ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR\x10\x04\x12,\n(ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE\x10\x05\x12.\n*ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE\x10\x06\x12\x30\n,ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED\x10\x07\x12\x32\n.ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED\x10\x08\x12/\n+ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING\x10\t\x12\'\n#ENUM_CHARGE_ERROR_MODE_GENERAL_INFO\x10\n\x12,\n(ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR\x10\x0b*\xc2\x01\n\x16\x45numChargingInletState\x12)\n%ENUM_CHARGING_INLET_STATE_UNSPECIFIED\x10\x00\x12\'\n#ENUM_CHARGING_INLET_STATE_UNPLUGGED\x10\x01\x12%\n!ENUM_CHARGING_INLET_STATE_PLUGGED\x10\x02\x12-\n)ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED\x10\x03*\xed\x01\n\x0e\x45numChargeType\x12 \n\x1c\x45NUM_CHARGE_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_CHARGE_TYPE_IMMEDIATE\x10\x01\x12#\n\x1f\x45NUM_CHARGE_TYPE_FIXED_SCHEDULE\x10\x02\x12\"\n\x1e\x45NUM_CHARGE_TYPE_SCHEDULE_PLUS\x10\x03\x12#\n\x1f\x45NUM_CHARGE_TYPE_SMART_SCHEDULE\x10\x04\x12+\n\'ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE\x10\x05*\xd9\x01\n\x12\x45numChargingMethod\x12$\n ENUM_CHARGING_METHOD_UNSPECIFIED\x10\x00\x12%\n!ENUM_CHARGING_METHOD_NOT_CHARGING\x10\x01\x12$\n ENUM_CHARGING_METHOD_AC_CHARGING\x10\x02\x12$\n ENUM_CHARGING_METHOD_DC_CHARGING\x10\x03\x12*\n&ENUM_CHARGING_METHOD_WIRELESS_CHARGING\x10\x04*i\n\x0e\x45numChargeDoor\x12 \n\x1c\x45NUM_CHARGE_DOOR_UNSPECIFIED\x10\x00\x12\x19\n\x15\x45NUM_CHARGE_DOOR_LEFT\x10\x01\x12\x1a\n\x16\x45NUM_CHARGE_DOOR_RIGHT\x10\x02*\xe2\x01\n\x17\x45numChargeDoorOperation\x12*\n&ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_CHARGE_DOOR_OPERATION_OPEN\x10\x01\x12$\n ENUM_CHARGE_DOOR_OPERATION_CLOSE\x10\x02\x12*\n&ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS\x10\x03\x12$\n ENUM_CHARGE_DOOR_OPERATION_ERROR\x10\x04*\xbc\x01\n\x18\x45numChargeCableOperation\x12+\n\'ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED\x10\x00\x12&\n\"ENUM_CHARGE_CABLE_OPERATION_UNLOCK\x10\x01\x12$\n ENUM_CHARGE_CABLE_OPERATION_LOCK\x10\x02\x12%\n!ENUM_CHARGE_CABLE_OPERATION_ERROR\x10\x03*\x8a\x01\n\x14\x45numPnCPaymentChoice\x12\'\n#ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED\x10\x00\x12$\n ENUM_PNC_PAYMENT_CHOICE_DISABLED\x10\x01\x12#\n\x1f\x45NUM_PNC_PAYMENT_CHOICE_ENABLED\x10\x02*\x8a\x01\n\x14\x45numPnCPaymentMethod\x12\'\n#ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED\x10\x00\x12%\n!ENUM_PNC_PAYMENT_METHOD_AUTOMATIC\x10\x01\x12\"\n\x1e\x45NUM_PNC_PAYMENT_METHOD_MANUAL\x10\x02*\x85\x01\n\rEnumPnCStatus\x12\x1f\n\x1b\x45NUM_PNC_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x45NUM_PNC_STATUS_ACTIVE\x10\x01\x12\x1c\n\x18\x45NUM_PNC_STATUS_INACTIVE\x10\x02\x12\x19\n\x15\x45NUM_PNC_STATUS_ERROR\x10\x03*\x9f\x01\n\x1a\x45numPnCFeatureAvailability\x12 \n\x1c\x45NUM_PNC_FEATURE_UNSPECIFIED\x10\x00\x12\"\n\x1e\x45NUM_PNC_FEATURE_NOT_SUPPORTED\x10\x01\x12\x1d\n\x19\x45NUM_PNC_FEATURE_DISABLED\x10\x02\x12\x1c\n\x18\x45NUM_PNC_FEATURE_ENABLED\x10\x03*v\n\x0f\x45numPnCVINShare\x12\"\n\x1e\x45NUM_PNC_VIN_SHARE_UNSPECIFIED\x10\x00\x12\x1f\n\x1b\x45NUM_PNC_VIN_SHARE_DISABLED\x10\x01\x12\x1e\n\x1a\x45NUM_PNC_VIN_SHARE_ENABLED\x10\x02*\xe2\x02\n\x1f\x45numPnCV2GRootCertificateStatus\x12-\n)ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED\x10\x00\x12+\n\'ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED\x10\x01\x12/\n+ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED\x10\x02\x12)\n%ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED\x10\x03\x12)\n%ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING\x10\x04\x12/\n+ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON\x10\x05\x12+\n\'ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED\x10\x06*\xe1\t\n\x12\x45numChrgTroubleSht\x12$\n ENUM_CHRG_TROUBLESHT_UNSPECIFIED\x10\x00\x12&\n\"ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE\x10\x01\x12)\n%ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT\x10\x02\x12.\n*ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE\x10\x03\x12-\n)ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED\x10\x04\x12\x30\n,ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL\x10\x05\x12\x32\n.ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE\x10\x06\x12-\n)ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN\x10\x07\x12(\n$ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN\x10\x08\x12\x30\n,ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING\x10\t\x12*\n&ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED\x10\n\x12)\n%ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED\x10\x0b\x12\'\n#ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING\x10\x0c\x12\'\n#ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING\x10\r\x12/\n+ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED\x10\x0e\x12-\n)ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE\x10\x0f\x12 \n\x1c\x45NUM_CHRG_TROUBLESHT_DC_800V\x10\x10\x12\x37\n3ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE\x10\x11\x12&\n\"ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP\x10\x12\x12/\n+ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP\x10\x13\x12\'\n#ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP\x10\x14\x12-\n)ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT\x10\x15\x12)\n%ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING\x10\x16\x12)\n%ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT\x10\x17\x12\x31\n-ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL\x10\x18\x12(\n$ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR\x10\x1b\x12.\n*ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR\x10\x1c\"\x04\x08\x19\x10\x19\"\x04\x08\x1a\x10\x1a*\xd8\x01\n\x1c\x45numPnCContractCertOperation\x12\x30\n,ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED\x10\x00\x12,\n(ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL\x10\x01\x12+\n\'ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE\x10\x02\x12+\n\'ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE\x10\x03*\x8b\x04\n\x15\x45numPnCContractStatus\x12(\n$ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED\x10\x00\x12.\n*ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED\x10\x01\x12\'\n#ENUM_PNC_CONTRACT_CERTIFICATE_VALID\x10\x02\x12)\n%ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED\x10\x03\x12/\n+ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON\x10\x04\x12(\n$ENUM_PNC_CONTRACT_REQUEST_PROCESSING\x10\x05\x12)\n%ENUM_PNC_CONTRACT_CERTIFICATE_INVALID\x10\x06\x12\x30\n,ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL\x10\x07\x12/\n+ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE\x10\x08\x12/\n+ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED\x10\t\x12*\n&ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK\x10\n*\x8d\x02\n\x0e\x45numHVPOStatus\x12 \n\x1c\x45NUM_HVPO_STATUS_UNSPECIFIED\x10\x00\x12)\n%ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST\x10\x01\x12*\n&ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS\x10\x02\x12\x30\n,ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED\x10\x03\x12*\n&ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE\x10\x04\x12$\n ENUM_HVPO_STATUS_DISCHARGE_ERROR\x10\x05*\x97\x01\n\x19\x45numChargeCableAutoUnlock\x12-\n)ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED\x10\x00\x12$\n ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON\x10\x01\x12%\n!ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF\x10\x02*\xa0\x01\n\x1d\x45numChargeCableApproachUnlock\x12*\n&ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED\x10\x00\x12(\n$ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON\x10\x01\x12)\n%ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF\x10\x02*\x90\x01\n\x18\x45numChargeLightPermanent\x12+\n\'ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED\x10\x00\x12\"\n\x1e\x45NUM_CHARGE_LIGHT_PERMANENT_ON\x10\x01\x12#\n\x1f\x45NUM_CHARGE_LIGHT_PERMANENT_OFF\x10\x02*\x8f\x01\n\x17\x45numChargeDoorAutoClose\x12+\n\'ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED\x10\x00\x12\"\n\x1e\x45NUM_CHARGE_DOOR_AUTO_CLOSE_ON\x10\x01\x12#\n\x1f\x45NUM_CHARGE_DOOR_AUTO_CLOSE_OFF\x10\x02*q\n\x10\x45numBatteryLevel\x12\"\n\x1e\x45NUM_BATTERY_LEVEL_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x45NUM_BATTERY_LEVEL_LOW\x10\x01\x12\x1d\n\x19\x45NUM_BATTERY_LEVEL_NORMAL\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'charging_common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_ENUMSTATUS']._serialized_start=138
  _globals['_ENUMSTATUS']._serialized_end=552
  _globals['_ENUMCHARGECONTROLOPERATION']._serialized_start=555
  _globals['_ENUMCHARGECONTROLOPERATION']._serialized_end=753
  _globals['_ENUMCHARGECONTEXT']._serialized_start=755
  _globals['_ENUMCHARGECONTEXT']._serialized_end=873
  _globals['_ENUMCHARGESTATE']._serialized_start=876
  _globals['_ENUMCHARGESTATE']._serialized_end=1503
  _globals['_ENUMCHARGEERRORMODE']._serialized_start=1506
  _globals['_ENUMCHARGEERRORMODE']._serialized_end=2070
  _globals['_ENUMCHARGINGINLETSTATE']._serialized_start=2073
  _globals['_ENUMCHARGINGINLETSTATE']._serialized_end=2267
  _globals['_ENUMCHARGETYPE']._serialized_start=2270
  _globals['_ENUMCHARGETYPE']._serialized_end=2507
  _globals['_ENUMCHARGINGMETHOD']._serialized_start=2510
  _globals['_ENUMCHARGINGMETHOD']._serialized_end=2727
  _globals['_ENUMCHARGEDOOR']._serialized_start=2729
  _globals['_ENUMCHARGEDOOR']._serialized_end=2834
  _globals['_ENUMCHARGEDOOROPERATION']._serialized_start=2837
  _globals['_ENUMCHARGEDOOROPERATION']._serialized_end=3063
  _globals['_ENUMCHARGECABLEOPERATION']._serialized_start=3066
  _globals['_ENUMCHARGECABLEOPERATION']._serialized_end=3254
  _globals['_ENUMPNCPAYMENTCHOICE']._serialized_start=3257
  _globals['_ENUMPNCPAYMENTCHOICE']._serialized_end=3395
  _globals['_ENUMPNCPAYMENTMETHOD']._serialized_start=3398
  _globals['_ENUMPNCPAYMENTMETHOD']._serialized_end=3536
  _globals['_ENUMPNCSTATUS']._serialized_start=3539
  _globals['_ENUMPNCSTATUS']._serialized_end=3672
  _globals['_ENUMPNCFEATUREAVAILABILITY']._serialized_start=3675
  _globals['_ENUMPNCFEATUREAVAILABILITY']._serialized_end=3834
  _globals['_ENUMPNCVINSHARE']._serialized_start=3836
  _globals['_ENUMPNCVINSHARE']._serialized_end=3954
  _globals['_ENUMPNCV2GROOTCERTIFICATESTATUS']._serialized_start=3957
  _globals['_ENUMPNCV2GROOTCERTIFICATESTATUS']._serialized_end=4311
  _globals['_ENUMCHRGTROUBLESHT']._serialized_start=4314
  _globals['_ENUMCHRGTROUBLESHT']._serialized_end=5563
  _globals['_ENUMPNCCONTRACTCERTOPERATION']._serialized_start=5566
  _globals['_ENUMPNCCONTRACTCERTOPERATION']._serialized_end=5782
  _globals['_ENUMPNCCONTRACTSTATUS']._serialized_start=5785
  _globals['_ENUMPNCCONTRACTSTATUS']._serialized_end=6308
  _globals['_ENUMHVPOSTATUS']._serialized_start=6311
  _globals['_ENUMHVPOSTATUS']._serialized_end=6580
  _globals['_ENUMCHARGECABLEAUTOUNLOCK']._serialized_start=6583
  _globals['_ENUMCHARGECABLEAUTOUNLOCK']._serialized_end=6734
  _globals['_ENUMCHARGECABLEAPPROACHUNLOCK']._serialized_start=6737
  _globals['_ENUMCHARGECABLEAPPROACHUNLOCK']._serialized_end=6897
  _globals['_ENUMCHARGELIGHTPERMANENT']._serialized_start=6900
  _globals['_ENUMCHARGELIGHTPERMANENT']._serialized_end=7044
  _globals['_ENUMCHARGEDOORAUTOCLOSE']._serialized_start=7047
  _globals['_ENUMCHARGEDOORAUTOCLOSE']._serialized_end=7190
  _globals['_ENUMBATTERYLEVEL']._serialized_start=7192
  _globals['_ENUMBATTERYLEVEL']._serialized_end=7305
  _globals['_PREDICTEDCHARGEDATA']._serialized_start=42
  _globals['_PREDICTEDCHARGEDATA']._serialized_end=135
# @@protoc_insertion_point(module_scope)
