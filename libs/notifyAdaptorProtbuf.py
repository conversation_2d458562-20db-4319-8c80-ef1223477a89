import ngtp_adapter_service_pb2 as ngtp
import someip_adapter_service_pb2 as someIpProto
import charging_common_pb2 as charging_common
import envelope_pb2 as envelopeProto
import time
from robot.api.deco import keyword
import hashlib

notify_charge_state_message = "someip_adpt_service.NotifyChargeState"


@keyword("Create serialised protbuf message for notify adaptor NotifyChargeSessionAttributes")
def notify_charge_session_attributes(
    range_added,
    energy_added,
    charging_duration,
):

    testdata = someIpProto.NotifyChargeSessionAttributes()
    service_message = "someip_adpt_service.NotifyChargeSessionAttributes"

    testdata.status = 1
    testdata.range_added = int(range_added)
    testdata.energy_added = int(energy_added)
    testdata.charging_duration = int(charging_duration)

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyChargeSettings")
def notify_charge_settings(
    charge_type,
    off_peak_start_time_hours,
    off_peak_start_time_minutes,
    off_peak_stop_time_hours,
    off_peak_stop_time_minutes,
    max_battery_soc,
    actual_ac_charge_rate_limit,
):

    testdata = someIpProto.NotifyChargeSettings()
    service_message = "someip_adpt_service.NotifyChargeSettings"

    testdata.status = 1
    testdata.charge_type = charge_type
    testdata.off_peak_start_time_hours = int(off_peak_start_time_hours)
    testdata.off_peak_start_time_minutes = int(off_peak_start_time_minutes)
    testdata.off_peak_stop_time_hours = int(off_peak_stop_time_hours)
    testdata.off_peak_stop_time_minutes = int(off_peak_stop_time_minutes)
    testdata.max_battery_soc = int(max_battery_soc)
    testdata.actual_ac_charge_rate_limit = int(actual_ac_charge_rate_limit)

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyChargeState")
def notify_charge_state(
    charging_status,
    charge_error_state,
    charging_inlet_state,
    charge_cable_lock_status,
    charge_troubleshooter,
):

    testdata = someIpProto.NotifyChargeState()
    service_message = notify_charge_state_message

    testdata.status = 1
    testdata.charging_status = charging_status
    testdata.charge_error_state = charge_error_state
    testdata.charging_inlet_state = charging_inlet_state
    testdata.charge_cable_lock_status = charge_cable_lock_status
    testdata.charge_troubleshooter = charge_troubleshooter
    # testdata.tgt_soc_reachable_by_departure = tgt_soc_reachable_by_departure

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protobuf message for notify adaptor NotifyChargeState with charging status and troubleshooter")
def notify_charge_state_with_charging_status_and_troubleshooter(
    charging_status,
    charge_troubleshooter
):

    testdata = someIpProto.NotifyChargeState()
    service_message = notify_charge_state_message

    testdata.status = 1
    testdata.charging_status = charging_status
    testdata.charge_troubleshooter = charge_troubleshooter

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protobuf message for notify adaptor NotifyChargeState with charging status")
def notify_charge_state_with_charging_status(
    charging_status
):

    testdata = someIpProto.NotifyChargeState()
    service_message = notify_charge_state_message

    testdata.status = 1
    testdata.charging_status = charging_status

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protobuf message for notify adaptor NotifyChargeInProgressData for Charging Error Notifications")
def notify_charge_in_progress_for_charging_error_notifications(
    charging_method
):

    testdata = someIpProto.NotifyChargeInProgressData()
    service_message = "someip_adpt_service.NotifyChargeInProgressData"

    testdata.status = 1
    testdata.charging_method = charging_method

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyVehicleCabinAirCleanState")
def notify_vehicle_cabin_air_clean_state(
    cabin_air_cleaning_status,
    cabin_pm2_5_level,
    cabin_pm2_5_band,
    external_pm2_5_level,
    external_pm2_5_band,
    cabin_air_clean_cycles_remaining,
    adaptor="ngtp",
):
    """ """

    if adaptor == "ngtp":
        testdata = ngtp.NotifyVehicleCabinAirCleanState()
        service_message = "ngtp_adpt_service.NotifyVehicleCabinAirCleanState"
    elif adaptor == "someip":
        testdata = someIpProto.NotifyVehicleCabinAirCleanState()
        service_message = "someip_adpt_service.NotifyVehicleCabinAirCleanState"
    else:
        print("Invalid notify adaptor specified, should be ngtp or someip, got: ", adaptor)

    testdata.status = 1
    testdata.cabin_air_cleaning_status = cabin_air_cleaning_status
    testdata.cabin_pm2_5_level = int(cabin_pm2_5_level)
    testdata.cabin_pm2_5_band = int(cabin_pm2_5_band)
    testdata.external_pm2_5_level = int(external_pm2_5_level)
    testdata.external_pm2_5_band = int(external_pm2_5_band)
    testdata.cabin_air_clean_cycles_remaining = int(cabin_air_clean_cycles_remaining)

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyVehiclePreconditionState")
def notify_vehicle_precondition_state(time_remaining, precondition_status, precondition_mode, adaptor=ngtp):
    """
    precondition_status: 0 to 13
    precondition_mode:
            ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED = 0;
            ENUM_PRECONDITION_CURRENT_MODE_INACTIVE = 1;
            ENUM_PRECONDITION_CURRENT_MODE_IMMEDIATE = 2;
            ENUM_PRECONDITION_CURRENT_MODE_TIMED = 3;
    """

    testdata = ngtp.NotifyVehiclePreconditionState()

    if adaptor == "ngtp":
        testdata = ngtp.NotifyVehiclePreconditionState()
        service_message = "ngtp_adpt_service.NotifyVehiclePreconditionState"
    elif adaptor == "someip":
        testdata = someIpProto.NotifyVehiclePreconditionState()
        service_message = "someip_adpt_service.NotifyVehiclePreconditionState"
    else:
        print("Invalid notify adaptor specified, should be ngtp or someip, got: ", adaptor)

    testdata.status = 1
    testdata.time_remaining = int(time_remaining)
    testdata.precondition_status = int(precondition_status)
    testdata.precondition_mode = int(precondition_mode)

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyPredictedCharge")
def notify_predicted_charge(next_charge_start_time_seconds, time_to_tgt_soc, tgt_soc_ev_range_km, status="ENUM_STATUS_OK"):

    testdata = someIpProto.NotifyPredictedCharge()
    service_message = "someip_adpt_service.NotifyPredictedCharge"

    # Create PredictedChargeData
    PredictedChargeData = charging_common.PredictedChargeData()
    PredictedChargeData.battery_level = 30
    PredictedChargeData.predicted_range = 123
    PredictedChargeData.predicted_time = 234

    testdata.status = status
    testdata.next_charge_start_time_seconds = int(next_charge_start_time_seconds)
    testdata.predicted_charge_data.append(PredictedChargeData)
    testdata.time_to_tgt_soc = int(time_to_tgt_soc)
    testdata.tgt_soc_ev_range_km = int(tgt_soc_ev_range_km)

    print(testdata)

    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyVehicleAlarmState")
def notify_vehicle_alarm_state(alarm_status, adaptor="ngtp", status="ENUM_STATUS_OK"):
    """
    alarm_status:
    0 - UNSPECIFIED
    1 - Armed
    2 - Disarmed
    3 - triggered
    4 - fault
    """

    if adaptor == "ngtp":
        testdata = ngtp.NotifyVehicleAlarmState()
        service_message = "ngtp_adpt_service.NotifyVehicleAlarmState"
    elif adaptor == "someip":
        testdata = someIpProto.NotifyVehicleAlarmState()
        service_message = "someip_adpt_service.NotifyVehicleAlarmState"
    else:
        print("Invalid notify adaptor specified, should be ngtp or someip, got: ", adaptor)

    testdata.status = status
    testdata.alarm_status = int(alarm_status)

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor notifySeatClimateStatus")
def notify_seats_climate_state(adaptor, seat, status=1, seat_area=None, seat_state=None, seat_climate_intensity=None):
    """ """
    if adaptor == "ngtp":
        testdata = ngtp.NotifyVehicleAlarmState()
        service_message = "ngtp_adpt_service.NotifyVehicleAlarmState"
    elif adaptor == "someip":
        testdata = someIpProto.NotifySeatClimateStatus()
        service_message = "someip_adpt_service.NotifySeatClimateStatus"
    else:
        print("Invalid notify adaptor specified, should be ngtp or someip, got: ", adaptor)

    print("Testdata::")
    print(testdata)
    testdata.status = 1

    # Create a new SeatClimateZoneState
    seat_climate_zone = someIpProto.SeatClimateZoneState()
    seat_climate_zone.seat_selection = seat

    # Create a SeatClimateOperation
    seat_operation = someIpProto.SeatClimateOperation()
    seat_operation.seat_area = seat_area
    seat_operation.seat_state = seat_state
    seat_operation.seat_climate_intensity = seat_climate_intensity

    # Create a SeatClimateIntensity
    seat_climate_zone.seat_climate_intensity = seat_climate_intensity

    # Add the operation to the zone state
    seat_climate_zone.seat_operation.append(seat_operation)
    # seat_climate_zone.seat_climate_intensity.append(seat_climate_zone)

    # Add the zone state to the notification message
    testdata.seat_climate_zone.append(seat_climate_zone)

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)
    print()

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for notify adaptor NotifyApertureLockState")
def notify_aperture_lock_state(lock_status, adaptor="ngtp", status="ENUM_STATUS_OK", omit_central_lock_status=False):
    """
    lockstatus:
    0 - UNSPECIFIED
    1 - UNLOCKED
    2 - SINGLE_LOCKED
    3 - DOUBLE_LOCKED
    """

    if adaptor == "ngtp":
        testdata = ngtp.NotifyApertureLockState()
        service_message = "ngtp_adpt_service.NotifyApertureLockState"
    elif adaptor == "someip":
        testdata = someIpProto.NotifyApertureLockState()
        service_message = "someip_adpt_service.NotifyApertureLockState"
    else:
        print("Invalid notify adaptor specified, should be ngtp or someip, got: ", adaptor)

    lock_status_int = int(lock_status)
    if lock_status_int == 3:
        central_lock_status_int = 4
    else:
        central_lock_status_int = lock_status_int

    testdata.status = status
    testdata.driver_door_status = lock_status_int
    testdata.passenger_door_status = lock_status_int
    testdata.reardriver_door_status = lock_status_int
    testdata.rearpassenger_door_status = lock_status_int
    testdata.tailgate_status = lock_status_int
    testdata.bonnet_status = lock_status_int
    if not omit_central_lock_status:
        testdata.central_lock_status = central_lock_status_int

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)

    # pack it in the envelope
    protobuf_envelope, now = envelope(service_message, protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for someip adaptor NotifyBatteryCurrentStateOfCharge")
def notify_battery_current_state_of_charge(charge_level, status="ENUM_STATUS_OK"):
    testdata = someIpProto.NotifyBatteryCurrentStateOfCharge()
    int_charge_level = int(charge_level)
    testdata.status = status
    testdata.current_battery_soc = int_charge_level

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)
    # pack it in the envelope
    protobuf_envelope, now = envelope("someip_adpt_service.NotifyBatteryCurrentStateOfCharge", protobuf_testdata)

    return (protobuf_envelope, now)


@keyword("Create serialised protbuf message for someip adaptor NotifyHeatedSteeringWheelStatus")
def notify_heated_steering_wheel_status(hsw_temperature_level, hsw_control_state, status="ENUM_STATUS_OK"):
    testdata = someIpProto.NotifyHeatedSteeringWheelStatus()
    testdata.hsw_temperature_level = hsw_temperature_level
    testdata.status = status
    testdata.hsw_control_state = hsw_control_state

    print(testdata)
    protobuf_testdata = testdata.SerializeToString()
    print(protobuf_testdata)
    # pack it in the envelope
    protobuf_envelope, now = envelope("someip_adpt_service.NotifyHeatedSteeringWheelStatus", protobuf_testdata)

    return (protobuf_envelope, now)


def envelope(service_message, protobuf_testdata):

    now = int(time.time() * 1000)
    print("TimeStamp::", now)
    envel = envelopeProto.GatewayResponseMessage()

    result = hashlib.md5(service_message.encode())
    print("The hexadecimal equivalent of hash is : ", end="")

    envel.response_message.hash = result.hexdigest().encode(encoding="utf-8")
    envel.response_message.message_payload = protobuf_testdata
    envel.timestamp_ms = now
    print("\n")
    print(envel)
    protobuf_envelope = envel.SerializeToString()

    return (protobuf_envelope, now)
