import RawMessageType_3 as vcdp
import time
from google.protobuf.json_format import MessageToJson
from robot.api.deco import keyword, not_keyword

"""
To create serialised protobuf messages as per the data offboarded from the vehilce
"""


@not_keyword
def add_signals_to_message(
    query_id, signals_and_values, calc_end_time_ms, calc_duration_ms, calc_avg, calc_min_data_float, calc_max_data_float, calc_std_dev, calc_count
):
    if query_id is None:
        print(f"query_id was None, signal(s) will not be added to message: {signals_and_values}")
        return (-1, -1)
    now = int(time.time() * 1000)
    testdata = vcdp.ResponseData()
    testdata.query_id = query_id
    testdata.event_timestamp_ms = now

    for signal, value in signals_and_values.items():
        try:
            value = float(value)
        except ValueError:
            print(str(signal) + " must be float got::" + str(value))
            return -1, -1

        data = testdata.data.add()
        data.data_id = signal

        datasamples = data.samples.add()
        datasamples.timestamp_ms = now
        datasamples.sample.data_float = float(value)

        if calc_duration_ms != 0:
            data.calc.end_time_ms = int(calc_end_time_ms)
            data.calc.duration_ms = int(calc_duration_ms)
            data.calc.avg = float(calc_avg)
            data.calc.min.data_float = float(calc_min_data_float)
            data.calc.max.data_float = float(calc_max_data_float)
            data.calc.std_dev = float(calc_std_dev)
            data.calc.count = int(calc_count)

    return testdata, now


@keyword("Create serialised protbuf message for float signal")
def create_serialised_protbuf_message_for_float_signal(
    query_id,
    signal,
    value,
    calc_end_time_ms=0,
    calc_duration_ms=0,
    calc_avg=0,
    calc_min_data_float=0,
    calc_max_data_float=0,
    calc_std_dev=0,
    calc_count=0,
):
    """
    Creates a serialised protobuf message for a vehicle float signal.
    Calculation is optional
    Example:

    | Create serialised protbuf message for float signal | Q9102010 | S00080 | 3 |
    | Create serialised protbuf message for float signal | Q9901009 | S00044 | 32 | 1680520130987 | 36432 | 32.769226 | 0 | 41 | 10.433551 | 7 |

    """
    testdata, now = add_signals_to_message(
        query_id, {signal: value}, calc_end_time_ms, calc_duration_ms, calc_avg, calc_min_data_float, calc_max_data_float, calc_std_dev, calc_count
    )
    if testdata == -1:
        return (-1, -1, -1)
    print(testdata)
    protobuf_string = testdata.SerializeToString()
    print(protobuf_string)
    return (0, protobuf_string, now)


@keyword("Create serialised protbuf message for multiple float signals")
def create_serialised_protbuf_message_for_multiple_float_signals(
    query_id,
    signals_and_values,
    calc_end_time_ms=0,
    calc_duration_ms=0,
    calc_avg=0,
    calc_min_data_float=0,
    calc_max_data_float=0,
    calc_std_dev=0,
    calc_count=0,
):
    """
    Creates a serialised protobuf message for vehicle float signals with the same queryId.
    Calculation is optional
    Example:

    &{signalsAndValues} =	Create Dictionary	S00080=3	S00044=32
    | Create serialised protbuf message for multiple float signals | Q9102010 | ${signalsAndValues} |
    | Create serialised protbuf message for multiple float signals | Q9901009 | ${signalsAndValues} | 1680520130987 | 36432 | 32.769226 | 0 | 41 | 10.433551 | 7 | # noqa: E501

    """
    testdata, now = add_signals_to_message(
        query_id, signals_and_values, calc_end_time_ms, calc_duration_ms, calc_avg, calc_min_data_float, calc_max_data_float, calc_std_dev, calc_count
    )
    if testdata == -1:
        return (-1, -1, -1)
    print(testdata)
    protobuf_string = testdata.SerializeToString()
    print(protobuf_string)
    print("PROTOBUF_TIMESTAMP: ", str(now))
    return (0, protobuf_string, now)


keyword("Deserialize Vehicle Protobuf")


def deserialise(protobuf):
    # deserialize it to check
    deser_test_data = vcdp.ResponseData()
    deser_test_data.ParseFromString(protobuf)
    print(deser_test_data)
    json_obj = MessageToJson(deser_test_data)
    return json_obj


@not_keyword
def read_from_file():
    testdata = vcdp.ResponseData()
    with open("./serializedFile", "rb") as fd:
        testdata.ParseFromString(fd.read())
        print(testdata)
