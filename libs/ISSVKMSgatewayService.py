#!/usr/bin python3

import base64
import hashlib
import requests
import json
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig
from commonUtils import get_keycloak_token
import random

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
contentType = "application/json"
authType = "Bearer "

systemVarFile = "../variables/system.yml"
noResponseBody = "No response body returned"


vin = "TESTVIN" + str(random.randint(1000000000, 9999999999))
digest = hashlib.sha256(vin.encode("utf-8")).digest()
b64_encoded = base64.b64encode(digest).decode("utf-8")
hvin = b64_encoded.replace("+", "-").replace("/", "_").rstrip("=")


@not_keyword
def get_url(url):
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, url)


@not_keyword
def issvkms_url(user, pw):
    token = get_keycloak_token(user, pw)
    url = get_url("ISSVKMS-gateway")
    ISSVKMSnewvinurl = url + "/api/vcdp/vkms/dslEvents"
    header = {"accept": "*/*", "Content-Type": contentType, "Authorization": authType + token}
    print(ISSVKMSnewvinurl)
    print(header)
    return ISSVKMSnewvinurl, header


@keyword("create new eva25 vin via ISSVKMS gateway")
def create_eva25vin_iss_vkms(vehicleConfig, user, pw, assignedvin=vin, assignedhvin=hvin):

    ISSVKMSnewvinurl, header = issvkms_url(user, pw)

    if assignedvin == vin and assignedhvin == hvin:
        print("Default assigned Vin: ", vin)
        print("Default assigned Hvin: ", hvin)
    else:
        print("Test assigned Vin: ", assignedvin)
        print("Tes assigned Hvin: ", assignedhvin)
    openJsonData = open(vehicleConfig, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["eventPayload"]["metadata"]["vehicleInfo"]["vin"] = assignedvin
    jsonRequestBody["eventPayload"]["metadata"]["vehicleInfo"]["hvin"] = assignedhvin

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)
    # print(payload)

    response = requests.request("POST", ISSVKMSnewvinurl, headers=header, data=payload)

    print(str(response.text))
    print(str(response.status_code))
    if response.status_code != 403:
        responseBody = response.text
        statusCode = response.status_code
    else:
        responseBody = noResponseBody
        statusCode = response.status_code

    return responseBody, statusCode, assignedvin


@keyword("Attempt to create new vin via ISSVKMS gateway with invalid schema")
def attempt_create_eva25vin_iss_vkms(vehicleConfig, user, pw, parameter):

    ISSVKMSnewvinurl, header = issvkms_url(user, pw)
    openJsonData = open(vehicleConfig, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)
    jsonRequestBody["eventPayload"]["metadata"]["vehicleInfo"]["vin"] = vin
    jsonRequestBody["eventPayload"]["metadata"]["vehicleInfo"]["hvin"] = hvin
    if parameter == "eventPayload":
        del jsonRequestBody["eventPayload"]
    elif parameter == "actionId":
        del jsonRequestBody["eventPayload"]["actionId"]
    elif parameter == "metadata":
        del jsonRequestBody["eventPayload"]["metadata"]
    elif parameter == "outcome":
        del jsonRequestBody["eventPayload"]["outcome"]
    elif parameter == "ecuname":
        del jsonRequestBody["eventPayload"]["metadata"]["ecus"][0]["name"]
    elif parameter == "ecukeyLabel":
        del jsonRequestBody["eventPayload"]["metadata"]["ecus"][0]["symKeys"][0]["keyLabel"]
    elif parameter == "dlcInfoID":
        del jsonRequestBody["eventPayload"]["actionInfo"]["dlcInfo"]["id"]
    elif parameter == "keyWrapkeyLabel":
        del jsonRequestBody["eventPayload"]["metadata"]["ecus"][0]["symKeys"][0]["keyWrapInfo"]["keyLabel"]
    elif parameter == "productID":
        del jsonRequestBody["eventPayload"]["actionInfo"]["productID"]
    elif parameter == "ecuasymkeyType":
        del jsonRequestBody["eventPayload"]["metadata"]["ecus"][0]["asymKeys"][0]["keyType"]
    elif parameter == "ecusymgenerationNo":
        del jsonRequestBody["eventPayload"]["metadata"]["ecus"][1]["symKeys"][0]["generationNo"]
    elif parameter == "certName":
        del jsonRequestBody["eventPayload"]["metadata"]["ecus"][0]["asymKeys"][0]["certName"]

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)
    print(payload)

    response = requests.request("POST", ISSVKMSnewvinurl, headers=header, data=payload)

    print(str(response.text))
    print(str(response.status_code))
    if response.status_code != 403:
        responseBody = response.text
        status_code = response.status_code
    else:
        responseBody = noResponseBody
        status_code = response.status_code

    return responseBody, status_code, vin
