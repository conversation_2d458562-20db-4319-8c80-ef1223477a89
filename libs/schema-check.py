import json
from jsonschema import validate, exceptions
from robot.api.deco import keyword, not_keyword

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"

test_response = [
    {
        "id": "6171725e6711c4063def6d05",
        "name": "e2eTest1-RS",
        "configurationType": "RULESET",
    },
    {
        "id": "617172b56711c4063def6d06",
        "name": "e2eTest1-QF",
        "configurationType": "QUERY",
    },
    {
        "id": "617173bc6711c4063def6d07",
        "name": "e2eTest1-PC",
        "configurationType": "CHANNEL",
    },
]


@not_keyword
def get_schema(schema_file):
    """This function loads the given schema available"""
    with open(schema_file, "r") as file:
        text_file = file.read()
        a = json.loads(text_file)
    return a


@keyword("Validate Json Response Schema")
def validate_json_response_schema(payload, schema_defintion):
    """
    Validate a json response against a schema definition file.


    Examples:
    | Validate Json Response Schema|  ${msg}  | ../jsonschema/diag-pubsub-schema.json |
    """
    """REF: https://json-schema.org/ """

    print(payload)
    print("Payload end")
    execute_api_schema = get_schema(schema_defintion)
    if not isinstance(payload, (dict, list)):
        json_object = json.loads(payload)
    else:
        json_object = payload

    try:
        validate(instance=json_object, schema=execute_api_schema)
    except exceptions.ValidationError as err:
        print(err)
        return False

    return True
