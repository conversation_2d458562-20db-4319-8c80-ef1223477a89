import forgeRock
import time
import requests
import json
import os
import commandAndControlLib
import commonUtils
from robot.api.deco import not_keyword
from pyaml_env import parse_config, BaseConfig


system_var_file = "../variables/system.yml"
authType = "Bearer "
request_sent = "Request sent: "
CONTENT_TYPE = "application/json"


@not_keyword
def post_heated_surface_command_oab(unique_id, command, temperature, CONTENT_TYPE, AUTH_TYPE):
    system_vars = BaseConfig(parse_config(system_var_file))
    url = getattr(system_vars, "oneapp-backend-url-2") + "/vehicles/" + unique_id + "/heatedsurface/seats"
    record = "../json-request-data/c2/hSeatsEmpty.json"
    payload_to_post = commandAndControlLib.payload_builder(record, command, temperature)

    payloadToInsert = json.loads(payload_to_post)

    response = commonUtils.send_request(url, payloadToInsert, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


@not_keyword
def command_one_app_back_end(unique_id, command, vehicleArchitecture="EVA2"):
    system_vars = BaseConfig(parse_config(system_var_file))
    url = getattr(system_vars, "oneapp-backend-url-2") + "/vehicles/" + unique_id + "/commands"
    match command:
        case "lock":
            path = "/door"
            payload = "../json-request-data/one-app-backend-commands/door-lock.json"
            forge_rock_username = os.environ.get("RDLU_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("RDLU_FR_PASSWORD_PREPROD")
        case "unlock":
            path = "/door"
            payload = "../json-request-data/one-app-backend-commands/door-unlock.json"
            forge_rock_username = os.environ.get("RDLU_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("RDLU_FR_PASSWORD_PREPROD")
        case "evStart":
            path = "/evcharge"
            payload = "../json-request-data/one-app-backend-commands/evStartPayload.json"
            forge_rock_username = os.environ.get("EVSS_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("EVSS_FR_PASSWORD_PREPROD")
        case "evStop":
            path = "/evcharge"
            payload = "../json-request-data/one-app-backend-commands/evStopPayload.json"
            forge_rock_username = os.environ.get("EVSS_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("EVSS_FR_PASSWORD_PREPROD")
        case "hvacStart":
            path = "/climate"
            payload = "../json-request-data/one-app-backend-commands/climate-start.json"
            forge_rock_username = os.environ.get("HVAC_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("HVAC_FR_PASSWORD_PREPROD")
        case "hvacStop":
            path = "/climate"
            payload = "../json-request-data/one-app-backend-commands/climate-stop.json"
            forge_rock_username = os.environ.get("HVAC_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("HVAC_FR_PASSWORD_PREPROD")
        case "cacStart":
            path = "/cabinairclean"
            payload = "../json-request-data/one-app-backend-commands/cacStartPayload.json"
            forge_rock_username = os.environ.get("CAC_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("CAC_FR_PASSWORD_PREPROD")
        case "cacStop":
            path = "/cabinairclean"
            payload = "../json-request-data/one-app-backend-commands/cacStopPayload.json"
            forge_rock_username = os.environ.get("CAC_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("CAC_FR_PASSWORD_PREPROD")
        case "msoc":
            path = "/evcharge/config/target"
            payload = "../json-request-data/one-app-backend-commands/msoPayload.json"
            forge_rock_username = os.environ.get("MSOC_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("MSOC_FR_PASSWORD_PREPROD")
        case "ar":
            path = "/alarm"
            payload = "../json-request-data/one-app-backend-commands/arPayload.json"
            forge_rock_username = os.environ.get("AR_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("AR_FR_PASSWORD_PREPROD")
        case "bnf":
            path = "/beepflash"
            payload = "../json-request-data/one-app-backend-commands/beep-flash.json"
            forge_rock_username = os.environ.get("BNF_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("BNF_FR_PASSWORD_PREPROD")
        case "hsSwOff":
            path = "/heatedsurface/steeringwheel"
            payload = "../json-request-data/one-app-backend-commands/hsSwOff.json"
            forge_rock_username = os.environ.get("HSSW_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("HSSW_FR_PASSWORD_PREPROD")
        case "hsSwOn":
            path = "/heatedsurface/steeringwheel"
            payload = "../json-request-data/one-app-backend-commands/hsSwOn.json"
            forge_rock_username = os.environ.get("HSSW_FR_EMAIL_PREPROD")
            forge_rock_password = os.environ.get("HSSW_FR_PASSWORD_PREPROD")
        case "cbaLeft":
            payload = "../json-request-data/one-app-backend-commands/cbaLeftPayload.json"
        case "cbaRight":
            payload = "../json-request-data/one-app-backend-commands/cbaRightPayload.json"
        case "ccuLock":
            payload = "../json-request-data/one-app-backend-commands/ccuLockPayload.json"
        case "ccuUnlock":
            payload = "../json-request-data/one-app-backend-commands/ccuUnlockPayload.json"

    # token = forgeRock.get_token(forge_rock_username, forge_rock_password)
    if vehicleArchitecture == "EVA25":
        forge_rock_username = os.environ.get("EVA25_FR_EMAIL_PREPROD")
        forge_rock_password = os.environ.get("EVA25_FR_PASSWORD_PREPROD")
        token = forgeRock.get_token(forge_rock_username, forge_rock_password)
    else:
        token = forgeRock.get_token(forge_rock_username, forge_rock_password)
    headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Authorization": authType + token, "Attestation": os.environ["ApproovToken"]}

    json_file = open(payload, "r")
    read = json_file.read()
    req_body = json.loads(read)

    urlPath = url + path

    print("url:")
    print(urlPath)
    print(req_body)
    response = requests.request("POST", urlPath, headers=headers, json=req_body)
    print(request_sent, str(int(time.time() * 1000)))
    print(response.status_code, response.text, response.headers)
    return (response.status_code, response.text, response.headers)
