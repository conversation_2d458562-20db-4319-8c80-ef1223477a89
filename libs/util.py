import random
import json
import csv
from pyaml_env import parse_config, BaseConfig


def get_random_dp(file_list):
    return random.choice(file_list)


def convert_csv_to_json(config):
    config_file_path = f"../variables/{config}.yml"
    config_vars = BaseConfig(parse_config(config_file_path))
    file_list = getattr(config_vars, "file_list")
    csv_file = get_random_dp(file_list=file_list)
    csv_file_path = f"path/{csv_file}.csv"
    keys = getattr(config_vars, "keys")
    list_items = getattr(config_vars, "list_items")
    dict_items = getattr(config_vars, "dict_items")
    json_key = getattr(config_vars, "json_key")

    with open(csv_file_path, "r") as csvfile:
        csv_reader = csv.DictReader(csvfile)
        lines = list(csv_reader)
        random_line = random.choice(lines)

    value = {k: v for k, v in random_line.items() if k not in keys}

    if list_items:
        for col in list_items:
            if col in value:
                value[col] = [value[col]]

    if dict_items:
        for col in dict_items:
            if col in value:
                value[col] = value[col].replace("'", '"')
                value[col] = json.loads(value[col])

    json_data = json.dumps({json_key: value}, indent=2)

    return json_data
