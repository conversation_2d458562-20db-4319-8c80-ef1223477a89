import base64
import json


def base64_decode(data):
    """Decodes Base64 URL-safe encoded JWT parts, handling padding issues."""
    padding = len(data) % 4
    if padding:
        data += "=" * (4 - padding)

    return base64.urlsafe_b64decode(data)


def decode_jwt_base64(jwt_token):
    """
    Extracts and decodes the Base64 JWT components.

    :param jwt_token: The JWT string (Header.Payload.Signature).
    :return: Decoded header, payload, and raw signature.
    """
    try:
        header_encoded, payload_encoded, signature_encoded = jwt_token.split(".")

        header = json.loads(base64_decode(header_encoded).decode("utf-8"))
        payload = json.loads(base64_decode(payload_encoded).decode("utf-8"))
        signature = base64_decode(signature_encoded)  # Keep as raw binary data

        return {"Header": header, "Payload": payload, "Signature (Raw Binary)": signature.hex()}  # Convert binary to hex for readability
    except Exception as e:
        return f"Error decoding JWT: {str(e)}"
