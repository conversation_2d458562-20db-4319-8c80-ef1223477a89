# flake8: noqa
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: RawMessageType-3.0.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x18RawMessageType-3.0.proto\x12\x12jlr.queue.protobuf"\x94\x06\n\x0cResponseData\x12\x10\n\x08query_id\x18\x01 \x01(\t\x12\x1a\n\x12\x65vent_timestamp_ms\x18\x02 \x01(\x04\x12\x33\n\x04\x64\x61ta\x18\x03 \x03(\x0b\x32%.jlr.queue.protobuf.ResponseData.Data\x12;\n\x08globalRT\x18\x04 \x01(\x0b\x32).jlr.queue.protobuf.ResponseData.GlobalRT\x1aY\n\x08\x64\x61taType\x12\x13\n\tdata_uint\x18\x01 \x01(\x04H\x00\x12\x14\n\ndata_float\x18\x02 \x01(\x02H\x00\x12\x13\n\tdata_byte\x18\x03 \x01(\x0cH\x00\x42\r\n\x0bvalue_oneof\x1a\x35\n\x08GlobalRT\x12\x10\n\x08RT_value\x18\x01 \x01(\x04\x12\x17\n\x0fRT_timestamp_ms\x18\x02 \x01(\x04\x1a\xd1\x03\n\x04\x44\x61ta\x12\x0f\n\x07\x64\x61ta_id\x18\x01 \x01(\t\x12>\n\x07samples\x18\x02 \x03(\x0b\x32-.jlr.queue.protobuf.ResponseData.Data.Samples\x12\x42\n\x04\x63\x61lc\x18\x03 \x01(\x0b\x32\x34.jlr.queue.protobuf.ResponseData.Data.CalculatedData\x1aZ\n\x07Samples\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x39\n\x06sample\x18\x02 \x01(\x0b\x32).jlr.queue.protobuf.ResponseData.dataType\x1a\xd7\x01\n\x0e\x43\x61lculatedData\x12\x13\n\x0b\x65nd_time_ms\x18\x01 \x01(\x04\x12\x13\n\x0b\x64uration_ms\x18\x02 \x01(\r\x12\x0b\n\x03\x61vg\x18\x03 \x01(\x02\x12\x36\n\x03min\x18\x04 \x01(\x0b\x32).jlr.queue.protobuf.ResponseData.dataType\x12\x36\n\x03max\x18\x05 \x01(\x0b\x32).jlr.queue.protobuf.ResponseData.dataType\x12\x0f\n\x07std_dev\x18\x06 \x01(\x02\x12\r\n\x05\x63ount\x18\x07 \x01(\rBK\n3com.jlr.dda.iotbroker.datatransform.protocol.raw.v3B\x12RawProtobufMessageP\x01\x62\x06proto3'
)


_RESPONSEDATA = DESCRIPTOR.message_types_by_name["ResponseData"]
_RESPONSEDATA_DATATYPE = _RESPONSEDATA.nested_types_by_name["dataType"]
_RESPONSEDATA_GLOBALRT = _RESPONSEDATA.nested_types_by_name["GlobalRT"]
_RESPONSEDATA_DATA = _RESPONSEDATA.nested_types_by_name["Data"]
_RESPONSEDATA_DATA_SAMPLES = _RESPONSEDATA_DATA.nested_types_by_name["Samples"]
_RESPONSEDATA_DATA_CALCULATEDDATA = _RESPONSEDATA_DATA.nested_types_by_name["CalculatedData"]
ResponseData = _reflection.GeneratedProtocolMessageType(
    "ResponseData",
    (_message.Message,),
    {
        "dataType": _reflection.GeneratedProtocolMessageType(
            "dataType",
            (_message.Message,),
            {
                "DESCRIPTOR": _RESPONSEDATA_DATATYPE,
                "__module__": "RawMessageType_3.0_pb2",
                # @@protoc_insertion_point(class_scope:jlr.queue.protobuf.ResponseData.dataType)
            },
        ),
        "GlobalRT": _reflection.GeneratedProtocolMessageType(
            "GlobalRT",
            (_message.Message,),
            {
                "DESCRIPTOR": _RESPONSEDATA_GLOBALRT,
                "__module__": "RawMessageType_3.0_pb2",
                # @@protoc_insertion_point(class_scope:jlr.queue.protobuf.ResponseData.GlobalRT)
            },
        ),
        "Data": _reflection.GeneratedProtocolMessageType(
            "Data",
            (_message.Message,),
            {
                "Samples": _reflection.GeneratedProtocolMessageType(
                    "Samples",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _RESPONSEDATA_DATA_SAMPLES,
                        "__module__": "RawMessageType_3.0_pb2",
                        # @@protoc_insertion_point(class_scope:jlr.queue.protobuf.ResponseData.Data.Samples)
                    },
                ),
                "CalculatedData": _reflection.GeneratedProtocolMessageType(
                    "CalculatedData",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _RESPONSEDATA_DATA_CALCULATEDDATA,
                        "__module__": "RawMessageType_3.0_pb2",
                        # @@protoc_insertion_point(class_scope:jlr.queue.protobuf.ResponseData.Data.CalculatedData)
                    },
                ),
                "DESCRIPTOR": _RESPONSEDATA_DATA,
                "__module__": "RawMessageType_3.0_pb2",
                # @@protoc_insertion_point(class_scope:jlr.queue.protobuf.ResponseData.Data)
            },
        ),
        "DESCRIPTOR": _RESPONSEDATA,
        "__module__": "RawMessageType_3.0_pb2",
        # @@protoc_insertion_point(class_scope:jlr.queue.protobuf.ResponseData)
    },
)
_sym_db.RegisterMessage(ResponseData)
_sym_db.RegisterMessage(ResponseData.dataType)
_sym_db.RegisterMessage(ResponseData.GlobalRT)
_sym_db.RegisterMessage(ResponseData.Data)
_sym_db.RegisterMessage(ResponseData.Data.Samples)
_sym_db.RegisterMessage(ResponseData.Data.CalculatedData)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n3com.jlr.dda.iotbroker.datatransform.protocol.raw.v3B\022RawProtobufMessageP\001"
    _RESPONSEDATA._serialized_start = 49
    _RESPONSEDATA._serialized_end = 837
    _RESPONSEDATA_DATATYPE._serialized_start = 225
    _RESPONSEDATA_DATATYPE._serialized_end = 314
    _RESPONSEDATA_GLOBALRT._serialized_start = 316
    _RESPONSEDATA_GLOBALRT._serialized_end = 369
    _RESPONSEDATA_DATA._serialized_start = 372
    _RESPONSEDATA_DATA._serialized_end = 837
    _RESPONSEDATA_DATA_SAMPLES._serialized_start = 529
    _RESPONSEDATA_DATA_SAMPLES._serialized_end = 619
    _RESPONSEDATA_DATA_CALCULATEDDATA._serialized_start = 622
    _RESPONSEDATA_DATA_CALCULATEDDATA._serialized_end = 837
# @@protoc_insertion_point(module_scope)
