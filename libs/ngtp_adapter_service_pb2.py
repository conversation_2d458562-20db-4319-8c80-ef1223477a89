# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ngtp_adapter_service.proto
# Protobuf Python Version: 5.27.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    1,
    '',
    'ngtp_adapter_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1angtp_adapter_service.proto\x12\x11ngtp_adpt_service\"\xae\x02\n\x14\x44oorLockingOperation\x12@\n\x0b\x64river_door\x18\x01 \x01(\x0e\x32+.ngtp_adpt_service.EnumComplexLockingAction\x12\x43\n\x0epassenger_door\x18\x02 \x01(\x0e\x32+.ngtp_adpt_service.EnumComplexLockingAction\x12\x45\n\x10rear_driver_door\x18\x03 \x01(\x0e\x32+.ngtp_adpt_service.EnumComplexLockingAction\x12H\n\x13rear_passenger_door\x18\x04 \x01(\x0e\x32+.ngtp_adpt_service.EnumComplexLockingAction\"\x8a\x02\n\x1bSetApertureLockStateRequest\x12<\n\x0erequest_source\x18\x01 \x01(\x0e\x32$.ngtp_adpt_service.EnumRequestSource\x12\x46\n\x12\x61perture_operation\x18\x02 \x01(\x0e\x32(.ngtp_adpt_service.EnumApertureOperationH\x00\x12R\n\x1findividual_door_locking_request\x18\x03 \x01(\x0b\x32\'.ngtp_adpt_service.DoorLockingOperationH\x00\x42\x11\n\x0flocking_request\"M\n\x1cSetApertureLockStateResponse\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"\x1d\n\x1bGetApertureLockStateRequest\"\xc4\x04\n\x1cGetApertureLockStateResponse\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\x12\x45\n\x12\x64river_door_status\x18\x02 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12H\n\x15passenger_door_status\x18\x03 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12I\n\x16reardriver_door_status\x18\x04 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12L\n\x19rearpassenger_door_status\x18\x05 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12\x42\n\x0ftailgate_status\x18\x06 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12@\n\rbonnet_status\x18\x07 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12\x45\n\x13\x63\x65ntral_lock_status\x18\x08 \x01(\x0e\x32(.ngtp_adpt_service.EnumCentralLockStatus\"a\n\x16SetBeepAndFlashRequest\x12G\n\x14\x62\x65\x65p_flash_operation\x18\x01 \x01(\x0e\x32).ngtp_adpt_service.EnumBeepFlashOperation\"H\n\x17SetBeepAndFlashResponse\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"X\n\x16SetVehicleAlarmRequest\x12>\n\x0f\x61larm_operation\x18\x01 \x01(\x0e\x32%.ngtp_adpt_service.EnumAlarmOperation\"H\n\x17SetVehicleAlarmResponse\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"\x1d\n\x1bGetVehicleAlarmStateRequest\"\x87\x01\n\x1cGetVehicleAlarmStateResponse\x12\x38\n\x0c\x61larm_status\x18\x01 \x01(\x0e\x32\".ngtp_adpt_service.EnumAlarmStatus\x12-\n\x06status\x18\x02 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"s\n\x1eSetVehicleCabinAirCleanRequest\x12Q\n\x1a\x63\x61\x62in_air_cleaning_request\x18\x01 \x01(\x0e\x32-.ngtp_adpt_service.EnumCabinAirCleanOperation\"P\n\x1fSetVehicleCabinAirCleanResponse\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"%\n#GetVehicleCabinAirCleanStateRequest\"\xd8\x03\n$GetVehicleCabinAirCleanStateResponse\x12M\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32*.ngtp_adpt_service.EnumCabinAirCleanStatus\x12\x1e\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1d\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\rH\x01\x88\x01\x01\x12!\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\rH\x02\x88\x01\x01\x12 \n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\rH\x03\x88\x01\x01\x12-\n\x06status\x18\x06 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\x12-\n cabin_air_clean_cycles_remaining\x18\x07 \x01(\rH\x04\x88\x01\x01\x42\x14\n\x12_cabin_pm2_5_levelB\x13\n\x11_cabin_pm2_5_bandB\x17\n\x15_external_pm2_5_levelB\x16\n\x14_external_pm2_5_bandB#\n!_cabin_air_clean_cycles_remaining\"\xbd\x01\n\x1dSetVehiclePreconditionRequest\x12J\n\x14precondition_request\x18\x01 \x01(\x0e\x32,.ngtp_adpt_service.EnumPreconditionOperation\x12,\n\x1fprecondition_target_temperature\x18\x02 \x01(\x02H\x00\x88\x01\x01\x42\"\n _precondition_target_temperature\"O\n\x1eSetVehiclePreconditionResponse\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"$\n\"GetVehiclePreconditionStateRequest\"\x97\x02\n#GetVehiclePreconditionStateResponse\x12I\n\x11precondition_mode\x18\x01 \x01(\x0e\x32..ngtp_adpt_service.EnumPreConditionCurrentMode\x12\x46\n\x13precondition_status\x18\x02 \x01(\x0e\x32).ngtp_adpt_service.EnumPreConditionStatus\x12\x1b\n\x0etime_remaining\x18\x03 \x01(\rH\x00\x88\x01\x01\x12-\n\x06status\x18\x04 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatusB\x11\n\x0f_time_remaining\"\xbf\x04\n\x17NotifyApertureLockState\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\x12\x45\n\x12\x64river_door_status\x18\x02 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12H\n\x15passenger_door_status\x18\x03 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12I\n\x16reardriver_door_status\x18\x04 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12L\n\x19rearpassenger_door_status\x18\x05 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12\x42\n\x0ftailgate_status\x18\x06 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12@\n\rbonnet_status\x18\x07 \x01(\x0e\x32).ngtp_adpt_service.EnumApertureLockStatus\x12\x45\n\x13\x63\x65ntral_lock_status\x18\x08 \x01(\x0e\x32(.ngtp_adpt_service.EnumCentralLockStatus\"H\n\x17NotifyBeepAndFlashState\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"\x82\x01\n\x17NotifyVehicleAlarmState\x12\x38\n\x0c\x61larm_status\x18\x01 \x01(\x0e\x32\".ngtp_adpt_service.EnumAlarmStatus\x12-\n\x06status\x18\x02 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\"\xd3\x03\n\x1fNotifyVehicleCabinAirCleanState\x12M\n\x19\x63\x61\x62in_air_cleaning_status\x18\x01 \x01(\x0e\x32*.ngtp_adpt_service.EnumCabinAirCleanStatus\x12\x1e\n\x11\x63\x61\x62in_pm2_5_level\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1d\n\x10\x63\x61\x62in_pm2_5_band\x18\x03 \x01(\rH\x01\x88\x01\x01\x12!\n\x14\x65xternal_pm2_5_level\x18\x04 \x01(\rH\x02\x88\x01\x01\x12 \n\x13\x65xternal_pm2_5_band\x18\x05 \x01(\rH\x03\x88\x01\x01\x12-\n\x06status\x18\x06 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatus\x12-\n cabin_air_clean_cycles_remaining\x18\x07 \x01(\rH\x04\x88\x01\x01\x42\x14\n\x12_cabin_pm2_5_levelB\x13\n\x11_cabin_pm2_5_bandB\x17\n\x15_external_pm2_5_levelB\x16\n\x14_external_pm2_5_bandB#\n!_cabin_air_clean_cycles_remaining\"\x92\x02\n\x1eNotifyVehiclePreconditionState\x12I\n\x11precondition_mode\x18\x01 \x01(\x0e\x32..ngtp_adpt_service.EnumPreConditionCurrentMode\x12\x46\n\x13precondition_status\x18\x02 \x01(\x0e\x32).ngtp_adpt_service.EnumPreConditionStatus\x12\x1b\n\x0etime_remaining\x18\x03 \x01(\rH\x00\x88\x01\x01\x12-\n\x06status\x18\x04 \x01(\x0e\x32\x1d.ngtp_adpt_service.EnumStatusB\x11\n\x0f_time_remaining*\x98\x03\n\nEnumStatus\x12\x1b\n\x17\x45NUM_STATUS_UNSPECIFIED\x10\x00\x12\x12\n\x0e\x45NUM_STATUS_OK\x10\x01\x12\x1d\n\x19\x45NUM_STATUS_DATA_DEGRADED\x10\x02\x12\x1f\n\x1b\x45NUM_STATUS_DATA_UNRELIABLE\x10\x03\x12 \n\x1c\x45NUM_STATUS_DATA_UNAVAILABLE\x10\x04\x12+\n\'ENUM_STATUS_ERROR_INVALID_SERVICE_STATE\x10\x05\x12+\n\'ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE\x10\x06\x12/\n+ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION\x10\x07\x12)\n%ENUM_STATUS_ERROR_MISSING_INPUT_FIELD\x10\x08\x12)\n%ENUM_STATUS_ERROR_INVALID_INPUT_FIELD\x10\t\x12\x16\n\x12\x45NUM_STATUS_NOT_OK\x10\n*\xb7\x02\n\x15\x45numApertureOperation\x12\'\n#ENUM_APERTURE_OPERATION_UNSPECIFIED\x10\x00\x12+\n\'ENUM_APERTURE_OPERATION_EXTERNAL_UNLOCK\x10\x01\x12/\n+ENUM_APERTURE_OPERATION_EXTERNAL_SINGLELOCK\x10\x02\x12/\n+ENUM_APERTURE_OPERATION_EXTERNAL_DOUBLELOCK\x10\x03\x12\x33\n/ENUM_APERTURE_OPERATION_INTERNAL_CENTRAL_UNLOCK\x10\x04\x12\x31\n-ENUM_APERTURE_OPERATION_INTERNAL_CENTRAL_LOCK\x10\x05*\xa0\x01\n\x11\x45numRequestSource\x12#\n\x1f\x45NUM_REQUEST_SOURCE_UNSPECIFIED\x10\x00\x12 \n\x1c\x45NUM_REQUEST_SOURCE_EXTERIOR\x10\x01\x12\"\n\x1e\x45NUM_REQUEST_SOURCE_TELEMATICS\x10\x02\x12 \n\x1c\x45NUM_REQUEST_SOURCE_INTERIOR\x10\x03*\x95\x01\n\x18\x45numComplexLockingAction\x12+\n\'ENUM_COMPLEX_LOCKING_ACTION_UNSPECIFIED\x10\x00\x12&\n\"ENUM_COMPLEX_LOCKING_ACTION_UNLOCK\x10\x01\x12$\n ENUM_COMPLEX_LOCKING_ACTION_LOCK\x10\x02*\xc5\x01\n\x16\x45numApertureLockStatus\x12)\n%ENUM_APERTURE_LOCK_STATUS_UNSPECIFIED\x10\x00\x12&\n\"ENUM_APERTURE_LOCK_STATUS_UNLOCKED\x10\x01\x12+\n\'ENUM_APERTURE_LOCK_STATUS_SINGLE_LOCKED\x10\x02\x12+\n\'ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\x10\x03*\xee\x01\n\x15\x45numCentralLockStatus\x12(\n$ENUM_CENTRAL_LOCK_STATUS_UNSPECIFIED\x10\x00\x12%\n!ENUM_CENTRAL_LOCK_STATUS_UNLOCKED\x10\x01\x12+\n\'ENUM_CENTRAL_LOCK_STATUS_PARTIAL_LOCKED\x10\x02\x12+\n\'ENUM_CENTRAL_LOCK_STATUS_CENTRAL_LOCKED\x10\x03\x12*\n&ENUM_CENTRAL_LOCK_STATUS_DOUBLE_LOCKED\x10\x04*Y\n\x12\x45numAlarmOperation\x12$\n ENUM_ALARM_OPERATION_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_ALARM_OPERATION_STOP\x10\x01*\xaf\x01\n\x0f\x45numAlarmStatus\x12!\n\x1d\x45NUM_ALARM_STATUS_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x45NUM_ALARM_STATUS_ARMED\x10\x01\x12\x1e\n\x1a\x45NUM_ALARM_STATUS_DISARMED\x10\x02\x12\x1f\n\x1b\x45NUM_ALARM_STATUS_TRIGGERED\x10\x03\x12\x1b\n\x17\x45NUM_ALARM_STATUS_FAULT\x10\x04*\x9f\x01\n\x1a\x45numCabinAirCleanOperation\x12.\n*ENUM_CABIN_AIR_CLEAN_OPERATION_UNSPECIFIED\x10\x00\x12(\n$ENUM_CABIN_AIR_CLEAN_OPERATION_START\x10\x01\x12\'\n#ENUM_CABIN_AIR_CLEAN_OPERATION_STOP\x10\x02*\x87\x06\n\x17\x45numCabinAirCleanStatus\x12(\n$ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED\x10\x00\x12%\n!ENUM_CABIN_AIR_CLEAN_STS_INACTIVE\x10\x01\x12%\n!ENUM_CABIN_AIR_CLEAN_STS_COMPLETE\x10\x02\x12(\n$ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS\x10\x03\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED\x10\x04\x12,\n(ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY\x10\x05\x12\x33\n/ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE\x10\x06\x12\x39\n5ENUM_CABIN_AIR_CLEAN_STS_ERR_CLEANING_CYCLE_EXHAUSTED\x10\x07\x12-\n)ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT\x10\x08\x12\x39\n5ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_POWER_TRANSITION\x10\t\x12\x35\n1ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE\x10\n\x12/\n+ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED\x10\x0b\x12\x31\n-ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE\x10\x0c\x12\x38\n4ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERVICE_ACTIVE\x10\r\x12\x36\n2ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS\x10\x0e*\xa7\x01\n\x19\x45numPreconditionOperation\x12+\n\'ENUM_PRECONDITION_OPERATION_UNSPECIFIED\x10\x00\x12.\n*ENUM_PRECONDITION_OPERATION_OFFBOARD_START\x10\x01\x12-\n)ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP\x10\x02*\xd2\x01\n\x1b\x45numPreConditionCurrentMode\x12.\n*ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED\x10\x00\x12+\n\'ENUM_PRECONDITION_CURRENT_MODE_INACTIVE\x10\x01\x12,\n(ENUM_PRECONDITION_CURRENT_MODE_IMMEDIATE\x10\x02\x12(\n$ENUM_PRECONDITION_CURRENT_MODE_TIMED\x10\x03*\xd0\x02\n\x16\x45numPreConditionStatus\x12%\n!ENUM_PRECONDITION_STS_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45NUM_PRECONDITION_STS_OFF\x10\x01\x12\"\n\x1e\x45NUM_PRECONDITION_STS_START_UP\x10\x02\x12%\n!ENUM_PRECONDITION_STS_IN_PROGRESS\x10\x03\x12\"\n\x1e\x45NUM_PRECONDITION_STS_COMPLETE\x10\x04\x12*\n&ENUM_PRECONDITION_STS_PARTIAL_COMPLETE\x10\x05\x12)\n%ENUM_PRECONDITION_STS_ERR_LOW_BATTERY\x10\x06\x12*\n&ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT\x10\x07*\x88\x01\n\x16\x45numBeepFlashOperation\x12)\n%ENUM_BEEP_FLASH_OPERATION_UNSPECIFIED\x10\x00\x12 \n\x1c\x45NUM_BEEP_FLASH_OPERATION_ON\x10\x01\x12!\n\x1d\x45NUM_BEEP_FLASH_OPERATION_OFF\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ngtp_adapter_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_ENUMSTATUS']._serialized_start=4661
  _globals['_ENUMSTATUS']._serialized_end=5069
  _globals['_ENUMAPERTUREOPERATION']._serialized_start=5072
  _globals['_ENUMAPERTUREOPERATION']._serialized_end=5383
  _globals['_ENUMREQUESTSOURCE']._serialized_start=5386
  _globals['_ENUMREQUESTSOURCE']._serialized_end=5546
  _globals['_ENUMCOMPLEXLOCKINGACTION']._serialized_start=5549
  _globals['_ENUMCOMPLEXLOCKINGACTION']._serialized_end=5698
  _globals['_ENUMAPERTURELOCKSTATUS']._serialized_start=5701
  _globals['_ENUMAPERTURELOCKSTATUS']._serialized_end=5898
  _globals['_ENUMCENTRALLOCKSTATUS']._serialized_start=5901
  _globals['_ENUMCENTRALLOCKSTATUS']._serialized_end=6139
  _globals['_ENUMALARMOPERATION']._serialized_start=6141
  _globals['_ENUMALARMOPERATION']._serialized_end=6230
  _globals['_ENUMALARMSTATUS']._serialized_start=6233
  _globals['_ENUMALARMSTATUS']._serialized_end=6408
  _globals['_ENUMCABINAIRCLEANOPERATION']._serialized_start=6411
  _globals['_ENUMCABINAIRCLEANOPERATION']._serialized_end=6570
  _globals['_ENUMCABINAIRCLEANSTATUS']._serialized_start=6573
  _globals['_ENUMCABINAIRCLEANSTATUS']._serialized_end=7348
  _globals['_ENUMPRECONDITIONOPERATION']._serialized_start=7351
  _globals['_ENUMPRECONDITIONOPERATION']._serialized_end=7518
  _globals['_ENUMPRECONDITIONCURRENTMODE']._serialized_start=7521
  _globals['_ENUMPRECONDITIONCURRENTMODE']._serialized_end=7731
  _globals['_ENUMPRECONDITIONSTATUS']._serialized_start=7734
  _globals['_ENUMPRECONDITIONSTATUS']._serialized_end=8070
  _globals['_ENUMBEEPFLASHOPERATION']._serialized_start=8073
  _globals['_ENUMBEEPFLASHOPERATION']._serialized_end=8209
  _globals['_DOORLOCKINGOPERATION']._serialized_start=50
  _globals['_DOORLOCKINGOPERATION']._serialized_end=352
  _globals['_SETAPERTURELOCKSTATEREQUEST']._serialized_start=355
  _globals['_SETAPERTURELOCKSTATEREQUEST']._serialized_end=621
  _globals['_SETAPERTURELOCKSTATERESPONSE']._serialized_start=623
  _globals['_SETAPERTURELOCKSTATERESPONSE']._serialized_end=700
  _globals['_GETAPERTURELOCKSTATEREQUEST']._serialized_start=702
  _globals['_GETAPERTURELOCKSTATEREQUEST']._serialized_end=731
  _globals['_GETAPERTURELOCKSTATERESPONSE']._serialized_start=734
  _globals['_GETAPERTURELOCKSTATERESPONSE']._serialized_end=1314
  _globals['_SETBEEPANDFLASHREQUEST']._serialized_start=1316
  _globals['_SETBEEPANDFLASHREQUEST']._serialized_end=1413
  _globals['_SETBEEPANDFLASHRESPONSE']._serialized_start=1415
  _globals['_SETBEEPANDFLASHRESPONSE']._serialized_end=1487
  _globals['_SETVEHICLEALARMREQUEST']._serialized_start=1489
  _globals['_SETVEHICLEALARMREQUEST']._serialized_end=1577
  _globals['_SETVEHICLEALARMRESPONSE']._serialized_start=1579
  _globals['_SETVEHICLEALARMRESPONSE']._serialized_end=1651
  _globals['_GETVEHICLEALARMSTATEREQUEST']._serialized_start=1653
  _globals['_GETVEHICLEALARMSTATEREQUEST']._serialized_end=1682
  _globals['_GETVEHICLEALARMSTATERESPONSE']._serialized_start=1685
  _globals['_GETVEHICLEALARMSTATERESPONSE']._serialized_end=1820
  _globals['_SETVEHICLECABINAIRCLEANREQUEST']._serialized_start=1822
  _globals['_SETVEHICLECABINAIRCLEANREQUEST']._serialized_end=1937
  _globals['_SETVEHICLECABINAIRCLEANRESPONSE']._serialized_start=1939
  _globals['_SETVEHICLECABINAIRCLEANRESPONSE']._serialized_end=2019
  _globals['_GETVEHICLECABINAIRCLEANSTATEREQUEST']._serialized_start=2021
  _globals['_GETVEHICLECABINAIRCLEANSTATEREQUEST']._serialized_end=2058
  _globals['_GETVEHICLECABINAIRCLEANSTATERESPONSE']._serialized_start=2061
  _globals['_GETVEHICLECABINAIRCLEANSTATERESPONSE']._serialized_end=2533
  _globals['_SETVEHICLEPRECONDITIONREQUEST']._serialized_start=2536
  _globals['_SETVEHICLEPRECONDITIONREQUEST']._serialized_end=2725
  _globals['_SETVEHICLEPRECONDITIONRESPONSE']._serialized_start=2727
  _globals['_SETVEHICLEPRECONDITIONRESPONSE']._serialized_end=2806
  _globals['_GETVEHICLEPRECONDITIONSTATEREQUEST']._serialized_start=2808
  _globals['_GETVEHICLEPRECONDITIONSTATEREQUEST']._serialized_end=2844
  _globals['_GETVEHICLEPRECONDITIONSTATERESPONSE']._serialized_start=2847
  _globals['_GETVEHICLEPRECONDITIONSTATERESPONSE']._serialized_end=3126
  _globals['_NOTIFYAPERTURELOCKSTATE']._serialized_start=3129
  _globals['_NOTIFYAPERTURELOCKSTATE']._serialized_end=3704
  _globals['_NOTIFYBEEPANDFLASHSTATE']._serialized_start=3706
  _globals['_NOTIFYBEEPANDFLASHSTATE']._serialized_end=3778
  _globals['_NOTIFYVEHICLEALARMSTATE']._serialized_start=3781
  _globals['_NOTIFYVEHICLEALARMSTATE']._serialized_end=3911
  _globals['_NOTIFYVEHICLECABINAIRCLEANSTATE']._serialized_start=3914
  _globals['_NOTIFYVEHICLECABINAIRCLEANSTATE']._serialized_end=4381
  _globals['_NOTIFYVEHICLEPRECONDITIONSTATE']._serialized_start=4384
  _globals['_NOTIFYVEHICLEPRECONDITIONSTATE']._serialized_end=4658
# @@protoc_insertion_point(module_scope)
