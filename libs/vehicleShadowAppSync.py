import os
import requests
import json
from os import path
from robot.api.deco import keyword
from pyaml_env import parse_config, BaseConfig
from aws_requests_auth.boto_utils import BotoAWSRequestsAuth

systemVarFile = "../variables/system.yml"
CONTENT_TYPE = "application/json"
PAYLOAD_DEFAULT = "{SIGNAL_LABEL_VALUE_ONE}"
VEHICLE_DEFAULT = "{VEHICLE_UNIQUE_ID_VALUE}"


def get_auth():
    return BotoAWSRequestsAuth(
        aws_host=os.getenv("OneAppAppSyncApiHost"),
        aws_region="eu-west-2",
        aws_service="appsync",
    )


def geturl():
    system_vars = BaseConfig(parse_config(systemVarFile))
    appSynUrl = getattr(system_vars, "vehicle-shadow-appsync-url")
    return appSynUrl


def getVehicleShadowHost():
    system_vars = BaseConfig(parse_config(systemVarFile))
    vsHost = getattr(system_vars, "vehicle-shadow-host")
    return vsHost


def read_graphql_schema_file(filename):
    file_path = path.relpath("../graphqlschema/" + filename)
    with open(file_path) as f:
        payload = f.read()
    return payload


@keyword("Get Vehicle Shadow Data")
def get_vehicle_data(vehicleId, signalLabel):
    auth = get_auth()
    match signalLabel:
        case "frontSystemOnCmd" | "cabinCleaning" | "alarmMode" | "isSteerWheelHeated":
            payload = read_graphql_schema_file("queryVehicleStatusData-NoAbstract.graphql")
            payload = payload.replace(PAYLOAD_DEFAULT, signalLabel)
            payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
            print(payload)
        case "evBatteryChargeTarget" | "tyrePressureFrontLeft" | "evHvBatteryChargeCurrentLimit":
            payload = read_graphql_schema_file("queryVehicleStatusData-ValueSource.graphql")
            payload = payload.replace(PAYLOAD_DEFAULT, signalLabel)
            payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
            print(payload)
        case "doors" | "tailgate" | "bonnet":
            payload = read_graphql_schema_file("queryVehicleStatusData-NoDetail.graphql")
            payload = payload.replace(PAYLOAD_DEFAULT, signalLabel)
            payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
            print(payload)
        case _:
            payload = read_graphql_schema_file("queryVehicleStatusData.graphql")
            payload = payload.replace(PAYLOAD_DEFAULT, signalLabel)
            payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
            print(payload)

    headers = {"Content-Type": CONTENT_TYPE}

    url = geturl()
    response = requests.request("POST", url, headers=headers, data=payload, auth=auth)

    print(response.text)
    return (response.status_code, response.text)


@keyword("Get Vehicle Data By Signal List")
def get_vehicle_data_signal_list(vehicleId, signalLabelOne, signalLabelTwo):
    payload = read_graphql_schema_file("queryVehicleDataBySignalList.graphql")
    payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
    payload = payload.replace(PAYLOAD_DEFAULT, signalLabelOne)
    payload = payload.replace("{SIGNAL_LABEL_VALUE_TWO}", signalLabelTwo)
    print(payload)

    auth = get_auth()
    headers = {"Content-Type": CONTENT_TYPE}

    url = geturl()
    response = requests.request("POST", url, headers=headers, data=payload, auth=auth)

    print(response.text)
    return response.status_code, response.text


@keyword("Request AppSync Vehicle Shadow Data")
def query_app_sync_save_response(vehicleId, appSyncSignalName, contains, frToken=""):
    if frToken == "":
        print("aws auth")
        auth = get_auth()
        headers = {"Content-Type": CONTENT_TYPE}
    else:
        print("Using ForgeRock")
        vsHost = getVehicleShadowHost()

        headers = {"Host": vsHost, "Content-Type": CONTENT_TYPE, "Authorization": frToken}
        auth = ""

    if contains == "NoAbstract":
        payload = read_graphql_schema_file("queryVehicleStatusData-NoAbstract.graphql")
        payload = payload.replace(PAYLOAD_DEFAULT, appSyncSignalName)
        payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
        print(payload)
    elif contains == "OnlyValue":
        payload = read_graphql_schema_file("queryVehicleStatusData-ValueSource.graphql")
        payload = payload.replace(PAYLOAD_DEFAULT, appSyncSignalName)
        payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
        print(payload)
    elif contains == "NoDetail":
        payload = read_graphql_schema_file("queryVehicleStatusData-NoDetail.graphql")
        payload = payload.replace(PAYLOAD_DEFAULT, appSyncSignalName)
        payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
        print(payload)
    elif contains == "Everything":
        payload = read_graphql_schema_file("queryVehicleStatusData.graphql")
        payload = payload.replace(PAYLOAD_DEFAULT, appSyncSignalName)
        payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
        print(payload)

    url = geturl()
    response = requests.request("POST", url, headers=headers, data=payload, auth=auth)

    print(response.text)
    return (response.status_code, response.text)


@keyword("Query Vehicle Data by Vehicle id")
def query_vehicle_data(vehicleId):
    payload = read_graphql_schema_file("queryVehicleData.graphql")
    payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
    print(payload)

    auth = get_auth()
    headers = {"Content-Type": CONTENT_TYPE}

    url = geturl()
    response = requests.request("POST", url, headers=headers, data=payload, auth=auth)

    print(response.text)
    return response.status_code, response.text


@keyword("Get Vehicle Status Data by Vehicle id")
def get_vehicle_status_data_by_vehicle_id(vehicleId, signal):
    payload = read_graphql_schema_file(signal + ".graphql")
    payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
    print(payload)

    headers = {"Content-Type": CONTENT_TYPE}
    auth = get_auth()
    url = geturl()
    response = requests.request("POST", url, headers=headers, data=payload, auth=auth)

    print(response.text)
    return response.status_code, response.text


@keyword("Query app sync vehicle data")
def get_vehicle_status_data(vehicleId):
    payload = read_graphql_schema_file("getVehicleStatusData.graphql")
    payload = payload.replace(VEHICLE_DEFAULT, vehicleId)
    print(payload)

    headers = {"Content-Type": CONTENT_TYPE}

    url = geturl()
    auth = get_auth()
    response = requests.request("POST", url, headers=headers, data=payload, auth=auth)
    print(response.text)
    return response.status_code, response.text


@keyword("Request app sync vehicle data")
def query_appsync(query):
    print(f"Query is - {query}")
    headers = {"Content-Type": CONTENT_TYPE}

    url = geturl()
    auth = get_auth()

    request_query = json.loads(query)

    print(f"{request_query}")
    response = requests.request("POST", url, headers=headers, data=query, auth=auth)

    print(f"Response is: {response}")

    return response.json()
