import requests
import json
import time

from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig

from commonUtils import get_keycloak_token

SYSTEM_VAR_FILE = "../variables/system.yml"
AUTH_TYPE = "Bearer "


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(SYSTEM_VAR_FILE))
    return getattr(system_vars, "vehicle-lifecycle-consent-url")


@keyword("Get Permissions for VIN")
def get_perm_for_vin(vin, user, pw):
    """
    Used to get the current permissions for a VIN
    Examples:
    | Get Permissions for VIN | TSTLRGBL663001234 | user | password |
    """
    token = get_keycloak_token(user, pw, True)

    headers = {"Vins": vin, "Authorization": AUTH_TYPE + token}

    url = get_url() + "permissions"
    print(url)
    payload = {}

    response = requests.request("GET", url, headers=headers, data=payload)
    print(response.text)

    return response.text, response.status_code


@keyword("Check permission is valid")
def check_perm_for_vin(vin, user, pw):
    permissionsSet = False
    currentPerm = get_perm_for_vin(vin, user, pw)
    y = json.loads(currentPerm[0])
    permlist = y["permissions"][0]["is_perm_list"]
    exclusive_from = y["permissions"][0]["exclusive_from"]
    exclusive_to = y["permissions"][0]["exclusive_to"]

    timeNow = int(time.time())
    pattern = "%Y-%m-%dT%H:%M:%S.%fZ"

    if permlist == []:
        print("permission list empty")
        permissionsSet = False
        return permissionsSet
    else:
        print("got permissions")
        epoch_exclusive_from = int(time.mktime(time.strptime(exclusive_from, pattern)))
        epoch_exclusive_to = int(time.mktime(time.strptime(exclusive_to, pattern)))

    if (timeNow <= epoch_exclusive_from) or (timeNow >= epoch_exclusive_to):
        print("Current time outside permission time")
        permissionsSet = False
        return permissionsSet

    # Check permissions in perm list
    L_App_Privacy = False
    A_App_Usage = False
    for x in permlist:
        if x["label"] == "L_App_Privacy":
            print("Got:: L_App_Privacy")
            L_App_Privacy = True
            break
    for x in permlist:
        if x["label"] == "A_App_Usage":
            print("Got:: A_App_Usage")
            A_App_Usage = True
            break
    if A_App_Usage and L_App_Privacy:
        permissionsSet = True

    return permissionsSet
