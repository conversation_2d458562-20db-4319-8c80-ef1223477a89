import requests
import ForgeRockVehicleTokenService
import hashlib
import json
import defusedxml.ElementTree as dxml
from pyaml_env import parse_config, BaseConfig
from robot.api.deco import keyword, not_keyword
from json import JSONDecodeError
from robot.api import logger

vin = "TESTVIN0987654322"
tcu = "TESTTCU008"

systemVarFile = "../variables/system.yml"
system_vars = BaseConfig(parse_config(systemVarFile))
vehicle_certfile = str(getattr(system_vars, "vehicle_certfile"))
vehicle_keyfile = str(getattr(system_vars, "vehicle_keyfile"))
vehicle_private_keyfile = str(getattr(system_vars, "vehicle_private_keyfile"))


# generate client id which is hash value of VIN and TCU
h = hashlib.new("sha256")
h.update(str(vin + tcu).encode("utf-8"))
vehicle_client_id = h.hexdigest()


@not_keyword
def get_headers(token):
    return {
        "accept": "*/*",
        "Authorization": "Bearer " + token,
    }


@not_keyword
def get_forge_rock_token():
    return ForgeRockVehicleTokenService.get_token_passphrase_mqtt_client_id(vehicle_client_id, False)[0]


@not_keyword
def client_assertion():
    return ForgeRockVehicleTokenService.get_client_assertion(vehicle_client_id, False)


@not_keyword
def update_xml_values(datafile, tag_list):
    counter = 0
    tree = dxml.parse(datafile)
    for tag in tag_list:
        tree.findall(".//ETag")[counter].text = tag[0]
        counter = counter + 1
    tree.write(datafile)


@not_keyword
def get_api_gateway_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "API-Gateway")


@not_keyword
def get_cloud_front_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "cloud-front")


@not_keyword
def get_fta_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "fta-token-url")


@not_keyword
def fta_token():
    openJsonData = open("../forgeRockjson/FTAtokenRequestBody.json", "r")
    jsonRequestBody = json.loads(openJsonData.read())
    jsonRequestBody["client_assertion"] = client_assertion().decode("utf-8")
    header = {"Content-Type": "application/x-www-form-urlencoded"}
    ftaUrl = get_fta_url()
    print(ftaUrl)
    response = requests.request("POST", ftaUrl, headers=header, data=jsonRequestBody, cert=(vehicle_certfile, vehicle_keyfile))
    logger.debug(response.text)
    try:
        access_token = json.loads(response.content)["access_token"]
    except (KeyError, JSONDecodeError) as error:
        print(error)
        access_token = ""
    print("this is fta token:", access_token)
    return access_token


@keyword("Initiate multipart upload")
def initiate_multipart_upload(upload, uuid, app_name, file_name):
    # print("this is vehicle client id:", vehicle_client_id)
    token = get_forge_rock_token()
    # print("this is token:", token)

    header = get_headers(token)

    url = get_api_gateway_url()
    initiate_multipart = f"{url}/{uuid}/{app_name}/files/{file_name}?uploads={upload}"
    print(initiate_multipart)

    response = requests.request("POST", initiate_multipart, headers=header, cert=(vehicle_certfile, vehicle_keyfile))
    print(response.text)
    return response.text, response.status_code


@keyword("Complete multipart upload")
def complete_multipart_upload(payload_data, etag1, etag2, etag3, upload_id, uuid, app_name, file_name):
    # print("this is vehicle client id:", vehicle_client_id)
    token = get_forge_rock_token()
    # print("this is token:", token)
    header = get_headers(token)

    url = get_api_gateway_url()
    completemultipart = f"{url}/{uuid}/{app_name}/files/{file_name}?uploadId={upload_id}"
    print(completemultipart)
    tag_list = [etag1, etag2, etag3]
    update_xml_values(payload_data, tag_list)
    with open(payload_data, "r") as file:
        read_data = file.read().rstrip()
    print("this is payload:", read_data)
    response = requests.request("POST", completemultipart, headers=header, data=read_data, cert=(vehicle_certfile, vehicle_keyfile))
    print(response.text)
    print(response.status_code)
    return response.text, response.status_code


@keyword("Multipart upload")
def multipart_upload(test_files, part, upload_id, uuid, app_name, file_name):
    ftaToken = fta_token()
    header = {
        "Authorization": "Bearer " + ftaToken,
        "content-type": "application/octet-stream",
    }

    url = get_cloud_front_url()
    multipart = f"{url}/{uuid}/{app_name}/files/{file_name}?partNumber={part}&uploadId={upload_id}"
    print(multipart)

    file = {"file": open(test_files, "rb")}
    response = requests.request("PUT", multipart, headers=header, files=file)

    headers = json.dumps(dict(response.headers))
    return headers, response.status_code


@keyword("Abort multipart upload")
def abort_multipart_upload(upload_id, uuid, app_name, file_name):
    # print("this is vehicle client id:", vehicle_client_id)
    token = get_forge_rock_token()
    # print("this is token:", token)
    header = get_headers(token)

    url = get_api_gateway_url()
    abort_multipart = f"{url}/{uuid}/{app_name}/files/{file_name}?uploadId={upload_id}"
    print(abort_multipart)

    response = requests.request("DELETE", abort_multipart, headers=header, cert=(vehicle_certfile, vehicle_keyfile))
    print(response.text)
    return response.text, response.status_code
