import climate_common_pb2 as _climate_common_pb2
import scheduling_common_pb2 as _scheduling_common_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnumStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_STATUS_UNSPECIFIED: _ClassVar[EnumStatus]
    ENUM_STATUS_OK: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_DEGRADED: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_UNRELIABLE: _ClassVar[EnumStatus]
    ENUM_STATUS_DATA_UNAVAILABLE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_SERVICE_STATE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_MISSING_INPUT_FIELD: _ClassVar[EnumStatus]
    ENUM_STATUS_ERROR_INVALID_INPUT_FIELD: _ClassVar[EnumStatus]
    ENUM_STATUS_NOT_OK: _ClassVar[EnumStatus]

class EnumEventTimeType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ENUM_EVENT_TIME_TYPE_UNSPECIFIED: _ClassVar[EnumEventTimeType]
    ENUM_EVENT_TIME_TYPE_BEGIN: _ClassVar[EnumEventTimeType]
    ENUM_EVENT_TIME_TYPE_END: _ClassVar[EnumEventTimeType]
ENUM_STATUS_UNSPECIFIED: EnumStatus
ENUM_STATUS_OK: EnumStatus
ENUM_STATUS_DATA_DEGRADED: EnumStatus
ENUM_STATUS_DATA_UNRELIABLE: EnumStatus
ENUM_STATUS_DATA_UNAVAILABLE: EnumStatus
ENUM_STATUS_ERROR_INVALID_SERVICE_STATE: EnumStatus
ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE: EnumStatus
ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION: EnumStatus
ENUM_STATUS_ERROR_MISSING_INPUT_FIELD: EnumStatus
ENUM_STATUS_ERROR_INVALID_INPUT_FIELD: EnumStatus
ENUM_STATUS_NOT_OK: EnumStatus
ENUM_EVENT_TIME_TYPE_UNSPECIFIED: EnumEventTimeType
ENUM_EVENT_TIME_TYPE_BEGIN: EnumEventTimeType
ENUM_EVENT_TIME_TYPE_END: EnumEventTimeType

class DepartureTaskPreconditioning(_message.Message):
    __slots__ = ["precondition_target_temperature", "seat_climate_zone", "hsw_temperature_level"]
    PRECONDITION_TARGET_TEMPERATURE_FIELD_NUMBER: _ClassVar[int]
    SEAT_CLIMATE_ZONE_FIELD_NUMBER: _ClassVar[int]
    HSW_TEMPERATURE_LEVEL_FIELD_NUMBER: _ClassVar[int]
    precondition_target_temperature: float
    seat_climate_zone: _containers.RepeatedCompositeFieldContainer[_climate_common_pb2.SeatClimateZoneState]
    hsw_temperature_level: _climate_common_pb2.EnumHSWTemperatureLevel
    def __init__(self, precondition_target_temperature: _Optional[float] = ..., seat_climate_zone: _Optional[_Iterable[_Union[_climate_common_pb2.SeatClimateZoneState, _Mapping]]] = ..., hsw_temperature_level: _Optional[_Union[_climate_common_pb2.EnumHSWTemperatureLevel, str]] = ...) -> None: ...

class DepartureTaskCabinClean(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class DepartureTask(_message.Message):
    __slots__ = ["preconditioning", "cabin_clean"]
    PRECONDITIONING_FIELD_NUMBER: _ClassVar[int]
    CABIN_CLEAN_FIELD_NUMBER: _ClassVar[int]
    preconditioning: DepartureTaskPreconditioning
    cabin_clean: DepartureTaskCabinClean
    def __init__(self, preconditioning: _Optional[_Union[DepartureTaskPreconditioning, _Mapping]] = ..., cabin_clean: _Optional[_Union[DepartureTaskCabinClean, _Mapping]] = ...) -> None: ...

class DepartureEvent(_message.Message):
    __slots__ = ["departure_time", "departure_tasks"]
    DEPARTURE_TIME_FIELD_NUMBER: _ClassVar[int]
    DEPARTURE_TASKS_FIELD_NUMBER: _ClassVar[int]
    departure_time: int
    departure_tasks: _containers.RepeatedCompositeFieldContainer[DepartureTask]
    def __init__(self, departure_time: _Optional[int] = ..., departure_tasks: _Optional[_Iterable[_Union[DepartureTask, _Mapping]]] = ...) -> None: ...

class WakeUpRequestDetails(_message.Message):
    __slots__ = ["event_time_type", "event_time", "event_duration_seconds", "wakeup_reason"]
    EVENT_TIME_TYPE_FIELD_NUMBER: _ClassVar[int]
    EVENT_TIME_FIELD_NUMBER: _ClassVar[int]
    EVENT_DURATION_SECONDS_FIELD_NUMBER: _ClassVar[int]
    WAKEUP_REASON_FIELD_NUMBER: _ClassVar[int]
    event_time_type: EnumEventTimeType
    event_time: int
    event_duration_seconds: int
    wakeup_reason: _scheduling_common_pb2.EnumVehicleWakeUpReason
    def __init__(self, event_time_type: _Optional[_Union[EnumEventTimeType, str]] = ..., event_time: _Optional[int] = ..., event_duration_seconds: _Optional[int] = ..., wakeup_reason: _Optional[_Union[_scheduling_common_pb2.EnumVehicleWakeUpReason, str]] = ...) -> None: ...

class WakeUpResponseDetails(_message.Message):
    __slots__ = ["result", "start_time_utc", "start_time_localtime", "end_time_utc", "end_time_localtime"]
    RESULT_FIELD_NUMBER: _ClassVar[int]
    START_TIME_UTC_FIELD_NUMBER: _ClassVar[int]
    START_TIME_LOCALTIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_UTC_FIELD_NUMBER: _ClassVar[int]
    END_TIME_LOCALTIME_FIELD_NUMBER: _ClassVar[int]
    result: EnumStatus
    start_time_utc: int
    start_time_localtime: int
    end_time_utc: int
    end_time_localtime: int
    def __init__(self, result: _Optional[_Union[EnumStatus, str]] = ..., start_time_utc: _Optional[int] = ..., start_time_localtime: _Optional[int] = ..., end_time_utc: _Optional[int] = ..., end_time_localtime: _Optional[int] = ...) -> None: ...

class SetDepartureScheduleRequest(_message.Message):
    __slots__ = ["departure_events"]
    DEPARTURE_EVENTS_FIELD_NUMBER: _ClassVar[int]
    departure_events: _containers.RepeatedCompositeFieldContainer[DepartureEvent]
    def __init__(self, departure_events: _Optional[_Iterable[_Union[DepartureEvent, _Mapping]]] = ...) -> None: ...

class SetDepartureScheduleResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class SetWakeUpTimesRequest(_message.Message):
    __slots__ = ["wakeup_details"]
    WAKEUP_DETAILS_FIELD_NUMBER: _ClassVar[int]
    wakeup_details: _containers.RepeatedCompositeFieldContainer[WakeUpRequestDetails]
    def __init__(self, wakeup_details: _Optional[_Iterable[_Union[WakeUpRequestDetails, _Mapping]]] = ...) -> None: ...

class SetWakeUpTimesResponse(_message.Message):
    __slots__ = ["status", "wakeup_resp_details"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    WAKEUP_RESP_DETAILS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    wakeup_resp_details: _containers.RepeatedCompositeFieldContainer[WakeUpResponseDetails]
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ..., wakeup_resp_details: _Optional[_Iterable[_Union[WakeUpResponseDetails, _Mapping]]] = ...) -> None: ...

class ClearChargingWakeUpTimesRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class ClearChargingWakeUpTimesResponse(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ...) -> None: ...

class GetNextDepartureEventRequest(_message.Message):
    __slots__ = []
    def __init__(self) -> None: ...

class GetNextDepartureEventResponse(_message.Message):
    __slots__ = ["status", "departure_event"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    DEPARTURE_EVENT_FIELD_NUMBER: _ClassVar[int]
    status: EnumStatus
    departure_event: DepartureEvent
    def __init__(self, status: _Optional[_Union[EnumStatus, str]] = ..., departure_event: _Optional[_Union[DepartureEvent, _Mapping]] = ...) -> None: ...
