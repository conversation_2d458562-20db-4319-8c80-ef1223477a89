import json
import boto3
from robot.api.deco import not_keyword
import threading
import queue
from pyaml_env import parse_config, BaseConfig
from kafka import KafkaConsumer
from kafka.errors import KafkaError
import logging
from kafkaConsumerLib import MSKTokenProvider  # noqa: E402
import string
import random

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@not_keyword
def create_vehicle_calling_dv_lambda(vin: str = None, prefix: str = "VCDP") -> tuple:
    """
    This function invokes digital vehicle lambda to create a vehicle for the given VIN.
    It also checks that the vehicle was created successfully by validating the message in the Kafka topic.
    If VIN is not provided, it generates one using the given or default prefix.
    """
    if not vin:
        vin = generate_vin(prefix)

    q = queue.Queue()
    thread_function = kafka_consumer
    x = threading.Thread(
        target=thread_function,
        args=(q, 30),
        daemon=True,
    )
    x.start()
    x.join(20)
    logger.info("Thread started")
    if x.is_alive():
        logger.info("Thread is alive")
        try:
            # Invoke lambda
            random_vin, response = invoke_lambda(vin)
            logger.info(response)
        except Exception as e:
            logger.error(f"Error in create_vehicle_dv_lambda: {e}")
    else:
        logger.warning("Thread is not alive")

    result = None  # Initialize result
    try:
        result = q.get(timeout=20)  # Wait for up to 20 seconds
        if result is None:
            logger.error("Error occurred in Kafka consumer")
        else:
            logger.info("Values in the topic")
            logger.info(str(result.value.decode("utf-8")))
    except queue.Empty:
        logger.warning("No messages received within the timeout period")

    return json.loads(str(result.value.decode("utf-8")))["vin"], random_vin


@not_keyword
def invoke_lambda(vin: str) -> tuple:
    with open("../jsonschema/create-vehicle-payload.json") as f:
        data = json.load(f)
    logger.info(f"Creating vehicle with VIN: {vin}")
    data["vin"] = vin
    json_payload = json.dumps(data)
    client = boto3.client("lambda")
    response = client.invoke(
        FunctionName="vehicle-resync-lambda-VehicleResyncFunction-hJzvpFGiJ4pE", InvocationType="RequestResponse", Payload=json_payload
    )
    response_payload = json.loads(response["Payload"].read())
    return vin, json.dumps(response_payload)


@not_keyword
def kafka_consumer(q: queue.Queue, timeout_s: int) -> None:
    tp = MSKTokenProvider()
    consumer_timeout_ms = timeout_s * 1000
    clientid = "e2e-automated-test-query-paak"
    system_var_file = "../variables/system.yml"
    system_vars = BaseConfig(parse_config(system_var_file))
    topic = getattr(system_vars, "kafka-topic-vehicle-registration")
    bootstrap_servers = getattr(system_vars, "kafka-bootstrap-servers")

    try:
        consumer = KafkaConsumer(
            topic,
            client_id=clientid,
            bootstrap_servers=bootstrap_servers,
            security_protocol="SASL_SSL",
            sasl_mechanism="OAUTHBEARER",
            sasl_oauth_token_provider=tp,
            consumer_timeout_ms=consumer_timeout_ms,
        )
        logger.info("Kafka consumer created")
    except KafkaError as ke:
        logger.error(f"Kafka error: {ke}")
        q.put(None)  # Put None to indicate an error
        return

    if consumer != -1:
        logger.info("Connected to Kafka")
        try:
            for message in consumer:
                logger.info(str(message.value))
                q.put(message)
            # Poll for new messages
            consumer.poll(timeout_ms=consumer_timeout_ms)
        finally:
            consumer.close()
            logger.info("Kafka consumer closed")


# Function to generate a random VIN with optional prefix
def generate_vin(prefix: str = "VCDP") -> str:
    vin_length = 13
    vin_chars = string.ascii_uppercase.replace("I", "").replace("O", "").replace("Q", "") + string.digits
    suffix = "".join(random.choices(vin_chars, k=vin_length))
    return prefix + suffix


def main() -> None:
    vin = "VCDPM82633A004352"  # Example VIN
    create_vehicle_calling_dv_lambda(vin)


if __name__ == "__main__":
    main()
