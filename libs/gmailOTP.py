# import the required libraries
from googleapiclient.discovery import build
from robot.api.deco import keyword
import pickle
import os.path
import time
import base64
from retry import retry
import html_to_json

# Define the SCOPES. If modifying it, delete the token.pickle file.
SCOPES = ["https://www.googleapis.com/auth/gmail.modify"]


@retry(exceptions=Exception, tries=5, delay=3, backoff=1)
def GetOneTimePasscode():
    allMessages, service = ConnectToGmail()
    oneTimePassword = GetOneTimePasscodeFromEmail(allMessages, service)
    return oneTimePassword


@retry(exceptions=Exception, tries=5, delay=3, backoff=1)
def clearDownGmailInbox():
    allMessages, service = ConnectToGmail()
    DeleteAllMessages(allMessages, service)


@keyword("Get delete oneapp account email")
def getDeleteOneappAccountEmail():
    allMessages, service = ConnectToGmail()
    deleteAccoutEmail = getDeleteAccountEmailFromGmail(service)
    return deleteAccoutEmail


def ConnectToGmail():
    print("Connect to Gmail")
    # Variable creds will store the user access token.
    # If no valid token found, we will create one.
    creds = None

    # The file token.pickle contains the user access token.
    # Check if it exists
    if os.path.exists("../../token_pickle"):
        # Read the token from the file and store it in the variable creds
        with open("../../token_pickle", "rb") as token:
            creds = pickle.load(token)  # nosemgrep

        # Save the access token in token.pickle file for the next run
        with open("../../token_pickle", "wb") as token:
            pickle.dump(creds, token)  # nosemgrep

    # Connect to the Gmail API
    service = build("gmail", "v1", credentials=creds)

    # request a list of all the messages
    result = service.users().messages().list(userId=os.environ["gmail_FR_email"], maxResults=10).execute()
    allMessages = result.get("messages")
    return allMessages, service
    # messages is a list of dictionaries where each dictionary contains a message id.


def GetOneTimePasscodeFromEmail(allMessages, service):
    # iterate through all the messages
    try:
        for msg in allMessages:
            # Get the message from its id
            txt = service.users().messages().get(userId=os.environ["gmail_FR_email"], id=msg["id"]).execute()

            # Get value of 'payload' from dictionary 'txt'
            payload = txt["payload"]

            # The Body of the message is in Encrypted format. So, we have to decode it.
            # Get the data and decode it with base 64 decoder.
            responseBody = payload.get("body")
            data = responseBody["data"]
            data = data.replace("-", "+").replace("_", "/")
            decoded_data = base64.b64decode(data)

            output_json = html_to_json.convert(decoded_data)

            oneTimePassword = output_json["html"][0]["body"][0]["table"][1]["tr"][0]["td"][0]["p"][0]["b"][0]["_value"]

            return oneTimePassword
    except Exception:
        ("inbox is empty retry")


def DeleteAllMessages(allMessages, service):
    # iterate through all the messages
    try:
        for msg in allMessages:
            # Get the message from its id
            service.users().messages().trash(userId=os.environ["gmail_FR_email"], id=msg["id"]).execute()
    except Exception:
        ("inbox is empty retry")


def getDeleteAccountEmailFromGmail(service):
    query = 'subject:"We have deleted your account"'

    max_retries = 6  # 1 minute total (10s x 6 attempts)
    retry_interval = 10  # 10 seconds between retries

    for attempt in range(max_retries):
        results = service.users().messages().list(userId=os.environ["gmail_FR_email"], q=query).execute()
        messages = results.get("messages", [])

        if messages:
            # Get the latest email
            message = service.users().messages().get(userId="me", id=messages[0]["id"]).execute()
            print("Email found!")
            print(message)
            return message

        print(f"Attempt {attempt + 1}: No delete email found. Retrying in {retry_interval} seconds...")
        time.sleep(retry_interval)

    raise Exception("Failed: No delete account email found after 1 minute.")
