import threading
import queue
import time
import json
import kafkaProducerLib
import kafkaConsumerLib
from robot.api.deco import keyword, not_keyword
from pyaml_env import BaseConfig, parse_config


__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"

systemVarFile = "../variables/system.yml"


@keyword("Publish Float Signal to Kafka")
def publish_float_signal_to_kafka(
    filename,
    value,
    topic,
    times_stamp_offset=0,
):
    try:
        value = int(value)
    except ValueError:
        print("doorLockStatus must be int got::" + str(value))
        return -1

    system_vars = BaseConfig(parse_config(systemVarFile))
    unique_id = getattr(system_vars, "unique_id")

    producer = kafkaProducerLib.connect_kafka_producer(5)

    print(filename)
    with open(filename) as f:
        digitalVeh = json.load(f)

    current_ts = round(time.time() * 1000) + times_stamp_offset

    for n in range(len(digitalVeh["data"])):
        digitalVeh["unique_id"] = unique_id
        digitalVeh["data"][n]["samples"][0]["timestamp_ms"] = current_ts
        digitalVeh["event_timestamp_ms"] = current_ts
        digitalVeh["data"][n]["samples"][0]["value"] = value

    print(digitalVeh)
    print(topic)

    ack = producer.send(topic, key=None, value=json.dumps(digitalVeh).encode("utf-8"))
    print(ack)
    producer.flush()

    return (ack, current_ts)


@not_keyword
def subscribe_to_topic_as_thread(unique_id, topic, timeout_s, queue):
    """
    Not a robot keyword.\n
    Function to be started as a thread.\n
    Used to consume a kafka topic whilst performing another action.\n
    Returns data in a queue\n\n

    Example:\n
        import threading, queue\n
        \n
        q = queue.Queue()\n
        thread_function = kafkaConsumerLib.SubscribeToTopic\n
        x = threading.Thread(target=thread_function, args=(vin, topic, timeout_s, q), daemon=True)\n
        x.start()\n
        time.sleep(3)\n
        # then send query file  \n
        statusCode = send_config(configId, vin, proxy)\n
        x.join()\n
        result = q.get()\n
    """
    consumer = kafkaConsumerLib.connect_kafka_consumer(topic, timeout_s)
    print(str(consumer))

    end_epoch_time = int(time.time()) + int(timeout_s)

    dataValue = "-1"
    if consumer != -1:
        # print(consumer.topics())
        for msg in consumer:
            msg_value = msg.value
            epoch_time = int(time.time())
            if end_epoch_time < epoch_time:
                print("timed out")
                break
            if msg_value["vehicle_id"] == unique_id:
                # print(data)

                msg_value = msg.value
                # print(msg_value)
                dataValue = msg_value
                queue.put(dataValue)
                break
    else:
        print("Failed to connect to kafka")

    queue.put(dataValue)


@keyword("Send CentralLockStatus to factory output kafka whilst checking one app shadow kafka")
def send_centrallockstatus_whilst_checking_one_app_shadow_kafka(timeout_s, central_lock_status):
    userConfig = BaseConfig(parse_config(systemVarFile))
    dpFactorytopic = userConfig.topic
    oneApptopic = userConfig.oneAppTopic

    system_vars = BaseConfig(parse_config(systemVarFile))
    unique_id = getattr(system_vars, "unique_id")

    q = queue.Queue()
    thread_function = subscribe_to_topic_as_thread
    x = threading.Thread(
        target=thread_function,
        args=(
            unique_id,
            oneApptopic,
            timeout_s,
            q,
        ),
        daemon=True,
    )
    x.start()
    x.join(2)
    # then send data to Data Product Factory output kafka topic
    dorrstatusjson = "../json/samplecentralock.json"
    ack, current_ts = publish_float_signal_to_kafka(
        dorrstatusjson,
        central_lock_status,
        dpFactorytopic,
    )
    x.join(0)
    result = q.get()
    print("thread result:: " + str(result))
    return result, current_ts
