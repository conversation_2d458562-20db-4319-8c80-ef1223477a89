import os
import requests
import json
import urllib.parse
from retry import retry
from pyaml_env import parse_config, BaseConfig
import re


VERIFIER = "ZpJiIM_G0SE9WlxzS69Cq0mQh8uyFaeEbILlW8tHs62SmEE6n7Nke0XJGx_F4OduTI4"
CODE_CHALLENGE = "j3wKnK2Fa_mc2tgdqa6GtUfCYjdWSA5S23JKTTtPF8Y"
CONTENT_TYPE = "application/json"
PROTOCOL = "https://"
COOKIE_CONTENTS = "INGRESSCOOKIE=1691764078.611.425.479744|b5dbf05c569e469aed31cafd430f2153; uule=rBAMyWTWRW3BCAGpAwMGAg==; lbcookie=01"
STEP_ONE_FAILURE_MESSAGE = "Failed trying to authenticate user, couldnt find <PERSON>O Token (Step 1)"

system_vars = "not set"
url = "not set"
headers = {"Content-Type": CONTENT_TYPE}


def setup_globals(calledAsBinaryFile):
    # binary doesn't like relative paths and have different paths to the robot tests
    global url, systemVarFile, customerLandUsername, customerLandRoute, authDataPassword, system_vars
    if calledAsBinaryFile:
        systemVarFile = os.path.abspath(os.path.join(os.path.dirname(__file__), "variables/system.yml"))
        customerLandUsername = os.path.abspath(os.path.join(os.path.dirname(__file__), "forgeRockjson/customerLandingUsernamePayload.json"))
        customerLandRoute = os.path.abspath(os.path.join(os.path.dirname(__file__), "forgeRockjson/customerLandingRoutePayload.json"))
        authDataPassword = os.path.abspath(os.path.join(os.path.dirname(__file__), "forgeRockjson/auth-payloadPassword.json"))
    else:
        systemVarFile = "../variables/system.yml"
        customerLandUsername = "../forgeRockjson/customerLandingUsernamePayload.json"
        customerLandRoute = "../forgeRockjson/customerLandingRoutePayload.json"
        authDataPassword = "../forgeRockjson/auth-payloadPassword.json"

    system_vars = BaseConfig(parse_config(systemVarFile))
    IAM_HOST = getattr(system_vars, "IAM_HOST")
    url = (
        PROTOCOL
        + IAM_HOST
        + "/gateway/json/realms/root/realms/customer/authenticate?service=main-landing&authIndexType=service&authIndexValue=main-landing"
    )


@retry(exceptions=Exception, tries=5, backoff=1)
def get_token(USER_EMAIL, USER_PASSWORD, ALT_SCOPE="", calledAsBinaryFile=False):
    setup_globals(calledAsBinaryFile)
    landingPageAuthId = get_landing_page_token()
    authId1 = post_customer_landing_username(USER_EMAIL, landingPageAuthId)
    authId2 = post_customer_landing_route(authId1)
    tokenId = password_post(USER_PASSWORD, authId2)
    AUTHZ_CODE = get_auth_code(tokenId, ALT_SCOPE)
    access_token = get_access_token(AUTHZ_CODE)
    # print(access_token)
    return access_token


# 1. Landing page authId
def get_landing_page_token():
    response = requests.request("POST", url=url, data=None, allow_redirects=True)
    landingPageResp = json.loads(response.text)
    landingPageAuthId = landingPageResp["authId"]
    return landingPageAuthId


# 2.
def post_customer_landing_username(USER_EMAIL, landingPageAuthId):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        openJsonData = open(customerLandUsername, "r")
        readJsonData = openJsonData.read()
        jsonRequestBody = json.loads(readJsonData)
        headers = {
            "Content-Type": CONTENT_TYPE,
            "Cookie": COOKIE_CONTENTS,
        }

        jsonRequestBody["authId"] = landingPageAuthId
        jsonRequestBody["callbacks"][1]["input"][0]["value"] = USER_EMAIL

        jsonRequestBody.update(jsonRequestBody)
        payload2 = json.dumps(jsonRequestBody)

        response2 = requests.request("POST", url, headers=headers, data=payload2, allow_redirects=True)
        # print(response2)
        x = json.loads(response2.text)
        authId1 = x["authId"]
        return authId1
    except KeyError as e:
        print(e)
        print(url, payload2, response2)
        print(STEP_ONE_FAILURE_MESSAGE)


# 3.
def post_customer_landing_route(authId1):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        openJsonData = open(customerLandRoute, "r")
        readJsonData = openJsonData.read()
        jsonRequestBody = json.loads(readJsonData)
        headers = {
            "Content-Type": CONTENT_TYPE,
            "Cookie": COOKIE_CONTENTS,
        }

        jsonRequestBody["authId"] = authId1

        jsonRequestBody.update(jsonRequestBody)
        payload2 = json.dumps(jsonRequestBody)

        response2 = requests.request("POST", url, headers=headers, data=payload2, allow_redirects=True)
        # print(response2)
        x = json.loads(response2.text)
        authId2 = x["authId"]
        return authId2
    except KeyError as e:
        print(e)
        print(url, payload2, response2)
        print(STEP_ONE_FAILURE_MESSAGE)


def password_post(USER_PASSWORD, authId2):
    try:
        # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
        openJsonData = open(authDataPassword, "r")
        readJsonData = openJsonData.read()
        jsonRequestBody = json.loads(readJsonData)
        headers = {
            "Content-Type": CONTENT_TYPE,
            "Cookie": COOKIE_CONTENTS,
        }

        jsonRequestBody["authId"] = authId2
        jsonRequestBody["callbacks"][0]["input"][0]["value"] = USER_PASSWORD

        jsonRequestBody.update(jsonRequestBody)
        payload2 = json.dumps(jsonRequestBody)

        response2 = requests.request("POST", url, headers=headers, data=payload2, allow_redirects=True)
        # print(response2)
        z = json.loads(response2.text)
        tokenId = z["tokenId"]
        return tokenId
    except KeyError as e:
        print(e)
        print(url, payload2, response2)
        print(STEP_ONE_FAILURE_MESSAGE)


def get_auth_code(tokenId, ALT_SCOPE):
    IAM_HOST = getattr(system_vars, "IAM_HOST")
    CLIENT_ID = getattr(system_vars, "CLIENT_ID")
    SCOPE = getattr(system_vars, "SCOPE") if not ALT_SCOPE else ALT_SCOPE
    CODE_CHALLENGE_METHOD = getattr(system_vars, "CODE_CHALLENGE_METHOD")
    REDIRECT_URI = getattr(system_vars, "REDIRECT_URI")
    try:
        # nosemgrep: python.lang.security.audit.httpsconnection-detected.httpsconnection-detected
        payload = ""
        url = (
            PROTOCOL
            + IAM_HOST
            + "/gateway/oauth2/realms/root/realms/customer/authorize?redirect_uri="
            + urllib.parse.quote_plus(REDIRECT_URI)
            + "&client_id="
            + urllib.parse.quote_plus(CLIENT_ID)
            + "&response_type=code&scope="
            + urllib.parse.quote_plus(SCOPE)
            + "&code_challenge="
            + urllib.parse.quote_plus(CODE_CHALLENGE)
            + "&code_challenge_method="
            + urllib.parse.quote_plus(CODE_CHALLENGE_METHOD)
            + "&state=MjAzMTA0NDczNjE0MTExMDEwOTExMzQxMTIyNDEyMzIyMDk1MTc2MTQ3&decision=allow&csrf="
            + urllib.parse.quote_plus(tokenId)
        )
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Cookie": "SSOSession=" + tokenId,
        }
        response = requests.request("GET", url, data=payload, headers=headers, allow_redirects=False)
        responseHeaders = response.headers
        # print(response.headers)
        locationHeader = responseHeaders["Location"]
        # print(locationHeader)
        AUTHZ_CODE = locationHeader
        regex = r"(?<=code=).*(?=\&iss)"  # noqa:W605
        reg = re.search(regex, AUTHZ_CODE)
        # print(reg)
        AUTHZ_CODE = reg.group()
        return AUTHZ_CODE
    except KeyError as e:
        print(e)
        print("Failed trying to locate AuthCode (TokenID) (Step 2)")

        # 3. Get Access Token


def get_access_token(AUTHZ_CODE):
    IAM_HOST = getattr(system_vars, "IAM_HOST")
    CLIENT_ID = getattr(system_vars, "CLIENT_ID")
    REDIRECT_URI = getattr(system_vars, "REDIRECT_URI")
    try:
        # nosemgrep: python.lang.security.audit.httpsconnection-detected.httpsconnection-detected
        payload = (
            "grant_type=authorization_code&redirect_uri="
            + urllib.parse.quote_plus(REDIRECT_URI)
            + "&client_id="
            + urllib.parse.quote_plus(CLIENT_ID)
            + "&code="
            + urllib.parse.quote_plus(AUTHZ_CODE)
            + "&code_verifier="
            + urllib.parse.quote_plus(VERIFIER)
        )
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        url = PROTOCOL + IAM_HOST + "/gateway/oauth2/realms/root/realms/customer/access_token"
        response = requests.request("POST", url=url, data=payload, headers=headers)
        # print(response.text)
        x = json.loads(response.text)
        ACCESS_TOKEN = x["access_token"]
        print("\nACCESS_TOKEN::\n" + ACCESS_TOKEN + "\n\n")
        return ACCESS_TOKEN
    except KeyError as e:
        print(e)
        print("Failed trying to locate access token (Step 3)")
