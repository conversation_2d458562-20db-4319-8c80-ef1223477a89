# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: BeepFlashRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n!BeepFlashRawProtobufMessage.proto\x12\x16jlr.protobuf.beepflash\x1a\x10\x45numStatus.proto"\x18\n\x16SetBeepAndFlashRequest"J\n\x17SetBeepAndFlashResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"J\n\x17NotifyBeepAndFlashState\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatusB_\n>com.jaguarlandrover.commandandcontrolprotobuflibrary.beepflashB\x1b\x42\x65\x65pFlashRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "BeepFlashRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n>com.jaguarlandrover.commandandcontrolprotobuflibrary.beepflashB\033BeepFlashRawProtobufMessageP\001"
    _globals["_SETBEEPANDFLASHREQUEST"]._serialized_start = 79
    _globals["_SETBEEPANDFLASHREQUEST"]._serialized_end = 103
    _globals["_SETBEEPANDFLASHRESPONSE"]._serialized_start = 105
    _globals["_SETBEEPANDFLASHRESPONSE"]._serialized_end = 179
    _globals["_NOTIFYBEEPANDFLASHSTATE"]._serialized_start = 181
    _globals["_NOTIFYBEEPANDFLASHSTATE"]._serialized_end = 255
# @@protoc_insertion_point(module_scope)
