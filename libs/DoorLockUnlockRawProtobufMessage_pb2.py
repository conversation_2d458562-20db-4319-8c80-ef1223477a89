# flake8: noqa
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: DoorLockUnlockRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n&DoorLockUnlockRawProtobufMessage.proto\x12\x1bjlr.protobuf.doorlockunlock\x1a\x10\x45numStatus.proto"\xbd\x01\n\x1bSetApertureLockStateRequest\x12N\n\x12\x61perture_selection\x18\x01 \x01(\x0e\x32\x32.jlr.protobuf.doorlockunlock.EnumApertureSelection\x12N\n\x12\x61perture_operation\x18\x02 \x01(\x0e\x32\x32.jlr.protobuf.doorlockunlock.EnumApertureOperation"O\n\x1cSetApertureLockStateResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x1d\n\x1bGetApertureLockStateRequest"\xbd\x01\n\x1cGetApertureLockStateResponse\x12H\n\x0f\x61perture_status\x18\x01 \x01(\x0e\x32/.jlr.protobuf.doorlockunlock.EnumApertureStatus\x12"\n\x1aindividual_aperture_status\x18\x02 \x01(\r\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\xb8\x01\n\x17NotifyApertureLockState\x12H\n\x0f\x61perture_status\x18\x01 \x01(\x0e\x32/.jlr.protobuf.doorlockunlock.EnumApertureStatus\x12"\n\x1aindividual_aperture_status\x18\x02 \x01(\r\x12/\n\x06status\x18\x03 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\x90\x03\n\x15\x45numApertureSelection\x12\'\n#ENUM_APERTURE_SELECTION_UNSPECIFIED\x10\x00\x12\'\n#ENUM_APERTURE_SELECTION_DRIVER_DOOR\x10\x01\x12*\n&ENUM_APERTURE_SELECTION_PASSENGER_DOOR\x10\x02\x12,\n(ENUM_APERTURE_SELECTION_DRIVER_REAR_DOOR\x10\x03\x12/\n+ENUM_APERTURE_SELECTION_PASSENGER_REAR_DOOR\x10\x04\x12"\n\x1e\x45NUM_APERTURE_SELECTION_BONNET\x10\x05\x12$\n ENUM_APERTURE_SELECTION_TAILGATE\x10\x06\x12%\n!ENUM_APERTURE_SELECTION_ALL_DOORS\x10\x07\x12)\n%ENUM_APERTURE_SELECTION_ALL_APERTURES\x10\x08*\xba\x01\n\x15\x45numApertureOperation\x12\'\n#ENUM_APERTURE_OPERATION_UNSPECIFIED\x10\x00\x12)\n%ENUM_APERTURE_OPERATION_EXTERNAL_LOCK\x10\x01\x12)\n%ENUM_APERTURE_OPERATION_INTERNAL_LOCK\x10\x02\x12"\n\x1e\x45NUM_APERTURE_OPERATION_UNLOCK\x10\x03*\xfe\x08\n\x12\x45numApertureStatus\x12$\n ENUM_APERTURE_STATUS_UNSPECIFIED\x10\x00\x12\x38\n4ENUM_APERTURE_STATUS_ALL_APERTURES_EXTERIOR_UNLOCKED\x10\x01\x12\x36\n2ENUM_APERTURE_STATUS_ALL_APERTURES_EXTERIOR_LOCKED\x10\x02\x12\x38\n4ENUM_APERTURE_STATUS_ALL_APERTURES_INTERIOR_UNLOCKED\x10\x03\x12\x36\n2ENUM_APERTURE_STATUS_ALL_APERTURES_INTERIOR_LOCKED\x10\x04\x12\x33\n/ENUM_APERTURE_STATUS_TAILGATE_EXTERIOR_UNLOCKED\x10\x05\x12\x31\n-ENUM_APERTURE_STATUS_TAILGATE_EXTERIOR_LOCKED\x10\x06\x12\x33\n/ENUM_APERTURE_STATUS_TAILGATE_INTERIOR_UNLOCKED\x10\x07\x12\x31\n-ENUM_APERTURE_STATUS_TAILGATE_INTERIOR_LOCKED\x10\x08\x12\x36\n2ENUM_APERTURE_STATUS_DOORS_FULLY_EXTERIOR_UNLOCKED\x10\t\x12\x34\n0ENUM_APERTURE_STATUS_DOORS_FULLY_EXTERIOR_LOCKED\x10\n\x12\x36\n2ENUM_APERTURE_STATUS_DOORS_FULLY_INTERIOR_UNLOCKED\x10\x0b\x12\x34\n0ENUM_APERTURE_STATUS_DOORS_FULLY_INTERIOR_LOCKED\x10\x0c\x12:\n6ENUM_APERTURE_STATUS_DOORS_PARTIALLY_EXTERIOR_UNLOCKED\x10\r\x12\x38\n4ENUM_APERTURE_STATUS_DOORS_PARTIALLY_EXTERIOR_LOCKED\x10\x0e\x12:\n6ENUM_APERTURE_STATUS_DOORS_PARTIALLY_INTERIOR_UNLOCKED\x10\x0f\x12\x38\n4ENUM_APERTURE_STATUS_DOORS_PARTIALLY_INTERIOR_LOCKED\x10\x10\x12\x31\n-ENUM_APERTURE_STATUS_BONNET_EXTERIOR_UNLOCKED\x10\x11\x12/\n+ENUM_APERTURE_STATUS_BONNET_EXTERIOR_LOCKED\x10\x12\x12\x31\n-ENUM_APERTURE_STATUS_BONNET_INTERIOR_UNLOCKED\x10\x13\x12/\n+ENUM_APERTURE_STATUS_BONNET_INTERIOR_LOCKED\x10\x14\x42i\nCcom.jaguarlandrover.commandandcontrolprotobuflibrary.doorlockunlockB DoorLockUnlockRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "DoorLockUnlockRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\nCcom.jaguarlandrover.commandandcontrolprotobuflibrary.doorlockunlockB DoorLockUnlockRawProtobufMessageP\001"
    _globals["_ENUMAPERTURESELECTION"]._serialized_start = 773
    _globals["_ENUMAPERTURESELECTION"]._serialized_end = 1173
    _globals["_ENUMAPERTUREOPERATION"]._serialized_start = 1176
    _globals["_ENUMAPERTUREOPERATION"]._serialized_end = 1362
    _globals["_ENUMAPERTURESTATUS"]._serialized_start = 1365
    _globals["_ENUMAPERTURESTATUS"]._serialized_end = 2515
    _globals["_SETAPERTURELOCKSTATEREQUEST"]._serialized_start = 90
    _globals["_SETAPERTURELOCKSTATEREQUEST"]._serialized_end = 279
    _globals["_SETAPERTURELOCKSTATERESPONSE"]._serialized_start = 281
    _globals["_SETAPERTURELOCKSTATERESPONSE"]._serialized_end = 360
    _globals["_GETAPERTURELOCKSTATEREQUEST"]._serialized_start = 362
    _globals["_GETAPERTURELOCKSTATEREQUEST"]._serialized_end = 391
    _globals["_GETAPERTURELOCKSTATERESPONSE"]._serialized_start = 394
    _globals["_GETAPERTURELOCKSTATERESPONSE"]._serialized_end = 583
    _globals["_NOTIFYAPERTURELOCKSTATE"]._serialized_start = 586
    _globals["_NOTIFYAPERTURELOCKSTATE"]._serialized_end = 770
# @@protoc_insertion_point(module_scope)
