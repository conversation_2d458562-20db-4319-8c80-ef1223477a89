import json
import kafkaConsumerLib


# **** WIP ****
# Subscribes to kafka topic
#
# for each message check the value against the limits in the signal dictonary
#

# ignore RAW data
ignoreQueryList = ["Q9901010", "Q9102004"]


def get_signal_min_max(data, signal):
    for i in data["dataDefinition"]:
        if i["id"] == signal:
            # print(i['id'])
            length = i["length"]
            scale = i["scale"]
            offset = i["offset"]
            break

    min_value = offset
    max_value = (((1 << length) - 1) * scale) + offset
    # print('length:: ' + str(length) + ',  scale:: ' + str(scale) + ',  offset:: ' + str(offset) + ',  min:: ' + str(min) + ',  max:: ' + str(max))
    return [min_value, max_value, length, scale, offset]


def process_message(topicMessage, bothPassAndFail=False):
    dataSample = topicMessage["data"]
    dataunique_id = topicMessage["unique_id"]
    dataQueryId = topicMessage["query_id"]

    for d in dataSample:
        if dataQueryId in ignoreQueryList:
            print("Ignoring query:: " + dataQueryId)
            break
        result = "PASSED"
        data_id = d["data_id"]
        if data_id[0] == "S":
            signal_range = get_signal_min_max(data, data_id)
        else:
            print("Skip Diagnostic Signal:: " + data_id)
            print("\n")
            break

        # does it use calulations?
        if "calc" in d:
            print("it has calcs!!")
            valueMin = d["calc"]["min"]["value"]
            valueMax = d["calc"]["max"]["value"]
            if not (signal_range[0] <= valueMin <= signal_range[1]):
                result = "FAILED"
            if not (signal_range[0] <= valueMax <= signal_range[1]):
                result = "FAILED"
        else:
            value = d["samples"][0]["value"]
            if not (signal_range[0] <= value <= signal_range[1]):
                result = "FAILED"

        if result == "FAILED" or bothPassAndFail is True:
            print("\n")
            print(dataQueryId)
            print(d)
            print(result)
            print(dataunique_id)
            print("[min, max, length, scale, offset]")
            print(str(signal_range))


#   Main   #


# topic = "LIVE.data-product-factory.network-test-output"
# topic = "LIVE.data-product-factory.inflation-test-output"
topic = "LIVE.data-product-factory.input"

# unique_id = "3657e9bd-3a5b-4155-ba12-42dd424e2a30"   # TSTLRGBL6N3PR2189  (dev)
# unique_id = "488b661c-c0c2-4c84-9c13-d10cedc21233" # MCRTESTVIN0000007 (SIL test.. )
# unique_id = "8b733514-5a23-459e-a36a-484605b1c87a"   # TSTLRGBL6N3PR2053  (preprod)
# unique_id = "f538f89e-ab7c-4e61-bfb0-856acb6c5e6b" # TSTLRGBVDNRIG7110 ( ash rig, pre prod)
# unique_id = "a5065ba0-c0ab-422e-be2d-ba89c69fc2ac"  # lornes lab rig - pre prod
# unique_id = "8b98a6f1-3ed7-4f2d-9d9d-1fa923d51257" # our l663
unique_id = "8d8b860c-6d17-445c-a566-47f5e093bfb1"  # our L460
# unique_id = ""   # leave blank for any vehicle


f = open("/home/<USER>/Projects/vcdp-e2e-test-hw-inloop/configs/config-dicts/signal_dict.json")
data = json.load(f)

# testData = '{"unique_id": "8b733514-5a23-459e-a36a-484605b1c87a", "query_id": "Q9003001", "data": [{"data_id": "S00668", "samples": [{"timestamp_ms": 1666076395513, "type": "DATA_FLOAT", "value": 44}]}], "event_timestamp_ms": 1666076397022, "fleet_id": ""}'  # noqa: E501
# testData = '{"unique_id": "8b98a6f1-3ed7-4f2d-9d9d-1fa923d51257", "query_id": "Q3", "data": [{"data_id": "S00973", "calc": {"duration_ms": 4913, "min": {"type": "DATA_FLOAT", "value": -100}, "max": {"type": "DATA_FLOAT", "value": 100}, "end_time_ms": 1666095783483}, "samples": []}], "event_timestamp_ms": 1666095783195, "fleet_id": ""}'  # noqa: E501
# topicMessage = json.loads(testData)

# from file
# testData = open("/home/<USER>/Projects/vcdp-e2e-test-hw-inloop/robot/sandpit/L663.json")
# topicMessage = json.load(testData)
# print(topicMessage)
# processMessage(topicMessage, False)

consumer = kafkaConsumerLib.connect_kafka_consumer(topic, 20)

for msg in consumer:
    msg_value = msg.value
    if msg_value["unique_id"] == unique_id:
        # print(msg.value)
        process_message(msg.value, False)
