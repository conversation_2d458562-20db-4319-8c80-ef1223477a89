# flake8: noqa
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ClimateRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1f\x43limateRawProtobufMessage.proto\x12\x18jlr.protobuf.hvacclimate\x1a\x10\x45numStatus.proto"\x9b\x01\n\x1dSetVehiclePreconditionRequest\x12Q\n\x14precondition_request\x18\x01 \x01(\x0e\x32\x33.jlr.protobuf.hvacclimate.EnumPreconditionOperation\x12\'\n\x1fprecondition_target_temperature\x18\x02 \x01(\r"Q\n\x1eSetVehiclePreconditionResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"$\n"GetVehiclePreconditionStateRequest"\x8f\x02\n#GetVehiclePreconditionStateResponse\x12P\n\x11precondition_mode\x18\x01 \x01(\x0e\x32\x35.jlr.protobuf.hvacclimate.EnumPreConditionCurrentMode\x12M\n\x13precondition_status\x18\x02 \x01(\x0e\x32\x30.jlr.protobuf.hvacclimate.EnumPreConditionStatus\x12\x16\n\x0etime_remaining\x18\x03 \x01(\r\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\x8a\x02\n\x1eNotifyVehiclePreconditionState\x12P\n\x11precondition_mode\x18\x01 \x01(\x0e\x32\x35.jlr.protobuf.hvacclimate.EnumPreConditionCurrentMode\x12M\n\x13precondition_status\x18\x02 \x01(\x0e\x32\x30.jlr.protobuf.hvacclimate.EnumPreConditionStatus\x12\x16\n\x0etime_remaining\x18\x03 \x01(\r\x12/\n\x06status\x18\x04 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\x95\x01\n\x19\x45numPreconditionOperation\x12+\n\'ENUM_PRECONDITION_OPERATION_UNSPECIFIED\x10\x00\x12%\n!ENUM_PRECONDITION_OPERATION_START\x10\x01\x12$\n ENUM_PRECONDITION_OPERATION_STOP\x10\x02*\xd2\x01\n\x1b\x45numPreConditionCurrentMode\x12.\n*ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED\x10\x00\x12+\n\'ENUM_PRECONDITION_CURRENT_MODE_INACTIVE\x10\x01\x12,\n(ENUM_PRECONDITION_CURRENT_MODE_IMMEDIATE\x10\x02\x12(\n$ENUM_PRECONDITION_CURRENT_MODE_TIMED\x10\x03*\xf3\x05\n\x16\x45numPreConditionStatus\x12(\n$ENUM_PRECONDITION_STATUS_UNSPECIFIED\x10\x00\x12%\n!ENUM_PRECONDITION_STATUS_COMPLETE\x10\x01\x12(\n$ENUM_PRECONDITION_STATUS_IN_PROGRESS\x10\x02\x12\x35\n1ENUM_PRECONDITION_STATUS_WARN_USER_STOP_REQUESTED\x10\x03\x12.\n*ENUM_PRECONDITION_STATUS_ERROR_LOW_BATTERY\x10\x04\x12\x35\n1ENUM_PRECONDITION_STATUS_ERROR_VEHICLE_NOT_SECURE\x10\x05\x12;\n7ENUM_PRECONDITION_STATUS_ERROR_CLEANING_CYCLE_EXHAUSTED\x10\x06\x12/\n+ENUM_PRECONDITION_STATUS_ERROR_SYSTEM_FAULT\x10\x07\x12;\n7ENUM_PRECONDITION_STATUS_ERROR_VEHICLE_POWER_TRANSITION\x10\x08\x12\x37\n3ENUM_PRECONDITION_STATUS_ERROR_OTHER_FEATURE_ACTIVE\x10\t\x12\x31\n-ENUM_PRECONDITION_STATUS_ERROR_CRASH_DETECTED\x10\n\x12\x33\n/ENUM_PRECONDITION_STATUS_ERROR_EXTERNAL_FAILURE\x10\x0b\x12:\n6ENUM_PRECONDITION_STATUS_ERROR_CRITICAL_SERVICE_ACTIVE\x10\x0c\x12\x38\n4ENUM_PRECONDITION_STATUS_ERROR_SW_UPDATE_IN_PROGRESS\x10\rBc\<EMAIL>\x1dHVACClimateRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "ClimateRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\<EMAIL>\035HVACClimateRawProtobufMessageP\001"
    _globals["_ENUMPRECONDITIONOPERATION"]._serialized_start = 902
    _globals["_ENUMPRECONDITIONOPERATION"]._serialized_end = 1051
    _globals["_ENUMPRECONDITIONCURRENTMODE"]._serialized_start = 1054
    _globals["_ENUMPRECONDITIONCURRENTMODE"]._serialized_end = 1264
    _globals["_ENUMPRECONDITIONSTATUS"]._serialized_start = 1267
    _globals["_ENUMPRECONDITIONSTATUS"]._serialized_end = 2022
    _globals["_SETVEHICLEPRECONDITIONREQUEST"]._serialized_start = 80
    _globals["_SETVEHICLEPRECONDITIONREQUEST"]._serialized_end = 235
    _globals["_SETVEHICLEPRECONDITIONRESPONSE"]._serialized_start = 237
    _globals["_SETVEHICLEPRECONDITIONRESPONSE"]._serialized_end = 318
    _globals["_GETVEHICLEPRECONDITIONSTATEREQUEST"]._serialized_start = 320
    _globals["_GETVEHICLEPRECONDITIONSTATEREQUEST"]._serialized_end = 356
    _globals["_GETVEHICLEPRECONDITIONSTATERESPONSE"]._serialized_start = 359
    _globals["_GETVEHICLEPRECONDITIONSTATERESPONSE"]._serialized_end = 630
    _globals["_NOTIFYVEHICLEPRECONDITIONSTATE"]._serialized_start = 633
    _globals["_NOTIFYVEHICLEPRECONDITIONSTATE"]._serialized_end = 899
# @@protoc_insertion_point(module_scope)
