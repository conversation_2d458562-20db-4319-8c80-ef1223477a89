#!/usr/bin python3

import json
import time
from robot.api.deco import keyword, not_keyword
import OBGdiscoveryService
import vcdpHiveBroker

SUBSCRIBING_TO_TOPIC_LOG = "Subscribing to topic:: "
AUTO_SUBSCRIPTION_FILE = "../json-request-data/service-gateway-manager/auto-subscription-file.json"
SERVICE_MAPPING_DICTIONAY_EXPECTED = "../json-request-data/service-gateway-manager/service-mapping-dictionary-expected.json"
SERVICE_MAPPING_DICTIONAY_ACTUAL = "../json-request-data/service-gateway-manager/service-mapping-dictionary-actual.json"
OBG_SUB_DATA_SUB_DATA = "../json-request-data/obg-HiveBroker-subscibe-data/obg-subscibed-data-for-validation.json"


@not_keyword
def broker_connection(SubscribeTopic, uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, writeToFile):
    client = vcdpHiveBroker.connect_to_broker(uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, True, True, "")
    if writeToFile is False:
        client.on_message = vcdpHiveBroker.on_message
    else:
        client.on_message = vcdpHiveBroker.on_message_write_to_file
    client.loop_start()
    print(SUBSCRIBING_TO_TOPIC_LOG + SubscribeTopic)
    client.subscribe(SubscribeTopic, qos=1)
    time.sleep(timeToSubs)
    client.loop_stop()
    client.disconnect()


@not_keyword
def auto_sub_file_transform(uniqueId, SubscribeTopic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False):
    broker_connection(SubscribeTopic, uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, writeToFile)

    file = open(AUTO_SUBSCRIPTION_FILE, "w")
    responsestr = str(vcdpHiveBroker.SubsData)
    res = responsestr.strip("b'")
    file.write(res)
    file.close()

    OBGdiscoveryService.file_transform(AUTO_SUBSCRIPTION_FILE)

    openJsonFile = open(
        OBG_SUB_DATA_SUB_DATA,
        "r",
    )
    readJsonData = openJsonFile.read()
    jsonRequestBody = json.loads(readJsonData)
    return jsonRequestBody


@keyword("Subscribe to hive topic for SGM service mapping dictionary")
def service_mapping_dictionary(
    uniqueId, SubscribeTopic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False
):
    broker_connection(SubscribeTopic, uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, writeToFile)
    file = open(SERVICE_MAPPING_DICTIONAY_ACTUAL, "w")
    responsestr = str(vcdpHiveBroker.SubsData)
    res = responsestr.strip("b'")
    file.write(res)
    file.close()

    OBGdiscoveryService.file_transform(SERVICE_MAPPING_DICTIONAY_ACTUAL)

    openJsonFile1 = open(
        SERVICE_MAPPING_DICTIONAY_EXPECTED,
        "r",
    )
    readJsonData1 = openJsonFile1.read()
    ExpectedFile = json.loads(readJsonData1)

    openJsonFile2 = open(
        OBG_SUB_DATA_SUB_DATA,
        "r",
    )
    readJsonData2 = openJsonFile2.read()
    ActualFile = json.loads(readJsonData2)

    # Convert JSON objects to string
    str1 = str(ExpectedFile)
    str2 = str(ActualFile)
    if str1 == str2:
        response = "VALID SERVICE MAPPING DICTIONARY"
    else:
        response = "INVALID SERVICE MAPPING DICTIONARY"

    return response


@not_keyword
def validate_response(autosubcription_id, json_data):
    if autosubcription_id in json_data:
        response = "VALID AUTO SUBSCRIPTION FILE"
    else:
        response = "INVALID AUTO SUBSCRIPTION FILE"
    return response


@not_keyword
def check_permissions(permission, json_data, permissions, autosubcription_id):
    if permission in permissions:
        response = validate_response(autosubcription_id, json_data)
    else:
        response = "PERMISSION DOES NOT MATCH WITH SERVICE"
    return response


@keyword("Subscribe to hive topic for SGM autosubscription file")
def auto_subscription_file(
    autosubscript,
    permission,
    unique_id,
    subscribe_topic,
    broker_user,
    broker_client_secret,
    certfile,
    ca_certs,
    keyfile,
    time_to_subs=0.5,
    write_to_file=False,
):
    json_request_body = auto_sub_file_transform(
        unique_id, subscribe_topic, broker_user, broker_client_secret, certfile, ca_certs, keyfile, time_to_subs, write_to_file
    )
    jsonData = str(json_request_body)

    charge_state_permissions = ["singlePerm", "AAppUsage", "SmartCharging"]
    ev_range_permissions = ["singlePerm", "AAppUsage", "SmartCharging"]
    charge_door_status_permissions = ["singlePerm", "AAppUsage", "SmartCharging"]
    aperture_lock_status_permissions = ["singlePerm", "AAppUsage"]
    bonnent_status_permissions = ["singlePerm", "AAppUsage"]
    cabin_temp_status_permissions = ["singlePerm", "AAppUsage"]
    window_status_permissions = ["singlePerm", "AAppUsage"]
    vehicle_state_permissions = ["singlePerm", "AAppUsage"]
    trip_computer_reset_permissions = ["geoTrip"]
    trip_compuer_bank_data_permissions = ["geoTrip"]
    vehicle_position_permissions_permissions = ["geoTrip"]

    match autosubscript:
        case "charging_service.NotifyChargeState":
            response = check_permissions(permission, jsonData, charge_state_permissions, "OZcWjo5nxBa1ZUdu87R1kA")
        case "ev_range_service.NotifyEVRange":
            response = check_permissions(permission, jsonData, ev_range_permissions, "RRmpqHA02Kag0HC404VF9g")
        case "charging_service.NotifyChargeDoorStatus":
            response = check_permissions(permission, jsonData, charge_door_status_permissions, "N9uplK7fIWHrP4Y5HUUNpA")
        case "access_service.NotifyApertureLockState":
            response = check_permissions(permission, jsonData, aperture_lock_status_permissions, "Ksiey+5D0xB6qAMdDpmtmg")
        case "body_native_service.NotifyBonnetSecondaryLatchStatus":
            response = check_permissions(permission, jsonData, bonnent_status_permissions, "hvuUaElU9Bu07jWZ1sVkYg")
        case "climate_service.NotifyInCabinTemperatureStatus":
            response = check_permissions(permission, jsonData, cabin_temp_status_permissions, "zc67XXseY8zOPDanbjoFeQ")
        case "pps_visibility_service.NotifyWindowsStatus":
            response = check_permissions(permission, jsonData, window_status_permissions, "1WSQOU0xD9g4F9BR2BbRUQ")
        case "lifecycle_cvlc_service.NotifyVehicleState":
            response = check_permissions(permission, jsonData, vehicle_state_permissions, "bNsL3RP8dQ7eJamdC1/giQ")
        case "trip_computer.NotifyTripComputerReset":
            response = check_permissions(permission, jsonData, trip_computer_reset_permissions, "FNkz7e1WdsmVkjnu8aj6VA")
        case "trip_computer.NotifyTripComputerBankData":
            response = check_permissions(permission, jsonData, trip_compuer_bank_data_permissions, "ZmaZ8N04PhPTTJ/zWeOKpA")
        case "vehicle_positioning_service.NotifyVehiclePosition":
            response = check_permissions(permission, jsonData, vehicle_position_permissions_permissions, "BkkqNS6tqyGvCpwMJMp6SA")

    return response


@keyword("Subscribe to validate Service Mapping Dictionary after consent is changed on vehicle")
def sub_smp_topic(uniqueId, SubscribeTopic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False):
    broker_connection(SubscribeTopic, uniqueId, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, writeToFile)

    return vcdpHiveBroker.SubsData


@keyword("Subscribe to hive topic for SGM autosubscription file with no consent on vehicle")
def sub_asf_topic(uniqueId, SubscribeTopic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs=0.5, writeToFile=False):
    jsonRequestBody = auto_sub_file_transform(
        uniqueId, SubscribeTopic, brokerUser, brokerClientSecret, certfile, ca_certs, keyfile, timeToSubs, writeToFile
    )

    if jsonRequestBody["auto_subscribe"] == []:
        print("this is autosubscibe:", jsonRequestBody["auto_subscribe"])
        response = "NO AUTOSUBSCRIBE SERVICES"

    else:
        print("this is autosubscibe:", jsonRequestBody["auto_subscribe"])
        response = "AUTOSUBSCRIBE SERVICES"

    return response
