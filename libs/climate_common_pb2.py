# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: climate_common.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import seats_common_pb2 as seats__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x63limate_common.proto\x12\x0e\x63limate_common\x1a\x12seats_common.proto\"\xd2\x01\n\x14SeatClimateOperation\x12\x36\n\tseat_area\x18\x01 \x01(\x0e\x32#.climate_common.EnumSeatClimateArea\x12\x38\n\nseat_state\x18\x02 \x01(\x0e\x32$.climate_common.EnumSeatClimateState\x12H\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32(.climate_common.EnumSeatClimateIntensity\"\xf7\x01\n\x14SeatClimateZoneState\x12\x37\n\x0eseat_selection\x18\x01 \x01(\x0e\x32\x1f.seats_common.EnumSeatSelection\x12<\n\x0eseat_operation\x18\x02 \x03(\x0b\x32$.climate_common.SeatClimateOperation\x12M\n\x16seat_climate_intensity\x18\x03 \x01(\x0e\x32(.climate_common.EnumSeatClimateIntensityH\x00\x88\x01\x01\x42\x19\n\x17_seat_climate_intensity*\xb8\x04\n\x18\x45numSeatClimateIntensity\x12+\n\'ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED\x10\x00\x12#\n\x1f\x45NUM_SEAT_CLIMATE_INTENSITY_OFF\x10\x01\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1\x10\x02\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1\x10\x03\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2\x10\x04\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2\x10\x05\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3\x10\x06\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3\x10\x07\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4\x10\x08\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4\x10\t\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5\x10\n\x12,\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5\x10\x0b*\xa3\x01\n\x13\x45numSeatClimateArea\x12&\n\"ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_AREA_ALL\x10\x01\x12\"\n\x1e\x45NUM_SEAT_CLIMATE_AREA_CUSHION\x10\x02\x12 \n\x1c\x45NUM_SEAT_CLIMATE_AREA_SQUAB\x10\x03*\xa5\x01\n\x14\x45numSeatClimateState\x12\'\n#ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45NUM_SEAT_CLIMATE_STATE_ON\x10\x01\x12\x1f\n\x1b\x45NUM_SEAT_CLIMATE_STATE_OFF\x10\x02\x12#\n\x1f\x45NUM_SEAT_CLIMATE_STATE_INHIBIT\x10\x03*\xad\x01\n\x17\x45numHSWTemperatureLevel\x12*\n&ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED\x10\x00\x12\"\n\x1e\x45NUM_HSW_TEMPERATURE_LEVEL_OFF\x10\x01\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_1\x10\x02\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_2\x10\x03\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'climate_common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_start=524
  _globals['_ENUMSEATCLIMATEINTENSITY']._serialized_end=1092
  _globals['_ENUMSEATCLIMATEAREA']._serialized_start=1095
  _globals['_ENUMSEATCLIMATEAREA']._serialized_end=1258
  _globals['_ENUMSEATCLIMATESTATE']._serialized_start=1261
  _globals['_ENUMSEATCLIMATESTATE']._serialized_end=1426
  _globals['_ENUMHSWTEMPERATURELEVEL']._serialized_start=1429
  _globals['_ENUMHSWTEMPERATURELEVEL']._serialized_end=1602
  _globals['_SEATCLIMATEOPERATION']._serialized_start=61
  _globals['_SEATCLIMATEOPERATION']._serialized_end=271
  _globals['_SEATCLIMATEZONESTATE']._serialized_start=274
  _globals['_SEATCLIMATEZONESTATE']._serialized_end=521
# @@protoc_insertion_point(module_scope)
