import dataProductManager
import json
import yaml
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig
import re


YAML_FAIL_NAME = "../libs/signalQuery.yml"
DATA_PRODUCTS = ["Dp_105", "Dp_107", "Dp_108", "Dp_111", "Dp_112", "Dp_113", "Dp_115", "Dp_116", "Dp_117", "Dp_118", "Dp_119"]


@keyword("Get the VA signal Query lookup file name")
def getsignalQueryFileName():
    return YAML_FAIL_NAME


@not_keyword
def alreadyGotSignal(signalQueryIds, emitSignal, anotherQueryId):
    for n in signalQueryIds:
        if emitSignal == n[0]:
            n[1].append(anotherQueryId)
            return n


@not_keyword
def createSignalsQueryYml(dictSignalQueryIds):
    with open(YAML_FAIL_NAME, "w") as file:
        yaml.dump(dictSignalQueryIds, file)


@keyword("Create yaml of all va queries emitting va signals for all data products")
def getQidsForVaSignal(user, pw):
    """
    Get all the data products via the data product manager.
    Creates a yaml file with a list of queries for each signal

    Examples:
    | Create yaml of all va queries emitting va signals for all data products | configUser | configPw |

    Creates yaml as:
        SRV-BrakeFluidStatus-brakepad_wearlamp_req:
        - Q711804010
        - Q711804011
        - Q711804012
        - Q711804013
        SRV-WasherFluidStatus-washer_fluid_level_warning:
        - Q711804010
        - Q711804014
        - Q711804015
        - Q711804016

    """

    # allDataProducts = json.loads(dataProductManager.get_data_product_deltas(user, pw)[0])
    count = 0
    while count < 3:
        allDataProductsRes = dataProductManager.get_data_product_deltas(user, pw)
        print(allDataProductsRes[1])
        count = count + 1
        if allDataProductsRes[1] == 200:
            break

    allDataProducts = json.loads(allDataProductsRes[0])
    signalQueryIds = []
    for dp in allDataProducts:
        if dp["dataProductId"] in DATA_PRODUCTS:
            for query in dp["queries"]:
                for emitSignal in query["emit"]:
                    notNewSignal = alreadyGotSignal(signalQueryIds, emitSignal, query["id"])
                    if not notNewSignal:  # new signal
                        sigQueryId = emitSignal, [query["id"]]
                        signalQueryIds.append(sigQueryId)

    dictSignalQueryIds = dict(signalQueryIds)
    createSignalsQueryYml(dictSignalQueryIds)


@keyword("Create yaml of all va queries emitting va signals for a specific data product")
def getQidForSpecificDpForSignal(user, pw, dp):
    """
    Get the data product (dp) via the data product manager.
    Creates a yaml file with a list of queries for each signal for that data product.

    Examples:
    | Create yaml of all va queries emitting va signals for a specific data product | configUser | configPw | Dp_115 |

    """
    # dataProduct = json.loads(dataProductManager.get_data_product_by_id(user, pw, dp)[0])
    count = 0
    while count < 3:
        DataProductRes = dataProductManager.get_data_product_by_id(user, pw, dp)
        print(DataProductRes[1])
        count = count + 1
        if DataProductRes[1] == 200:
            break

    dataProduct = json.loads(DataProductRes[0])
    signalQueryIds = []
    for query in dataProduct["delta"]["queries"]:
        for emitSignal in query["emit"]:
            notNewSignal = alreadyGotSignal(signalQueryIds, emitSignal, query["id"])
            if not notNewSignal:
                sigQueryId = emitSignal, [query["id"]]
                signalQueryIds.append(sigQueryId)

    dictSignalQueryIds = dict(signalQueryIds)
    createSignalsQueryYml(dictSignalQueryIds)


@keyword("Get all queries for a va signal")
def getQueriesForSignalFromYmal(signal):
    """
    From signalQuery.yml, returns a list of all the Queries which emit the va signal.

    Data is looked up from the  signalQuery.yml created by either:
    "Get all query Ids for a VA signal"
    "Get query IDs for specific Data Product for signal"

    Examples:
    | ${queries}= | Get all queries for a va signal | VA-SIG-ChargingStatusDisp |

    """
    signalQueries = BaseConfig(parse_config(YAML_FAIL_NAME))
    try:
        queries = getattr(signalQueries, signal)
    except KeyError:
        print("VA Signal", signal, " Not Found!!")
        queries = ["NotFound"]

    return queries


@keyword("Get queries for specific data product for a va signal")
def getSpecifciDpQueryForSignalFromYmal(signal, dp):
    """
    From signalQuery.yml, returns a list of all the Queries which emit the va signal.
    Only returns the Queries for a specific data product (dp)

    Examples:
    | ${queries}= | Get all queries for a va signal | VA-SIG-ChargingStatusDisp | Dp_115 |
    """
    dpNumber = r"Q.{1}" + dp.lower().removeprefix("dp_")
    print(dpNumber)
    signalQueries = BaseConfig(parse_config(YAML_FAIL_NAME))
    queriesFiltered = []
    try:
        queries = getattr(signalQueries, signal)
    except KeyError:
        print("VA Signal", signal, " Not Found!!")
        queries = ["NotFound"]

    for q in queries:
        if re.match(dpNumber, q):
            queriesFiltered.append(q)
        queries = queriesFiltered
    return queries


def main() -> None:
    ccUSer = "xxxxx"
    ccPw = "xxxx"
    getQidsForVaSignal(ccUSer, ccPw)

    queries = getQueriesForSignalFromYmal("VA-SIG-EVRangeDisp")
    print(queries)

    queries = getSpecifciDpQueryForSignalFromYmal("VA-SIG-EVRangeDisp", "Dp_115")
    print(queries)
    queries = getSpecifciDpQueryForSignalFromYmal("VA-SIG-EVRangeDisp", "Dp_108")
    print(queries)

    getQidForSpecificDpForSignal(ccUSer, ccPw, "Dp_115")
    queries = getQueriesForSignalFromYmal("VA-SIG-EVRangeDisp")
    print(queries)


if __name__ == "__main__":
    main()
