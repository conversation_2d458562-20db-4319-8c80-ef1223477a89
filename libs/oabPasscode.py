from robot.api.deco import keyword
import commonUtils


@keyword("update passcode")
def update_passcode(user_email, user_password, new_passcode):

    endpoint = "https://oneapp.mab.pre-prod.jlr-vcdp.com/api/v1/users/me/passcode"

    response = commonUtils.send_put_request(
        user_email,
        user_password,
        endpoint,
        True,
        True,
        "PUT",
        "application/json",
        "Bearer ",
        {
            "passcode": new_passcode,
        },
    )
    return response
