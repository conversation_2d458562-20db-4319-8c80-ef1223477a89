import json
import commonUtils
from robot.api.deco import keyword, not_keyword


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"
serviceUrl = "oneapp-backend-url-2"
FILTER_MISSING_ERROR_MESSAGE = "filter is either missing or empty."


# POST public chargepoints
@keyword("Post public chargepoints")
def post_public_chargepoints(user_email, user_password, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/charging/public/stations"

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]


# POST public chargepoints with filters
@keyword("Post public chargepoints with filters")
def post_public_chargepoints_with_filters(user_email, user_password, requestBody, filter_type, filter, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/charging/public/stations"

    # nosemgrep: python.flask.security.injection.ssrf-requests.ssrf-requests
    response = commonUtils.send_request_with_json_updated_payload(
        user_email, user_password, requestBody, "search_filters", filter_type, filter, endpoint, approov, auth, "POST", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1], response[2]


# GET a single PC station
@keyword("Get a single public charging station")
def get_single_trip_station_id(user_email, user_password, stationID, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/charging/public/stations/" + stationID

    queryParams = {"language": "en-GB"}

    response = commonUtils.get_request_with_params(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE, params=queryParams)
    return response[0], response[1]


# GET JLR charging contract details
@keyword("Get jlr charging contract details")
def get_jlr_charging_contract_details(user_email, user_password, signUpFlag, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/users/me/contracts"

    queryParams = {"sign_up_flag": signUpFlag}

    response = commonUtils.get_request_with_params(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE, params=queryParams)
    return response[0], response[1]


# GET a single PC station
@keyword("Get a single public charging station via the gateway")
def get_single_trip_station_id_via_gw(user_email, user_password, stationID, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/charging/public/stations?stationId=" + stationID

    queryParams = {"language": "en-GB"}

    response = commonUtils.get_request_with_params(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE, params=queryParams)
    return response[0], response[1]


@keyword("Is tethered cable avaliable")
def tethered_cable(response):
    jsonResponse = json.loads(response)
    for chargeStation in jsonResponse["data"]:
        searchFilterFound = False
        for connectors in chargeStation["connectors"]:
            if connectors["fixed_cable"] != "TRUE":
                continue
            else:
                searchFilterFound = True
                break
        if searchFilterFound is not True:
            return f"{searchFilterFound}"
    return True


@keyword("Power Min")
def power_min(response, power):
    jsonResponse = json.loads(response)
    for chargeStation in jsonResponse["data"]:
        searchFilterFound = False
        for connectors in chargeStation["connectors"]:
            print(connectors["max_power_level"])
            if connectors["max_power_level"] >= power:
                searchFilterFound = True
                break
        if searchFilterFound is not True:
            return False
    return True


@keyword("Power Max")
def power_max(response, power):
    jsonResponse = json.loads(response)
    for chargeStation in jsonResponse["data"]:
        searchFilterFound = False
        for connectors in chargeStation["connectors"]:
            print(connectors["max_power_level"])
            if connectors["max_power_level"] <= power:
                searchFilterFound = True
                break
        if searchFilterFound is not True:
            return False
    return True


@keyword("Is JLR Charging avaliable")
def jlr_charging(response):
    jsonResponse = json.loads(response)
    for chargeStation in jsonResponse["data"]:
        if chargeStation["is_jlr_charging"] is not True:
            return False
    return True


@keyword("Verify the expected auth method search filter is returned in the response")
def verify_public_charging_search_filters_auth_methods(actual_response, request_body, request_filter_type, reponse_filter_type):
    load_response = json.loads(actual_response)
    search_filters = load_request(request_body)
    actual_response_public_charging = step_into_data_array(load_response)

    for public_charging in actual_response_public_charging:
        # Check if each conenctor has a nested "ev_station_details" object
        if (
            ("ev_station_details" in public_charging)
            and isinstance(public_charging["ev_station_details"], object)
            and public_charging["ev_station_details"]
        ):  # Noqa:E501
            public_charging_services = public_charging["ev_station_details"]
        else:
            return "EV station details object is either missing or empty"
        # Check if each ev_station_details object has a nested filters array
        if (
            (reponse_filter_type in public_charging_services)
            and isinstance(public_charging_services[reponse_filter_type], list)
            and public_charging_services[reponse_filter_type]
        ):  # Noqa:E501
            public_charging_search_filters = public_charging_services[reponse_filter_type]
        else:
            return FILTER_MISSING_ERROR_MESSAGE
        # If the filters array doesn't contain one of the search_filters, then return the details
        searchFilterFound = searchFilterFoundInPublicChargingConnectors(
            search_filters[request_filter_type], public_charging, public_charging_search_filters
        )  # Noqa:E501
        if searchFilterFound != "found":
            return f"{searchFilterFound}"

    return "ALL CONNECTORS MATCHED AT LEAST ONE FILTER"


def searchFilterFoundInPublicChargingConnectors(search_filters, public_charging, public_charging_search_filters):
    for searchFilter in search_filters:
        for public_charging_search_filter in public_charging_search_filters:
            if public_charging_search_filter.upper() == searchFilter:
                return "found"
    return f"The expected search filter {searchFilter} was not returned in the response for {public_charging}"


@not_keyword
def check_if_each_ev_station_details_object_has_a_nested_filters_array(
    reponse_filter_type, public_charging_service, search_filters, request_filter_type
):
    if (
        (reponse_filter_type in public_charging_service)
        and isinstance(public_charging_service[reponse_filter_type], str)
        and public_charging_service[reponse_filter_type]
    ):  # Noqa:E501
        public_charging_search_filters = public_charging_service[reponse_filter_type]
    else:
        return FILTER_MISSING_ERROR_MESSAGE
    # If the filters array doesn't contain one of the search_filters, then return the details
    searchFilterFound = search_filtersMatch(search_filters[request_filter_type], public_charging_service, public_charging_search_filters)
    return searchFilterFound


@keyword("Verify the expected connector type search filter is returned in the response")
def verify_public_charging_search_filters_connector_type(actual_response, request_body, request_filter_type, reponse_filter_type):
    load_response = json.loads(actual_response)
    search_filters = load_request(request_body)
    actual_response_public_charging = step_into_data_array(load_response)
    print(actual_response_public_charging)
    for public_charging in actual_response_public_charging:
        # Check if each conenctor has a nested "ev_station_details" object
        if ("ev_station_details" in public_charging) and isinstance(public_charging["connectors"], list) and public_charging["connectors"]:
            public_charging_services = public_charging["connectors"]
        else:
            return "connectors object is either missing or empty"
        # Check if each ev_station_details object has a nested filters array
        searchFilterFound = False
        for public_charging_service in public_charging_services:
            searchFilterFound = check_if_each_ev_station_details_object_has_a_nested_filters_array(
                reponse_filter_type, public_charging_service, search_filters, request_filter_type
            )
            if searchFilterFound == "found":
                break
        if searchFilterFound != "found":
            return f"{searchFilterFound}"

    return "ALL CONNECTORS MATCHED AT LEAST ONE FILTER"


@not_keyword
def check_if_each_connector_statuses_object_has_a_nested_status(
    reponse_filter_type, public_charging_status, search_filters, request_filter_type, public_charging_service
):
    if (
        (reponse_filter_type in public_charging_status)
        and isinstance(public_charging_status[reponse_filter_type], str)
        and public_charging_status[reponse_filter_type]
    ):  # Noqa:E501
        public_charging_search_filter = public_charging_status[reponse_filter_type]
    else:
        return FILTER_MISSING_ERROR_MESSAGE
    # If the filters array doesn't contain one of the search_filters, then return the details
    searchFilterFound = search_filtersMatch(search_filters[request_filter_type], public_charging_service, public_charging_search_filter)
    return searchFilterFound


@not_keyword
def check_if_each_conenctor_has_a_nested_connector_statuses_object(public_charging_service, reponse_filter_type, search_filters, request_filter_type):
    if (
        ("connector_statuses" in public_charging_service)
        and isinstance(public_charging_service["connector_statuses"], list)
        and public_charging_service["connector_statuses"]
    ):  # Noqa:E501
        public_charging_statuses = public_charging_service["connector_statuses"]
    else:
        return "connectors status is either missing or empty"
    # Check if each connector_statuses object has a nested status
    for public_charging_status in public_charging_statuses:
        searchFilterFound = check_if_each_connector_statuses_object_has_a_nested_status(
            reponse_filter_type, public_charging_status, search_filters, request_filter_type, public_charging_service
        )
        if searchFilterFound == "found":
            break
    return searchFilterFound


@keyword("Verify the expected connector status search filter is returned in the response")
def verify_public_charging_search_filters_connector_status(actual_response, request_body, request_filter_type, reponse_filter_type):
    load_response = json.loads(actual_response)
    search_filters = load_request(request_body)
    actual_response_public_charging = step_into_data_array(load_response)

    for public_charging in actual_response_public_charging:
        # Check if each conenctor has a nested "ev_station_details" object
        if ("connectors" in public_charging) and isinstance(public_charging["connectors"], list) and public_charging["connectors"]:
            public_charging_services = public_charging["connectors"]
        else:
            return "connectors object is either missing or empty"
        searchFilterFound = False
        for public_charging_service in public_charging_services:
            searchFilterFound = check_if_each_conenctor_has_a_nested_connector_statuses_object(
                public_charging_service, reponse_filter_type, search_filters, request_filter_type
            )
            if searchFilterFound == "found":
                break
        if searchFilterFound != "found":
            return f"{searchFilterFound}"

    return "ALL CONNECTORS MATCHED AT LEAST ONE FILTER"


def search_filtersMatch(search_filters, public_charging, public_charging_search_filter):
    for searchFilter in search_filters:
        if public_charging_search_filter.upper() == searchFilter:
            return "found"
    return f"The expected search filter {searchFilter} was not returned in the response for {public_charging}"


def load_request(request_body):
    # Load expected filters from request body
    request_body_full = json.loads(request_body)
    search_filters = request_body_full["search_filters"]
    return search_filters


def step_into_data_array(load_response):
    # Check if "data" exists in the response body
    if "data" in load_response and isinstance(load_response["data"], list) and load_response["data"]:
        actual_response_public_charging = load_response["data"]
        return actual_response_public_charging
    else:
        return 'No valid "data" key found in the response body - it was either missing or empty.'


@keyword("Correct blocking reason returned")
def assert_reason(response, expected_reason):
    load_response = json.loads(response)

    for contract in load_response:
        reason = contract.get("blocking_reason", {}).get("reason")
        assert reason == expected_reason, f"Expected '{expected_reason}', but got '{reason}'"
    return reason
