from robot.api.deco import keyword
import commonUtils


systemVarFile = "../variables/system.yml"
AUTH_TYPE = "Bearer "
CONTENT_TYPE = "application/json"
ME_URI = "/me/vehicles/"


serviceUrl = "oneapp-backend-url"


# GET vehcile
@keyword("Get user vehicle")
def get_user_vehicle(user_email, user_password, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + "/me/vehicles"

    response = commonUtils.get_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]


# PUT vehicle registration
@keyword("Update vehicle registration")
def update_vehicle_registration(user_email, user_password, vehicleID, requestBody, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + ME_URI + vehicleID

    response = commonUtils.send_request_with_json_payload(
        user_email, user_password, requestBody, endpoint, approov, auth, "PATCH", CONTENT_TYPE, AUTH_TYPE
    )
    return response[0], response[1]


# DELETE vehicle
@keyword("Delete users vehicle")
def delete_user_vehicle(user_email, user_password, vehicleID, approov=False, auth=True):
    url = commonUtils.get_url(serviceUrl)

    endpoint = url + ME_URI + vehicleID

    response = commonUtils.delete_request(user_email, user_password, endpoint, approov, auth, CONTENT_TYPE, AUTH_TYPE)
    return response[0], response[1]
