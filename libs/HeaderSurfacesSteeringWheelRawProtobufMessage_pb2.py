# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: HeaderSurfacesSteeringWheelRawProtobufMessage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import EnumStatus_pb2 as EnumStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n3HeaderSurfacesSteeringWheelRawProtobufMessage.proto\x12)jlr.protobuf.heatedsurfaces.steeringwheel\x1a\x10\x45numStatus.proto"\x82\x01\n\x1dSetHeatedSteeringWheelRequest\x12\x61\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32\x42.jlr.protobuf.heatedsurfaces.steeringwheel.EnumHSWTemperatureLevel"Q\n\x1eSetHeatedSteeringWheelResponse\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"%\n#GetHeatedSteeringWheelStatusRequest"\xba\x01\n$GetHeatedSteeringWheelStatusResponse\x12\x61\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32\x42.jlr.protobuf.heatedsurfaces.steeringwheel.EnumHSWTemperatureLevel\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus"\xb5\x01\n\x1fNotifyHeatedSteeringWheelStatus\x12\x61\n\x15hsw_temperature_level\x18\x01 \x01(\x0e\x32\x42.jlr.protobuf.heatedsurfaces.steeringwheel.EnumHSWTemperatureLevel\x12/\n\x06status\x18\x02 \x01(\x0e\x32\x1f.jlr.protobuf.common.EnumStatus*\xad\x01\n\x17\x45numHSWTemperatureLevel\x12*\n&ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED\x10\x00\x12"\n\x1e\x45NUM_HSW_TEMPERATURE_LEVEL_OFF\x10\x01\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_1\x10\x02\x12 \n\x1c\x45NUM_HSW_TEMPERATURE_LEVEL_2\x10\x03\x42\x84\x01\nQcom.jaguarlandrover.commandandcontrolprotobuflibrary.heatedsurfaces.steeringwheelB-HeatedSurfacesSteeringWheelRawProtobufMessageP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "HeaderSurfacesSteeringWheelRawProtobufMessage_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\nQcom.jaguarlandrover.commandandcontrolprotobuflibrary.heatedsurfaces.steeringwheelB-HeatedSurfacesSteeringWheelRawProtobufMessageP\001"
    )
    _globals["_ENUMHSWTEMPERATURELEVEL"]._serialized_start = 745
    _globals["_ENUMHSWTEMPERATURELEVEL"]._serialized_end = 918
    _globals["_SETHEATEDSTEERINGWHEELREQUEST"]._serialized_start = 117
    _globals["_SETHEATEDSTEERINGWHEELREQUEST"]._serialized_end = 247
    _globals["_SETHEATEDSTEERINGWHEELRESPONSE"]._serialized_start = 249
    _globals["_SETHEATEDSTEERINGWHEELRESPONSE"]._serialized_end = 330
    _globals["_GETHEATEDSTEERINGWHEELSTATUSREQUEST"]._serialized_start = 332
    _globals["_GETHEATEDSTEERINGWHEELSTATUSREQUEST"]._serialized_end = 369
    _globals["_GETHEATEDSTEERINGWHEELSTATUSRESPONSE"]._serialized_start = 372
    _globals["_GETHEATEDSTEERINGWHEELSTATUSRESPONSE"]._serialized_end = 558
    _globals["_NOTIFYHEATEDSTEERINGWHEELSTATUS"]._serialized_start = 561
    _globals["_NOTIFYHEATEDSTEERINGWHEELSTATUS"]._serialized_end = 742
# @@protoc_insertion_point(module_scope)
