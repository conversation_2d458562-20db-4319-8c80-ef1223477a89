import asyncio
import sys
import forgeRock
import threading
import queue
import time
import vcdpHiveBroker
import vehicleProtobuf
from pyaml_env import parse_config, BaseConfig
from urllib.parse import urlparse
from gql import Client, gql
from gql.transport.appsync_websockets import AppSyncWebsocketsTransport
from gql.transport.appsync_auth import AppSyncJWTAuthentication
from robot.api.deco import keyword, not_keyword

systemVarFile = "../variables/system.yml"
frUsr = ""
frPw = ""


@not_keyword
def geturl():
    system_vars = BaseConfig(parse_config(systemVarFile))
    app_sync_url = getattr(system_vars, "vehicle-shadow-appsync-wss-url")
    if app_sync_url is None:
        print("Missing environment variables")
        sys.exit()
    return app_sync_url


@not_keyword
def create_protobuf(signal_value, query_id, signal_id):
    protobuf = vehicleProtobuf.create_serialised_protbuf_message_for_float_signal(query_id, signal_id, signal_value)
    return protobuf


@not_keyword
def get_app_sync_transport(use_forge_rock, url, host):
    if use_forge_rock is False:  # USE IAM
        transport = AppSyncWebsocketsTransport(url=url)
    else:
        token = forgeRock.get_token(frUsr, frPw)
        auth = AppSyncJWTAuthentication(
            host=host,
            jwt=token,
        )
        transport = AppSyncWebsocketsTransport(url=url, auth=auth)
    return transport


@not_keyword
async def appsync_subs(unique_id, signal_map, value_abstract, queue=None, use_forge_rock=False):
    url = geturl()

    host = str(urlparse(url).netloc)
    print(f"Host: {host}")
    transport = get_app_sync_transport(use_forge_rock, url, host)

    async with Client(transport=transport) as session:
        if len(value_abstract) <= 0:
            subscription = gql(
                f"""
                    subscription MySubscription($unique_id: String!) {{
                    onVehicleDataUpdate(VehicleId: $unique_id) {{
                        {signal_map} {{
                                eventTime
                                signalLabel
                                valueAbstract
                                valueDetailed
                                valueSource
                            }}
                        }}
                    }}
                    """
            )
        else:
            subscription = gql(
                f"""
                    subscription MySubscription($unique_id: String!) {{
                    onVehicleDataUpdate(VehicleId: $unique_id) {{
                        {signal_map} {{
                                eventTime
                                signalLabel
                                valueDetailed
                                valueSource
                            }}
                        }}
                    }}
                    """
            )

        variable_values = {"unique_id": unique_id}
        print("Waiting for messages...")
        vehicle_latency_times = []
        async for result in session.subscribe(subscription, variable_values=variable_values):
            time_of_update = int(time.time() * 1000)
            print(time_of_update)
            print(result)
            if result["onVehicleDataUpdate"][signal_map] is None:
                print("null in AppSync Subscription")
            else:
                if queue is None:
                    print("Doing latency testing")
                    event_time = result["onVehicleDataUpdate"][signal_map]["eventTime"]
                    latency = time_of_update - event_time
                    vehicle_latency_times.append(latency)
                    if len(vehicle_latency_times) >= 3:
                        return vehicle_latency_times
                else:
                    queue.put([time_of_update, result])
                    exit()

        return (-1, -1)


@keyword("Get time of next vehicle signal change")
def get_time_of_next_signal_change(unique_id, signal_map, value_abstract, queue=None):
    """
    Subscribes to AppSync for given unique_id and Signal
    Returns the time stamp when the data was changed. (not the time the data was offboarded from the vehicle)
    Examples:
    | ${timestamp}= | Get time of next vehicle signal change | 619eec0f-4e99-4eef-b058-c53a08574bad | CentralLockStatus  |
    | ${timestamp}= | Get time of next vehicle signal change | 619eec0f-4e99-4eef-b058-c53a08574bad | ActualTyrePressureFL |

    Optionally can be used as a thread if queue!=None
    """
    time_of_update = asyncio.run(appsync_subs(unique_id, signal_map, value_abstract, queue))
    print(time_of_update)
    return time_of_update


@keyword("Update in received in AppSync Subscription")
def update_received_appsync_subscription(
    unique_id,
    broker_user,
    broker_client_secret,
    hive_certfile,
    hive_ca_cert,
    hive_keyfile,
    signal_value,
    signal_id,
    query_id,
    signal_map,
    value_abstract,
    latency=False,
):
    start_of_test_time = int(time.time() * 1000)
    print("TIMESTAMP::::: Start of Test timestamp (ms):: ", start_of_test_time)

    q = queue.Queue()
    thread_function = get_time_of_next_signal_change
    x = threading.Thread(target=thread_function, args=(unique_id, signal_map, value_abstract, q), daemon=True)
    x.start()
    x.join(5)
    appsync_sub_started_time = int(time.time() * 1000)
    print(
        "TIMESTAMP::::: Time to start AppSync Subscription (ms):: ",
        appsync_sub_started_time - start_of_test_time,
    )
    # then send data to hive
    client = vcdpHiveBroker.connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        hive_certfile,
        hive_ca_cert,
        hive_keyfile,
    )

    connected_to_hive_time = int(time.time() * 1000)
    print(
        "TIMESTAMP::::: Time to connect to hive (ms):: ",
        connected_to_hive_time - appsync_sub_started_time,
    )
    client.loop_start()
    topic = "dt/" + unique_id + "/stream/eng"
    protobuf = create_protobuf(signal_value, signal_id, query_id)

    result = client.publish(topic, payload=protobuf[1], qos=0, retain=False)
    print("publish result: " + str(result))
    publised_to_hive_time = int(time.time() * 1000)
    print(
        "TIMESTAMP::::: Published to hive (ms):: ",
        publised_to_hive_time - connected_to_hive_time,
    )

    client.disconnect()
    client.loop_stop()

    x.join(0)
    thread_result = q.get()
    print("thread result:: ", thread_result)
    print("TIMESTAMP::::: AppSync update (ms):: " + str(thread_result))
    print("TIMESTAMP::::: protobuf event time (ms):: ", protobuf[2])
    # calculate time difference
    time_diff = thread_result[0] - protobuf[2]
    print("TIMESTAMP::::: Time from publish to Hive to App Sync (ms):: ", thread_result[0] - publised_to_hive_time, result)
    if latency is True:
        return time_diff
    else:
        return thread_result


@not_keyword
def publish_va_data_to_hive_only(
    unique_id,
    broker_user,
    broker_client_secret,
    hive_certfile,
    hive_ca_cert,
    hive_keyfile,
    num_messages=3,
    delay_between_messages=10,
):
    client = vcdpHiveBroker.connect_to_broker(
        unique_id,
        broker_user,
        broker_client_secret,
        hive_certfile,
        hive_ca_cert,
        hive_keyfile,
    )

    client.loop_start()
    topic = "dt/" + unique_id + "/stream/eng"
    for n in range(num_messages):
        before_proto = int(time.time() * 1000)
        protobuf = create_protobuf(n)
        after_proto = int(time.time() * 1000)
        print("\nTime stamp before protobuf: ", str(before_proto))
        print("\nTime to create Protobuf: ", str(after_proto - before_proto))
        result = client.publish(topic, payload=protobuf[1], qos=0, retain=False)
        print("publish result: " + str(result))
        published_to_hive = int(time.time() * 1000)
        print("\nTime to Publish Protobuf: ", str(published_to_hive - after_proto))
        time.sleep(delay_between_messages)

    client.disconnect()
    client.loop_stop()
