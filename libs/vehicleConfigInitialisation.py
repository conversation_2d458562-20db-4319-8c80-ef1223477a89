#!/usr/bin python3

import requests
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig

from commonUtils import get_keycloak_token


__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
CONTENT_TYPE = "application/json"
AUTH_TYPE = "Bearer "
ERRORS_URI = "/api/errors"
NO_RESPONSE_MESSAGE = "No response body returned"

systemVarFile = "../variables/system.yml"

headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Requester": "Robot"}


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "vehicel-config-initialisation")


@keyword("Get Errors VCI")
def get_errors_vci(user, pw, vin):
    token = get_keycloak_token(user, pw)

    VinString = str(vin)
    vinStriped = VinString.strip("['']")
    header = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Requester": "Robot", "VIN": vinStriped, "Authorization": AUTH_TYPE + token}

    print(header)
    url = get_url()
    getErrors = url + ERRORS_URI

    response = requests.request("GET", getErrors, headers=header)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return NO_RESPONSE_MESSAGE, response.status_code


@keyword("Retry vehicle initialisation")
def retry_initialise(user, pw, vin):
    token = get_keycloak_token(user, pw)

    VinString = str(vin)
    vinStriped = VinString.strip("['']")
    header = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Requester": "Robot", "VIN": vinStriped, "Authorization": AUTH_TYPE + token}

    url = get_url()
    retryInitialise = url + ERRORS_URI

    response = requests.request("POST", retryInitialise, headers=header)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return NO_RESPONSE_MESSAGE, response.status_code


@keyword("Delete error logs")
def delete_error_logs(user, pw, vin):
    token = get_keycloak_token(user, pw)

    VinString = str(vin)
    vinStriped = VinString.strip("['']")
    header = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Requester": "Robot", "VIN": vinStriped, "Authorization": AUTH_TYPE + token}

    url = get_url()
    deletelogs = url + ERRORS_URI

    response = requests.request("DELETE", deletelogs, headers=header)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return NO_RESPONSE_MESSAGE, response.status_code


@not_keyword
def get_url_ccem():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "config-centre-event-manager")


@keyword("Retry Initialisation")
def retry_initialisation(user, pw):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Requester": "Robot", "Authorization": AUTH_TYPE + token}

    print(header)
    url = get_url_ccem()

    retryInitialisaton = url + "/api/admin/vci/pending-errors"
    print(retryInitialisaton)
    response = requests.request("POST", retryInitialisaton, headers=header)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return NO_RESPONSE_MESSAGE, response.status_code
