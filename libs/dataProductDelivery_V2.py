#!/usr/bin python3

import requests
import json
from robot.api.deco import keyword, not_keyword
from pyaml_env import parse_config, BaseConfig
from commonUtils import get_keycloak_token


__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"
contentType = "application/json"
authType = "Bearer "

systemVarFile = "../variables/system.yml"

headers = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot"}
noResponseBody = "No response body returned"


@not_keyword
def get_dpd_v2url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "data-product-delivery-url-v2")


@not_keyword
def get_url():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "vehicel-config-initialisation")


@not_keyword
def config_event():
    system_vars = BaseConfig(parse_config(systemVarFile))
    return getattr(system_vars, "config-center-event-manager")


@keyword("Deliver data product with eligibility criteria")
def deliver_dp(user, pw, criteria):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": contentType, "Authorization": authType + token}

    print(header)
    url = config_event() + "/api/events/process-vehicles"
    print(url)

    if criteria == "SINGLERR":
        eligibilityCriteria = {"assets": {"brand": ["RANGEROVER"], "modelRange": ["L460"], "modelYear": ["2023"], "fuelType": ["DIESEL"]}}
    elif criteria == "SINGLEJAG":
        eligibilityCriteria = {"assets": {"brand": ["JAGUAR"], "modelRange": ["L4601"], "modelYear": ["2025"], "fuelType": ["PETROL"]}}
    elif criteria == "NOBRAND":
        eligibilityCriteria = {"assets": {"brand": None, "modelRange": ["L460"], "modelYear": ["2023"], "fuelType": ["DIESEL"]}}
    elif criteria == "NORANGE":
        eligibilityCriteria = {"assets": {"brand": ["RANGEROVER"], "modelRange": None, "modelYear": ["2023"], "fuelType": ["DIESEL"]}}
    elif criteria == "NOYEAR":
        eligibilityCriteria = {"assets": {"brand": ["RANGEROVER"], "modelRange": ["L460"], "modelYear": None, "fuelType": ["DIESEL"]}}
    elif criteria == "NOFUELTYPE":
        eligibilityCriteria = {"assets": {"brand": ["RANGEROVER"], "modelRange": ["L460"], "modelYear": ["2023"], "fuelType": None}}
    elif criteria == "MULTI":
        eligibilityCriteria = {
            "assets": {
                "brand": ["RANGEROVER", "JAGUAR"],
                "modelRange": ["L460", "L4601"],
                "modelYear": ["2023", "2025"],
                "fuelType": ["DIESEL", "PETROL"],
            }
        }
    else:
        # use eligibilityCriteria as passed in
        eligibilityCriteria = criteria

    payload = json.dumps(eligibilityCriteria)
    print(payload)

    response = requests.request("POST", url, headers=header, data=payload)

    print(str(response.text))
    print(str(response.status_code))
    if response.status_code != 403:
        return response.text, response.status_code
    else:
        return noResponseBody, response.status_code


@keyword("remove data products")
def remove_dp(data, user, pw, dpId):
    token = get_keycloak_token(user, pw)

    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    jsonRequestBody["dataProductId"] = dpId

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    header = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot", "Authorization": authType + token}

    print(header)
    url = config_event()
    removeDP = url + "/api/events/remove-data-product"
    print(removeDP)

    response = requests.request("POST", removeDP, headers=header, data=payload)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return noResponseBody, response.status_code


@keyword("redeliver data products")
def redeliver_dp(user, pw):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot", "Authorization": authType + token}

    print(header)
    url = config_event()

    redeliverDp = url + "/api/admin/dpd/expired-data-products"
    print(redeliverDp)
    response = requests.request("POST", redeliverDp, headers=header)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return noResponseBody, response.status_code


@keyword("Deliver data product on VIN")
def deliver_dp_with_vin(data, user, pw, vin):
    token = get_keycloak_token(user, pw)

    header = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot", "Authorization": authType + token}
    openJsonData = open(data, "r")
    readJsonData = openJsonData.read()
    jsonRequestBody = json.loads(readJsonData)

    vinString = str(vin)
    print(type(vinString))
    print(vinString)

    vinStriped = vinString.strip("['']")
    jsonRequestBody["vins"][0] = vinStriped

    jsonRequestBody.update(jsonRequestBody)

    payload = json.dumps(jsonRequestBody)

    print(payload)
    url = config_event()

    deliverDp = url + "/api/admin/dpd/data-products"
    print(deliverDp)
    response = requests.request("POST", deliverDp, headers=header, data=payload)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return noResponseBody, response.status_code


@keyword("Delete error logs")
def delete_error_logs(user, pw, vin):
    token = get_keycloak_token(user, pw)

    VinString = str(vin)
    vinStriped = VinString.strip("['']")
    header = {"accept": "*/*", "Content-Type": contentType, "Requester": "Robot", "VIN": vinStriped, "Authorization": authType + token}

    url = get_url()
    deletelogs = url + "/api/errors"

    response = requests.request("DELETE", deletelogs, headers=header)

    print(type(response.text))

    if response.status_code != 403:
        return response.text, response.status_code

    else:
        return noResponseBody, response.status_code


@keyword("Send Data Product to VIN DPD v2")
def send_data_product_to_vin(user, pw, dataProductIds: list, vin, auth=True):
    url = get_dpd_v2url()
    dataproducturl = url + "vehicles/data-products"
    print(dataproducturl)
    token = get_keycloak_token(user, pw)
    headers = {"Authorization": authType + token, "Content-Type": contentType}
    print(vin)

    payload = json.dumps({"dataProductsForVehicles": [{"vin": vin, "dataProductIds": dataProductIds}]})
    print(payload)

    response = requests.request("PUT", dataproducturl, data=payload, headers=headers)
    print(response.status_code)
    return response.status_code, response.text
