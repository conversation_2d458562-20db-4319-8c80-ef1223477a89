from robot.api.deco import keyword
from pyaml_env import parse_config, BaseConfig
from vehicle_permissions import call_post_endpoint, call_get_endpoint
import uuid
import json


class BindPayload:
    def __init__(self, user_id: str, user_id_type: str, binding_type: str, exclusive_from: str, vin: str = None, vuid: str = None):
        self.vin = vin
        self.vuid = vuid
        self.user_id = user_id
        self.user_id_type = user_id_type
        self.binding_type = binding_type
        self.exclusive_from = exclusive_from

    def to_json(self):
        json_data = {"userId": self.user_id, "userIdType": self.user_id_type, "bindingType": self.binding_type, "exclusiveFrom": self.exclusive_from}
        if self.vin is not None:
            json_data["vin"] = self.vin
        else:
            json_data["vuid"] = self.vuid
        return json_data


class UnbindPayload:
    def __init__(self, user_id: str, user_id_type: str, unbinding_type: str, exclusive_to: str, vin: str = None, vuid: str = None):
        self.vin = vin
        self.vuid = vuid
        self.user_id = user_id
        self.user_id_type = user_id_type
        self.unbinding_type = unbinding_type
        self.exclusive_to = exclusive_to

    def to_json(self):
        json_data = {"userId": self.user_id, "userIdType": self.user_id_type, "unbindingType": self.unbinding_type, "exclusiveTo": self.exclusive_to}
        if self.vin is not None:
            json_data["vin"] = self.vin
        else:
            json_data["vuid"] = self.vuid
        return json_data


@keyword("create BindPayload object")
def create_BindPayload_object(user_id: str, user_id_type: str, binding_type: str, exclusive_from: str, vin=None, vuid=None):
    if vin is not None:
        return BindPayload(user_id, user_id_type, binding_type, exclusive_from, vin)
    else:
        return BindPayload(user_id, user_id_type, binding_type, exclusive_from, vuid=vuid)


@keyword("create UnbindPayload object")
def create_UnbindPayload_object(user_id: str, user_id_type: str, unbinding_type: str, exclusive_to: str, vin=None, vuid=None):
    if vin is not None:
        return UnbindPayload(user_id, user_id_type, unbinding_type, exclusive_to, vin=vin)
    else:
        return UnbindPayload(user_id, user_id_type, unbinding_type, exclusive_to, vuid=vuid)


@keyword("post binding request")
def post_bind_request(var_file, bindRequest: BindPayload, keycloak_token):
    system_vars = BaseConfig(parse_config(var_file))
    correlation_id = str(uuid.uuid4())
    vroom_gateway_endpoint = getattr(system_vars, "vroom_gateway_endpoint")
    json_request = json.dumps(bindRequest.to_json())
    headers = {"Content-Type": "application/json", "Correlation-Id": correlation_id.encode("utf-8"), "Authorization": keycloak_token}
    return call_post_endpoint(url=vroom_gateway_endpoint + "binding/bind", json_payload=json_request, headers=headers)


@keyword("post unbinding request")
def post_unbind_request(var_file, unbindRequest: UnbindPayload, keycloak_token):
    system_vars = BaseConfig(parse_config(var_file))
    correlation_id = str(uuid.uuid4())
    vroom_gateway_endpoint = getattr(system_vars, "vroom_gateway_endpoint")
    json_request = json.dumps(unbindRequest.to_json())
    headers = {"Content-Type": "application/json", "Correlation-Id": correlation_id.encode("utf-8"), "Authorization": keycloak_token}
    return call_post_endpoint(url=vroom_gateway_endpoint + "binding/unbind", json_payload=json_request, headers=headers)


@keyword("get Vehicle-Person association by vin or vuid")
def get_vp_assocation_by_vin(keycloak_token, var_file, vin=None, vuid=None):
    system_vars = BaseConfig(parse_config(var_file))
    room_v2_endpoint = getattr(system_vars, "room_v2_endpoint")
    correlation_id = str(uuid.uuid4())[3:].encode("utf-8")
    headers = {"Correlation-Id": correlation_id, "Authorization": keycloak_token}
    if vuid:
        params = {"vehicleIdentifier": "VUID"}
        return call_get_endpoint(room_v2_endpoint + "/vp-association/vehicle/" + vuid, headers=headers, params=params)
    else:
        return call_get_endpoint(room_v2_endpoint + "/vp-association/vehicle/" + vuid, headers=headers)
