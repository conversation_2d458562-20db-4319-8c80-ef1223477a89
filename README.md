# Vcdp E2e Tests Base Image

base docker image for vcdp e2e tests

## Usage

Used to crete a basic docker image which has the nessesary python dependencies to run e2e tests
Creates testUser which can be used in dependent docker containers to run tasks as opposed to root

This container also contains shared libs and json schema data which is commonly used across vcdp


## Running on MacOS
The scripts in this test suite are written for GNU sed, but MacOS ships with BSD sed as standard. To remove the need for
maintaining multiple versions of the script, GNU sed can be installed in parallel with BSD sed. The script will detect
the runtime environment and use the appropriate version of sed when running on MacOS.
Install GNU sed using the following command: `brew install gsed`


## Available flags and config options (in config.yml)

### pullTests
#### default true
pull down tests from xray (replacing local changes)
### usePabot
#### default False
switch to use pabot rather then robot (tests run in parallel)
### retries
#### depends on usePabot
#### default 0
set the number of reties of a failing test when using pabot


### pullBaseImage
#### default True

pull down the base image (will replace local libs/getConfigFromFile.py/getSecretsFromAWSSecretsManager.py)
### pullSecrets
#### default True

pull down the secrets from AWS secrets manager
### useSharedLibs
#### default False

If your project doesn't use the shared libs from this project set this to false

Note this also requires updating the gitlab.yml to prevent libs directory being replaced 

```
  script:
    - cp /home/<USER>/ciRunTests.sh /home/<USER>/getSecretsFromAWSSecretsManager.py /home/<USER>/getConfigFromFile.py .
    - ENV_SECRETS_YAML=secret_keys_ecosys.yml bash ciRunTests.sh

```
### resourceFile
#### default app-config.robot
override the name of the resource file
### testSetUpStep
#### default not set
if you want  a set up step in your tests, add it here

### testTearDownStep
#### default not set
if you want a tear down step in your tests, add it here

### logLevel
#### default INFO
If you are using robot.api logger library in your python code to print logs at different levels (INFO, DEBUG, WARN, ERROR etc), the logging level default to INFO.
If you want to set logging level to DEBUG so you could invesitgate with more details while code executes, you could set this flag DEBUG.

Example:

Config.yml
```
  config:
    - logLevel: DEBUG
```
Python code
```

  from robot.api import logger
  logger.info("This message is for information")
  logger.debug("This is a debug message")
  logger.warn("This is a warning message")
  logger.error("There is an error occured")

```

Logs printed on the report

```
  11:44:52.196	INFO	This message is for information	
  11:44:52.196	DEBUG	This is a debug message	
  11:44:52.196	WARN	This is a warning message	
  11:44:52.196	ERROR	There is an error occured
```

