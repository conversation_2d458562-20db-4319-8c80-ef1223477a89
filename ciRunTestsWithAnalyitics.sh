#!/bin/bash

#get jira test plan and jira token from secrets or variables
exitCode=1
getTests () {
    mkdir -p robot
    cd robot
    rm -rf testsuites featureExport reatures.zip

    # get test scenarios from a JIRA "Test plan"
    curl -s --location --request GET 'https://jira.devops.jlr-apps.com/rest/raven/1.0/export/test?keys='$XRAY_TEST_SUITE_ID \
    --header 'Accept=application/zip' \
    --header 'Authorization: Bearer '"$jiraToken"'' \
    --output features.zip

    unzip -qq features.zip -d featureExport || {
        mkdir featureExport
        echo "failed to unzip file, assuming this is a single feature file project"
        mv features.zip featureExport/1.feature

    }
    cd featureExport

    for file in *.feature; do
        gherkin2robotframework $file
    done

    mkdir ../testsuites
    for file in *.robot; do
        mv $file ../testsuites/$file
    done

    cd ../testsuites
    rm *_step_definitions.robot

    if [[ -z "$resourceFile" ]]; then
        resourceFile="app-config.robot" #change default resource file name

    fi

    for file in *.robot; do
        sed -i -e 's/TEST_//g' $file
        sed -i '/step_definitions.robot/d' $file
        sed -i -e 's/ ${/    ${/g' $file
        sed -i '/Metadata/d' $file
        sed -i -e 's/Given\s/GIVEN /g' $file
        sed -i -e 's/When\s/WHEN /g' $file
        sed -i -e 's/Then\s/THEN /g' $file
        sed -i -e 's/And\s/AND /g' $file
        sed -i 's/txt/robot/' $file
        sed -i 's/Resource	keywords.robot//' $file
        sed -i "/Test Cases/ i Resource        ../resource/$resourceFile" $file
        if [[ ! -z "$suiteSetUpStep" ]]; then  #add a setup line to robot files
            sed -i "/Test Cases/ i Suite Setup      $suiteSetUpStep" $file
        fi
        if [[ ! -z "$testSetUpStep" ]]; then  #add a setup line to robot files
            sed -i "/Test Cases/ i Test Setup       $testSetUpStep" $file
        fi
        if [[ ! -z "$testTearDownStep" ]]; then #add a teardown line to robot files
            sed -i "/Test Cases/ i Test Teardown 	       $testTearDownStep" $file

        fi
        sed -i '/Test Cases/ i \\' $file         # blank line
    done

    cd ../..
    rm -rf featureExport features.zip

}

getAndSetSecrets () {

    #Get secrets from secrets manager and load them as environment variables
    python3 getSecretsFromAWSSecretsManager.py
    while read line
    do
        export "$line"
    done < ci_secrets.env  &> /dev/null
}

runTests () {
    excludeList="--exclude AutomatedTest-NotReadyORAutomatedTest-BlockedORAutomatedVCDPTest-IsolatedORAutomatedVCDPTest-RETIRED"
   
    if [[ ! -z $excludeOverride ]]; then
        excludeList="--exclude $excludeOverride"

    fi
    if [[ ! -z $includedTagOverride ]]; then
        includedTags="--include $includedTagOverride --output $includedTagOverride.xml"

    fi
    echo $excludeList
    cd robot/testsuites
    if [[ $usePabot != True ]]; then 
        if [[ ! -n "$logLevel" || "$logLevel" == "INFO" ]]; then
            robot $excludeList --xunit junitOutput.xml -v useProxy:False $testSuite $includedTags
        else
            robot --loglevel=$logLevel $excludeList --xunit junitOutput.xml -v useProxy:False $testSuite $includedTags
        fi        
    else
        pabot --processes 4 --chunk --xunit junitOutput.xml --listener RetryFailed:$numberOfRetries $excludeList $includedTags -v useProxy:False $testSuite
    
    fi
    exitCode=$?
}

uploadResults(){
    outputFile="output.xml"
    if [[ ! -z $includedTagOverride ]]; then
        outputFile="$includedTagOverride.xml"
    fi
    echo "Pushing results to Jira x-ray......"
    curl -s -H "Content-Type: multipart/form-data" \
    -F "file=@./$outputFile" "https://jira.devops.jlr-apps.com/rest/raven/1.0/import/execution/robot?projectKey=DDA&testPlanKey="$XRAY_TEST_SUITE_ID \
    --header 'Accept: application/json' \
    --header 'Authorization: Bearer '"$jiraToken"''


}

pullAndSetConfig () {

    python3 getConfigFromFile.py
    while read line
    do
        export "$line"
    done < ci_config.env  &> /dev/null

}

analyiseSharedLibUsage () {

    python3 /home/<USER>/testOutputParser.py
}


#if we have a local .env file load it in
[ -s .env ] && export $(cat .env | xargs) &> /dev/null

pullAndSetConfig
getAndSetSecrets
getTests
runTests
analyiseSharedLibUsage

#only upload test results if running in gitlab
if [[ ! -z "$GITLAB_CI" ]]; then
    uploadResults
fi

exit $exitCode
