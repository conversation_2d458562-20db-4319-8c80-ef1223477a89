FROM 557068259488.dkr.ecr.eu-west-2.amazonaws.com/python:3

ENV LIBRD_VER=2.5.3

LABEL maintainer="<EMAIL>"
RUN apt-get update && \
    apt-get install -y --no-install-recommends git gcc g++ less libxslt-dev libxml2-dev openssh-client sshpass unzip xvfb curl npm musl-dev libffi-dev bash make awscli libssl-dev slapd ldap-utils librdkafka-dev wget && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/* && apt clean

RUN groupadd -g 10000 app-group && adduser testUser --allow-bad-names && adduser testUser app-group
ENV WORKDIR=/home/<USER>
WORKDIR ${WORKDIR}

#copy files into the test users home to make it eaiser in gitlab
COPY --chown=testUser:app-group forgeRockjson /home/<USER>/forgeRockjson
COPY --chown=testUser:app-group graphqlschema /home/<USER>/graphqlschema
COPY --chown=testUser:app-group jsonschema /home/<USER>/jsonschema
COPY --chown=testUser:app-group libs /home/<USER>/libs
COPY --chown=testUser:app-group libsHil /home/<USER>/libsHil
COPY --chown=testUser:app-group ciRunTests.sh ciRunTestsWithAnalyitics.sh getConfigFromFile.py getSecretsFromAWSSecretsManager.py testAnalysis.py slackAlert.py /home/<USER>/

#copy files in root while we are migrating the gitlab template
COPY --chown=testUser:app-group forgeRockjson forgeRockjson
COPY --chown=testUser:app-group graphqlschema graphqlschema
COPY --chown=testUser:app-group jsonschema jsonschema
COPY --chown=testUser:app-group libs libs 
COPY --chown=testUser:app-group libsHil libsHil
COPY --chown=testUser:app-group requirements.txt ciRunTests.sh ciRunTestsWithAnalyitics.sh getConfigFromFile.py getSecretsFromAWSSecretsManager.py testAnalysis.py slackAlert.py ./


#work around for issues installing this with pip
RUN curl https://files.pythonhosted.org/packages/4b/99/4ffc2253cbff664c93f4fe63663a0d0a68862c7bbe40aea6f324fd371ef3/robotframework-retryfailed-0.2.0.tar.gz --output retryfailed.tar.gz &&\ 
    pip3 install --no-cache-dir --force-reinstall -v -r requirements.txt && pip3 install ${WORKDIR}/retryfailed.tar.gz

USER testUser

#get python pip versions
RUN pip list





