{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"phone_number": {"type": "string"}, "home_address": {"type": "object", "properties": {"address_line1": {"type": "string"}, "address_line2": {"type": "null"}, "city": {"type": "string"}, "post_code": {"type": "string"}, "county": {"type": "string"}, "country": {"type": "string"}}, "required": ["address_line1", "address_line2", "city", "post_code", "county", "country"]}, "delivery_address": {"type": "object", "properties": {"address_line1": {"type": "string"}, "address_line2": {"type": "null"}, "city": {"type": "string"}, "post_code": {"type": "string"}, "county": {"type": "string"}, "country": {"type": "string"}}, "required": ["address_line1", "address_line2", "city", "post_code", "county", "country"]}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string"}, "region": {"type": "string"}}, "required": ["phone_number", "home_address", "delivery_address", "first_name", "last_name", "email", "region"]}