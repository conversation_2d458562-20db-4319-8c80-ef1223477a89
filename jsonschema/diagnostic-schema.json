{"definitions": {}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://example.com/object1652112948.json", "title": "Root", "type": "object", "required": ["name", "description", "configurationType", "file"], "properties": {"name": {"$id": "#root/name", "title": "Name", "type": "string", "default": "", "examples": ["ASH-del-me-1"], "pattern": "^.*$"}, "description": {"$id": "#root/description", "title": "Description", "type": "string", "default": "", "examples": ["ASH-del-me-1"], "pattern": "^.*$"}, "configurationType": {"$id": "#root/configurationType", "title": "Configurationtype", "type": "string", "default": "", "examples": ["RULESET"], "pattern": "^.*$"}, "file": {"$id": "#root/file", "title": "File", "type": "object", "required": ["version", "createdTimestamp_s", "schemaVersion", "dataDefinition"], "properties": {"version": {"$id": "#root/file/version", "title": "Version", "type": "string", "default": "", "examples": ["2.1.1"], "pattern": "^.*$"}, "createdTimestamp_s": {"$id": "#root/file/createdTimestamp_s", "title": "Createdtimestamp_s", "type": "integer", "examples": [1623234453], "default": 0}, "schemaVersion": {"$id": "#root/file/schemaVersion", "title": "Schemaversion", "type": "string", "default": "", "examples": ["2.1.0"], "pattern": "^.*$"}, "dataDefinition": {"$id": "#root/file/dataDefinition", "title": "Datadefinition", "type": "array", "default": [], "items": {"$id": "#root/file/dataDefinition/items", "title": "Items", "type": "object", "required": ["EcuLogicalAddress", "entityType", "data"], "properties": {"EcuLogicalAddress": {"$id": "#root/file/dataDefinition/items/EcuLogicalAddress", "title": "Eculogicaladdress", "type": "string", "default": "", "examples": ["0x17A4"], "pattern": "^.*$"}, "entityType": {"$id": "#root/file/dataDefinition/items/entityType", "title": "Entitytype", "type": "string", "default": "", "examples": ["DoIP"], "pattern": "^.*$"}, "data": {"$id": "#root/file/dataDefinition/items/data", "title": "Data", "type": "array", "default": [], "items": {"$id": "#root/file/dataDefinition/items/data/items", "title": "Items", "type": "object", "required": ["id", "type", "serviceData"], "properties": {"id": {"$id": "#root/file/dataDefinition/items/data/items/id", "title": "Id", "type": "string", "default": "", "examples": ["D00001"], "pattern": "^.*$"}, "type": {"$id": "#root/file/dataDefinition/items/data/items/type", "title": "Type", "type": "string", "default": "", "examples": ["DTC"], "pattern": "^.*$"}, "serviceData": {"$id": "#root/file/dataDefinition/items/data/items/serviceData", "title": "Servicedata", "type": "string", "default": "", "examples": ["AF"], "pattern": "^.*$"}}}}}}}}}}}