{"type": "object", "properties": {"data": {"type": "object", "properties": {"faqList": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"_path": {"type": "string"}, "_tags": {"type": "array", "items": {"type": "string"}}, "_metadata": {"type": "object", "properties": {"calendarMetadata": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}}}}, "question": {"type": "string"}, "answer": {"type": "object", "properties": {"html": {"type": "string"}, "markdown": {"type": "string"}, "plaintext": {"type": "string"}, "json": {"type": "array", "items": {"type": "object", "properties": {"nodeType": {"type": "string"}, "content": {"type": "array", "items": {"type": "object", "properties": {"nodeType": {"type": "string"}, "value": {"type": "string"}}}}}}}}}}}}}}}}}}