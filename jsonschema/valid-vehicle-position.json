{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "minItems": 1, "properties": {"location": {"type": "object", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}, "address": {"type": "null"}, "accuracy_meter": {"type": "null"}}, "required": ["latitude", "longitude", "address", "accuracy_meter"]}, "vehicle_id": {"type": "string"}, "heading_degree": {"type": "integer"}, "position_created": {"type": "string"}}, "required": ["location", "vehicle_id", "heading_degree", "position_created"]}