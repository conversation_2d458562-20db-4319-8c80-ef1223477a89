{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "configurationType": {"type": "string"}, "file": {"type": "object", "properties": {"version": {"type": "string"}, "createdTimestamp_s": {"type": "integer"}, "schemaVersion": {"type": "string"}, "channelDefinition": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "string"}, "protocol": {"type": "string"}, "topic": {"type": "string"}}, "required": ["id", "protocol", "topic"]}, {"type": "object", "properties": {"id": {"type": "string"}, "protocol": {"type": "string"}, "topic": {"type": "string"}}, "required": ["id", "protocol", "topic"]}, {"type": "object", "properties": {"id": {"type": "string"}, "protocol": {"type": "string"}, "topic": {"type": "string"}}, "required": ["id", "protocol", "topic"]}]}}, "required": ["version", "createdTimestamp_s", "schemaVersion", "channelDefinition"]}}, "required": ["id", "name", "configurationType", "file"]}