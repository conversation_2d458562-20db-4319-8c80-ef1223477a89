{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"metadata": {"type": "object", "properties": {"query": {"type": "string"}}, "required": ["query"]}, "source": {"type": "string"}, "payload": {"type": "array", "items": [{"type": "object", "properties": {"ecuRawResponse": {"type": "string"}, "subReadType": {"type": "string"}, "timestamp": {"type": "integer"}, "ecuEntityType": {"type": "string"}, "ecuName": {"type": "string"}, "readType": {"type": "string"}, "didId": {"type": "string"}, "sessionType": {"type": "string"}}, "required": ["ecuRawResponse", "subReadType", "timestamp", "ecuEntityType", "ecuName", "readType", "didId", "sessionType"]}]}}, "required": ["metadata", "source", "payload"]}