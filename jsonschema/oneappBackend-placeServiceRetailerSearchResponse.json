{"$schema": "http://json-schema.org/draft-04/schema#", "type": "array", "minItems": 1, "items": {"type": "object", "properties": {"trading_title": {"type": "string"}, "address_line": {"type": "string"}, "address_line2": {"type": "string"}, "address_line3": {"type": "string"}, "locality": {"type": "string"}, "county": {"type": "string"}, "postcode": {"type": "string"}, "main_phone": {"type": "string"}, "distance": {"type": "string"}, "longitude": {"type": "number"}, "latitude": {"type": "number"}, "service_types": {"type": "array", "items": [{"type": "string"}]}}, "required": ["trading_title", "address_line", "address_line2", "address_line3", "locality", "county", "postcode", "main_phone", "distance", "longitude", "latitude", "service_types"], "additionalProperties": false}}