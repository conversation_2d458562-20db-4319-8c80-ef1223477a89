{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "configurationType": {"type": "string"}, "file": {"type": "object", "properties": {"SIGNAL_DICTIONARY": {"type": "object", "properties": {"version": {"type": "string"}}, "required": ["version"]}, "DIAGNOSTICS_DICTIONARY": {"type": "object", "properties": {"version": {"type": "string"}}, "required": ["version"]}, "QUERIES": {"type": "object", "properties": {"version": {"type": "string"}}, "required": ["version"]}, "PROTOCOL_CHANNELS": {"type": "object", "properties": {"version": {"type": "string"}}, "required": ["version"]}}, "required": ["SIGNAL_DICTIONARY", "DIAGNOSTICS_DICTIONARY", "QUERIES", "PROTOCOL_CHANNELS"]}, "dateCreated": {"type": "null"}, "author": {"type": "object", "properties": {"username": {"type": "string"}, "groups": {"type": "array", "items": {}}}, "required": ["username", "groups"]}}, "required": ["id", "name", "description", "configurationType", "file", "dateCreated", "author"]}