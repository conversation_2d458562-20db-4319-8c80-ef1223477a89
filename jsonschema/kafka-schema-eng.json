{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"unique_id": {"type": "string"}, "query_id": {"type": "string"}, "event_timestamp_ms": {"type": "number"}, "data": {"type": "array", "items": {"type": "object", "properties": {"unit": {"type": "string"}, "min": {"type": "number"}, "offset": {"type": "number"}, "data_id": {"type": "string"}, "max": {"type": "number"}, "name": {"type": "string"}, "publisher": {"type": "array", "items": [{"type": "string"}]}, "scale": {"type": "number"}, "samples": {"type": "array", "items": [{"type": "object", "properties": {"timestamp_ms": {"type": "integer"}, "type": {"type": "string"}, "value": {"type": "number"}}, "required": ["timestamp_ms", "type", "value"]}]}}, "required": ["data_id", "name", "publisher", "samples"], "optional": ["unit", "min", "max", "offset", "scale"]}}}, "required": ["unique_id", "query_id", "data", "event_timestamp_ms"]}