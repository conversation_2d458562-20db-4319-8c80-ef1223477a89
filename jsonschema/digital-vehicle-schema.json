{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"data": {"type": "array", "items": [{"type": "object", "properties": {"identity": {"type": "object", "properties": {"uniqueId": {"type": "string"}, "identityCreated": {"type": "string"}, "vin": {"type": "string"}, "squishVin": {"type": "string"}, "vehicleReg": {"type": "string"}}, "required": ["uniqueId", "identityCreated", "vin", "squishVin", "vehicleReg"]}, "policies": {"type": "null"}, "assets": {"type": "object", "properties": {"fleetId": {"type": "string"}, "brand": {"type": "string"}, "modelRange": {"type": "string"}, "targetMarket": {"type": "string"}, "soldIntoMarket": {"type": "string"}, "modelYear": {"type": "string"}, "modelName": {"type": "string"}, "trim": {"type": "string"}, "bodyStyle": {"type": "string"}, "driver": {"type": "string"}, "transmission": {"type": "string"}, "engine": {"type": "string"}, "plant": {"type": "null"}, "fuelType": {"type": "null"}, "featureCodes": {"type": "null"}}, "required": ["fleetId", "brand", "modelRange", "targetMarket", "soldIntoMarket", "modelYear", "modelName", "trim", "bodyStyle", "driver", "transmission", "engine", "plant", "fuelType", "featureCodes"]}, "inventory": {"type": "array"}, "applications": {"type": "object", "properties": {"vehicleAnalytics": {"type": "object", "properties": {"status": {"type": "object", "properties": {"enableVA": {"type": "boolean"}, "allowWiFi": {"type": "boolean"}, "enableMirroring": {"type": "boolean"}, "enablePushData": {"type": "boolean"}, "lastVaControlsUpdate": {"type": "string"}}, "required": ["enableVA", "allowWiFi", "enableMirroring", "enablePushData", "lastVaControlsUpdate"]}, "manifest": {"type": "object", "properties": {"protocolChannels": {"type": "string"}, "protocolChannelsSchema": {"type": "string"}, "queries": {"type": "string"}, "queriesSchema": {"type": "string"}, "signalDictionary": {"type": "string"}, "signalDictionarySchema": {"type": "string"}, "vaAppSoftwareVersion": {"type": "string"}, "diagnosticsDictionary": {"type": "string"}, "diagnosticsDictionarySchema": {"type": "string"}, "vaConfigAcceptErrorState": {"type": "null"}, "lastManifestUpdate": {"type": "null"}}, "required": ["protocolChannels", "protocolChannelsSchema", "queries", "queriesSchema", "signalDictionary", "signalDictionarySchema", "vaAppSoftwareVersion", "diagnosticsDictionary", "diagnosticsDictionarySchema", "vaConfigAcceptErrorState", "lastManifestUpdate"]}, "errorMessages": {"type": "null"}}, "required": ["status", "manifest", "errorMessages"]}, "digitalVehicle": {"type": "object", "properties": {"vehicleModifiedDate": {"type": "string"}, "inventoryModifiedDate": {"type": "null"}}, "required": ["vehicleModifiedDate", "inventoryModifiedDate"]}, "obg": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}}, "required": ["isEnabled"]}}, "required": ["vehicleAnalytics", "digitalVehicle", "obg"]}}, "required": ["identity", "policies", "assets", "inventory", "applications"]}]}}, "required": ["data"]}