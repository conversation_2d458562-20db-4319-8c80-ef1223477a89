{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"responseList": {"type": "array", "minItems": 1, "items": {"type": "object", "additionalProperties": false, "properties": {"apiResponse": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "targetId": {"type": "string"}}, "required": ["apiResponse", "targetId"]}}}, "additionalProperties": false, "required": ["responseList"]}