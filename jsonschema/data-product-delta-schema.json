{"$schema": "http://json-schema.org/draft-04/schema#", "type": "array", "items": [{"type": "object", "properties": {"dataProductId": {"type": "string"}, "modified": {"type": ["string", "null"]}, "created": {"type": ["string", "null"]}, "schemaVersion": {"type": "string"}, "comment": {"type": "string"}, "status": {"type": "string"}, "queries": {"type": "array", "items": [{"type": "object", "properties": {"comment": {"type": "string"}, "id": {"type": "string"}, "emit": {"type": "array", "items": [{"type": "string"}]}, "out": {"type": "object", "properties": {"chId": {"type": "array", "items": [{"type": "string"}]}, "eng": {"type": "integer"}}, "required": ["chId", "eng"]}}, "required": ["comment", "id", "emit", "out"]}]}}, "required": ["dataProductId", "modified", "created", "schemaVersion", "comment", "queries", "status"]}]}