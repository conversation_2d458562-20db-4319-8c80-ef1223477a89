{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"vehicleId": {"type": "string"}, "version": {"type": "string"}, "schemaVersion": {"type": "string"}, "dataDefinition": {"type": "array", "items": {"type": "object", "properties": {"entityType": {"type": "string"}, "data": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "serviceData": {"type": "string"}}, "required": ["id", "type", "serviceData"]}, {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "serviceData": {"type": "string"}, "valueType": {"type": "string"}}, "required": ["id", "type", "serviceData", "valueType"]}, {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "serviceData": {"type": "string"}, "valueType": {"type": "string"}}, "required": ["id", "type", "serviceData", "valueType"]}, {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "serviceData": {"type": "string"}, "valueType": {"type": "string"}}, "required": ["id", "type", "serviceData", "valueType"]}, {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "serviceData": {"type": "string"}, "valueType": {"type": "string"}}, "required": ["id", "type", "serviceData", "valueType"]}, {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "serviceData": {"type": "string"}, "valueType": {"type": "string"}}, "required": ["id", "type", "serviceData", "valueType"]}]}, "EcuLogicalAddress": {"type": "string"}}, "required": ["entityType", "data", "EcuLogicalAddress"]}}, "createdTimestamp_s": {"type": "integer"}}, "required": ["vehicleId", "version", "schemaVersion", "dataDefinition", "createdTimestamp_s"]}