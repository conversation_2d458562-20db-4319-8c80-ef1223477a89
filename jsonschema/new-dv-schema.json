{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"identity": {"type": "object", "properties": {"uniqueId": {"type": "string"}, "identityCreated": {"type": "string"}, "vin": {"type": "string"}, "squishVin": {"type": "string"}, "vehicleReg": {"type": "string"}}, "required": ["uniqueId", "identityCreated", "vin", "squishVin", "vehicleReg"]}, "vin": {"type": "string"}, "uniqueId": {"type": "string"}, "sotaEnabled": {"type": "boolean"}, "fleetId": {"type": "null"}, "brand": {"type": "string"}, "modelRange": {"type": "string"}, "targetMarket": {"type": "string"}, "soldIntoMarket": {"type": "string"}, "modelYear": {"type": "string"}, "modelName": {"type": "string"}, "trim": {"type": "string"}, "bodyStyle": {"type": "string"}, "driver": {"type": "string"}, "transmission": {"type": "string"}, "engine": {"type": "string"}, "fuelType": {"type": "array", "items": [{"type": "string"}]}, "plant": {"type": "string"}, "userTermsConditions": {"type": "boolean"}, "vehicleInSync": {"type": "boolean"}, "enableVA": {"type": "boolean"}, "allowWiFi": {"type": "boolean"}, "enableMirroring": {"type": "boolean"}, "enablePushData": {"type": "boolean"}, "lastVaControlsUpdate": {"type": "string"}, "vaAppSoftwareVersion": {"type": "string"}, "signalDictionary": {"type": "string"}, "signalDictionarySchema": {"type": "string"}, "diagnosticsDictionary": {"type": "string"}, "diagnosticsDictionarySchema": {"type": "string"}, "queries": {"type": "string"}, "queriesSchema": {"type": "string"}, "protocolChannels": {"type": "string"}, "protocolChannelsSchema": {"type": "string"}, "lastManifestUpdate": {"type": "string"}, "modules": {"type": "null"}, "modulesModifiedDate": {"type": "string"}, "errorMessages": {"type": "array", "items": [{"type": "object", "properties": {"faultIdentifier": {"type": "string"}, "timestamp": {"type": "string"}, "severity": {"type": "string"}, "errorMetadata": {"type": "string"}}, "required": ["faultIdentifier", "timestamp", "severity", "errorMetadata"]}, {"type": "object", "properties": {"faultIdentifier": {"type": "string"}, "timestamp": {"type": "string"}, "severity": {"type": "string"}, "errorMetadata": {"type": "string"}}, "required": ["faultIdentifier", "timestamp", "severity", "errorMetadata"]}]}, "vehicleStatus": {"type": "string"}, "modifiedDate": {"type": "string"}, "featureCodes": {"type": "null"}, "assets": {"type": "object", "properties": {"fleetId": {"type": "null"}, "brand": {"type": "string"}, "modelRange": {"type": "string"}, "targetMarket": {"type": "string"}, "soldIntoMarket": {"type": "string"}, "modelYear": {"type": "string"}, "modelName": {"type": "string"}, "trim": {"type": "string"}, "bodyStyle": {"type": "string"}, "driver": {"type": "string"}, "transmission": {"type": "string"}, "engine": {"type": "string"}, "plant": {"type": "string"}, "fuelType": {"type": "array", "items": [{"type": "string"}]}, "featureCodes": {"type": "null"}}, "required": ["fleetId", "brand", "modelRange", "targetMarket", "soldIntoMarket", "modelYear", "modelName", "trim", "bodyStyle", "driver", "transmission", "engine", "plant", "fuelType", "featureCodes"]}, "policies": {"type": "null"}}, "required": ["identity", "vin", "uniqueId", "sota<PERSON><PERSON>bled", "fleetId", "brand", "modelRange", "targetMarket", "soldIntoMarket", "modelYear", "modelName", "trim", "bodyStyle", "driver", "transmission", "engine", "fuelType", "plant", "userTermsConditions", "vehicleInSync", "enableVA", "allowWiFi", "enableMirroring", "enablePushData", "lastVaControlsUpdate", "vaAppSoftwareVersion", "signalDictionary", "signalDictionarySchema", "diagnosticsDictionary", "diagnosticsDictionarySchema", "queries", "queriesSchema", "protocolChannels", "protocolChannelsSchema", "lastManifestUpdate", "modules", "modulesModifiedDate", "errorMessages", "vehicleStatus", "modifiedDate", "featureCodes", "assets", "policies"]}