{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"attributes": {"type": "array", "items": [{"type": "object", "properties": {"identifier": {"type": "string"}, "brand": {"type": "string"}, "modelRange": {"type": "string"}, "targetMarket": {"type": "string"}, "modelYear": {"type": "string"}, "trim": {"type": "string"}, "bodyStyle": {"type": "string"}, "driver": {"type": "string"}, "transmission": {"type": "string"}, "engine": {"type": "string"}, "plant": {"type": "string"}, "fuelType": {"type": "array"}, "features": {"type": "array", "items": [{"type": "object", "properties": {"featureCode": {"type": "string"}, "description": {"type": "string"}, "featureLongDescription": {"type": "string"}, "featureName": {"type": "string"}, "featureAlternativeName": {"type": "string"}, "brandDescriptionLandRover": {"type": "string"}, "brandDescriptionJaguar": {"type": "string"}}, "required": ["featureCode", "description", "featureLongDescription", "featureName", "featureAlternativeName", "brandDescriptionLandRover", "brandDescriptionJaguar"]}, {"type": "object", "properties": {"featureCode": {"type": "string"}, "description": {"type": "string"}, "featureLongDescription": {"type": "string"}, "featureName": {"type": "string"}, "featureAlternativeName": {"type": "string"}, "brandDescriptionLandRover": {"type": "string"}, "brandDescriptionJaguar": {"type": "string"}}, "required": ["featureCode", "description", "featureLongDescription", "featureName", "featureAlternativeName", "brandDescriptionLandRover", "brandDescriptionJaguar"]}]}}, "required": ["identifier", "brand", "modelRange", "targetMarket", "modelYear", "trim", "bodyStyle", "driver", "transmission", "engine", "plant", "fuelType", "features"]}]}}, "required": ["attributes"]}