{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"unique_id": {"type": "string"}, "query_id": {"type": "string"}, "data": {"type": "array", "items": [{"type": "object", "properties": {"data_id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "ecu": {"type": "string"}, "service_data": {"type": "string"}, "entity_type": {"type": "string"}, "samples": {"type": "array", "items": [{"type": "object", "properties": {"timestamp_ms": {"type": "integer"}, "type": {"type": "string"}, "value": {"type": "string"}}, "required": ["timestamp_ms", "type", "value"]}]}}, "required": ["data_id", "name", "type", "ecu", "service_data", "entity_type", "samples"]}]}, "event_timestamp_ms": {"type": "integer"}, "fleet_id": {"type": "string"}}, "required": ["unique_id", "query_id", "data", "event_timestamp_ms", "fleet_id"]}