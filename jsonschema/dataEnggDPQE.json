{"Q7105024|S00073|_": {"query_id": "Q7105024", "calc_va_code": "_", "payload_signal_id": "S00073", "dp_id": "Dp_105", "dp_modified": "2022-10-19T15:46", "dp_priority": "Highest", "dp_latency": "Lowest", "dp_privacy": "3-Medium-Maybe", "dp_gps": "none", "dp_perm_list": ["L_App_Privacy"], "element_id": "EL_9999008", "element_type": "net", "signal_id": "S00073", "signal_label": "DoorLatchStatus", "calc_formula": "_sample", "trigger_formula": "|*IntervalTimer|==|00-00:00:05|", "signal_type": "net", "net_unit": "Door latch status.", "net_min": "0", "net_max": "31", "net_publisher": ["BCM_H"], "decode_detail": {"0": "value 0", "1": "value 1", "2": "value 2"}, "decode_abstract": {"=0": "on", ">0": "off"}}}