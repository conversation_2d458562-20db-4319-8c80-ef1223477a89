{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "configurationType": {"type": "string"}, "file": {"type": "object", "properties": {"version": {"type": "string"}, "createdTimestamp_s": {"type": "integer"}, "schemaVersion": {"type": "string"}, "queries": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "string"}, "while": {"type": "array", "items": [{"type": "object", "properties": {"gt": {"type": "array", "items": [{"type": "string"}, {"type": "integer"}]}}, "required": ["gt"]}]}, "when": {"type": "object", "properties": {"periodic": {"type": "array", "items": [{"type": "integer"}]}}, "required": ["periodic"]}, "emit": {"type": "array", "items": [{"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}]}, "out": {"type": "object", "properties": {"chId": {"type": "array", "items": [{"type": "string"}]}, "eng": {"type": "integer"}}, "required": ["chId", "eng"]}}, "required": ["id", "while", "when", "emit", "out"]}, {"type": "object", "properties": {"id": {"type": "string"}, "while": {"type": "array", "items": [{"type": "object", "properties": {"eq": {"type": "array", "items": [{"type": "string"}, {"type": "integer"}]}}, "required": ["eq"]}]}, "when": {"type": "object", "properties": {"periodic": {"type": "array", "items": [{"type": "integer"}]}}, "required": ["periodic"]}, "emit": {"type": "array", "items": [{"type": "string"}, {"type": "string"}, {"type": "string"}]}, "out": {"type": "object", "properties": {"chId": {"type": "array", "items": [{"type": "string"}]}}, "required": ["chId"]}}, "required": ["id", "while", "when", "emit", "out"]}]}}, "required": ["version", "createdTimestamp_s", "schemaVersion", "queries"]}}, "required": ["name", "description", "configurationType", "file"]}