{"type": "array", "items": {"type": "object", "properties": {"trip_id": {"type": "string"}, "start_time": {"type": "string"}, "end_time": {"type": "string"}, "start_address": {"type": "string"}, "end_address": {"type": "string"}, "distance_in_km": {"type": "number"}, "eco_score": {"type": "object", "properties": {"total_eco_score": {"type": "number"}, "throttle_eco_score": {"type": "number"}, "speed_eco_score": {"type": "number"}, "brake_eco_score": {"type": "number"}}, "required": ["total_eco_score", "throttle_eco_score", "speed_eco_score", "brake_eco_score"]}}, "required": ["trip_id", "start_time", "end_time", "start_address", "end_address", "distance_in_km", "eco_score"]}}