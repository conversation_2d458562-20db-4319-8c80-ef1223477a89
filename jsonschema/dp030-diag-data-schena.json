{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"unique_id": {"type": "string"}, "globalRT": {"type": "object", "properties": {"RT_value": {"type": "integer"}, "RT_timestamp_ms": {"type": "integer"}}, "required": ["RT_value", "RT_timestamp_ms"]}, "data": {"type": "array", "items": [{"type": "object", "properties": {"data_id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "ecu": {"type": "string"}, "service_data": {"type": "string"}, "entity_type": {"type": "string"}, "samples": {"type": "array", "items": [{"type": "object", "properties": {"timestamp_ms": {"type": "integer"}, "type": {"type": "string"}, "value": {"type": "string"}}, "required": ["timestamp_ms", "type", "value"]}]}}, "required": ["data_id", "name", "type", "ecu", "service_data", "entity_type", "samples"]}]}, "event_timestamp_ms": {"type": "integer"}, "fleet_id": {"type": "string"}, "query": {"type": "object", "properties": {"id": {"type": "string"}, "cond_list_formula": {"type": "string"}, "trigger_formula": {"type": "string"}}, "required": ["id", "cond_list_formula", "trigger_formula"]}, "data_product": {"type": "object", "properties": {"id": {"type": "string"}, "privacy": {"type": "string"}, "security": {"type": "string"}, "statutory": {"type": "array", "items": [{"type": "string"}]}, "lia": {"type": "array", "items": [{"type": "string"}]}, "agreement": {"type": "array", "items": [{"type": "string"}]}, "consent": {"type": "array", "items": [{"type": "string"}]}, "output_topic": {"type": "string"}}, "required": ["id", "privacy", "security", "statutory", "lia", "agreement", "consent"]}, "not": {"required": ["output_topic"]}}, "required": ["unique_id", "globalRT", "data", "event_timestamp_ms", "fleet_id", "query", "data_product"]}