{"$schema": "http://json-schema.org/draft-04/schema#", "properties": {"address": {"type": "integer"}, "entity_type": {"type": "string"}, "did_type": {"type": "string"}, "data_id": {"type": "string"}, "ecu": {"type": "string"}, "address_in_hex": {"type": "string"}, "did_value_type": {"type": "string"}, "name": {"type": "string"}, "service_data": {"type": "string"}, "type": {"type": "string"}, "samples": {"type": "array", "items": [{"properties": {"timestamp_ms": {"type": "integer"}, "type": {"type": "string"}, "value": {"type": "string"}}, "required": ["timestamp_ms", "type", "value"]}]}}, "required": ["address", "entity_type", "did_type", "data_id", "ecu", "address_in_hex", "did_value_type", "name", "service_data", "type", "samples"]}