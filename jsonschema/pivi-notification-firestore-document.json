{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"msgExpiry": {"type": "string"}, "requestId": {"type": "string"}, "sourceId": {"type": "string"}, "sourcePriority": {"type": "integer"}, "sourceTimestamp": {"type": "string"}, "target": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"]}, "targetData": {"type": "object", "properties": {"action": {"type": "string"}, "payload": {"type": "object", "properties": {"message": {"type": "string"}, "refillRequiredIn": {"type": "integer"}, "warningCode": {"type": "integer"}}, "required": ["message", "refillRequiredIn", "warningCode"]}, "targetApp": {"type": "string"}}, "required": ["action", "payload", "targetApp"]}, "token": {"type": "string"}}, "required": ["msgExpiry", "requestId", "sourceId", "sourcePriority", "sourceTimestamp", "target", "targetData"]}