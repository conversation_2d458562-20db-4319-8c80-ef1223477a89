{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"version": {"type": "string"}, "source": {"type": "string"}, "vehicle_unique_id": {"type": "string"}, "metadata": {"type": "object", "properties": {"query": {"type": "object", "properties": {"id": {"type": "string"}, "cond_list_formula": {"type": "string"}, "trigger_formula": {"type": "string"}, "event_timestamp_ms": {"type": "integer"}}, "required": ["id", "cond_list_formula", "trigger_formula", "event_timestamp_ms"]}, "data_product": {"type": "object", "properties": {"id": {"type": "string"}, "privacy": {"type": "string"}, "security": {"type": "string"}, "statutory": {"type": "string"}, "lia": {"type": "array", "items": [{"type": "string"}]}, "agreement": {"type": "array", "items": [{"type": "string"}]}, "consent": {"type": "array", "items": [{"type": "string"}]}}, "required": ["id", "privacy", "security", "statutory", "lia", "agreement", "consent"]}}, "required": ["query", "data_product"]}, "payload": {"type": "array", "items": [{"type": "object", "properties": {"ecu_raw_response": {"type": "string"}, "timestamp_ms": {"type": "integer"}, "ecu_entity_type": {"type": "string"}, "data_id": {"type": "string"}, "ecu_name": {"type": "string"}, "diagnostic_type": {"type": "string"}, "service_data": {"type": "string"}, "session_type": {"type": "string"}, "global_real_time": {"type": "integer"}, "global_real_time_timestamp_ms": {"type": "integer"}}, "required": ["ecu_raw_response", "timestamp_ms", "ecu_entity_type", "data_id", "ecu_name", "diagnostic_type", "service_data", "session_type", "global_real_time", "global_real_time_timestamp_ms"]}]}}, "required": ["version", "source", "vehicle_unique_id", "metadata", "payload"]}