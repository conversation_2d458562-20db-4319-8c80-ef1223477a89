{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["version", "createdTimestamp_s", "schemaVersion", "dataDefinition"], "properties": {"version": {"$id": "#root/version", "title": "Version", "type": "string", "default": "", "examples": ["2.1.1"], "pattern": "^.*$"}, "createdTimestamp_s": {"$id": "#root/createdTimestamp_s", "title": "Createdtimestamp_s", "type": "integer", "examples": [1623234453], "default": 0}, "schemaVersion": {"$id": "#root/schemaVersion", "title": "Schemaversion", "type": "string", "default": "", "examples": ["2.1.0"], "pattern": "^.*$"}, "dataDefinition": {"$id": "#root/dataDefinition", "title": "Datadefinition", "type": "array", "default": [], "items": {"$id": "#root/dataDefinition/items", "title": "Items", "type": "object", "required": ["id", "pubFrameId", "IslFrameId"], "properties": {"id": {"$id": "#root/dataDefinition/items/id", "title": "ID", "type": "string", "default": "", "examples": ["S00001"], "pattern": "^.*$"}, "pubFrameId": {"$id": "#root/dataDefinition/items/pubFrameId", "title": "PubFrameId", "type": "integer", "default": "", "examples": [0]}, "IslFrameId": {"$id": "#root/dataDefinition/items/IslFrameId", "title": "IslFrameId", "type": "integer", "default": "", "examples": [0]}}}}}}