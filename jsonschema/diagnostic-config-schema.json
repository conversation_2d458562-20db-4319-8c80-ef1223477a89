{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["version", "createdTimestamp_s", "schemaVersion", "dataDefinition"], "properties": {"version": {"$id": "#root/version", "title": "Version", "type": "string", "default": "", "examples": ["2.1.1"], "pattern": "^.*$"}, "createdTimestamp_s": {"$id": "#root/createdTimestamp_s", "title": "Createdtimestamp_s", "type": "integer", "examples": [1623234453], "default": 0}, "schemaVersion": {"$id": "#root/schemaVersion", "title": "Schemaversion", "type": "string", "default": "", "examples": ["2.1.0"], "pattern": "^.*$"}, "dataDefinition": {"$id": "#root/dataDefinition", "title": "Datadefinition", "type": "array", "default": [], "items": {"$id": "#root/dataDefinition/items", "title": "Items", "type": "object", "required": ["EcuLogicalAddress", "entityType", "data"], "properties": {"EcuLogicalAddress": {"$id": "#root/dataDefinition/items/EcuLogicalAddress", "title": "Eculogicaladdress", "type": "string", "default": "", "examples": ["0x17A4"], "pattern": "^.*$"}, "entityType": {"$id": "#root/dataDefinition/items/entityType", "title": "Entitytype", "type": "string", "default": "", "examples": ["DoIP"], "pattern": "^.*$"}, "data": {"$id": "#root/dataDefinition/items/data", "title": "Data", "type": "array", "default": [], "items": {"$id": "#root/dataDefinition/items/data/items", "title": "Items", "type": "object", "required": ["id", "type", "serviceData"], "properties": {"id": {"$id": "#root/dataDefinition/items/data/items/id", "title": "Id", "type": "string", "default": "", "examples": ["D00001"], "pattern": "^.*$"}, "type": {"$id": "#root/dataDefinition/items/data/items/type", "title": "Type", "type": "string", "default": "", "examples": ["DTC"], "pattern": "^.*$"}, "serviceData": {"$id": "#root/dataDefinition/items/data/items/serviceData", "title": "Servicedata", "type": "string", "default": "", "examples": ["AF"], "pattern": "^.*$"}}}}}}}}}