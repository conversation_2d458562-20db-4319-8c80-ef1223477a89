#!/bin/bash
exitCode=1
testSuite=$1

if [[ -z "$testSuite" ]]; then
    testSuite="*.robot"

fi

getBaseImage () {
    if [[ $usePytest != True ]]; then
        mkdir -p robot
        cd robot
    fi
    rm -rf vcdp-e2e-tests-json-schema jsonschema graphqlschema
    <NAME_EMAIL>:D9/testing/vcdp-e2e-tests-base-image.git
    if [[ $useSharedLibs != False ]]; then
       mkdir -p jsonschema libs graphqlschema libsHil
       cp -rf vcdp-e2e-tests-base-image/forgeRockjson .
       cp -rf vcdp-e2e-tests-base-image/graphqlschema .
       cp -rf vcdp-e2e-tests-base-image/jsonschema .
       cp -rf vcdp-e2e-tests-base-image/libs/*.py libs
       cp -rf vcdp-e2e-tests-base-image/libsHil/*.py libsHil

           #bring in the changes from the remote gitignore and merge them with project gitlab ignore
        sort -o ../.gitignore{,}
        sort -o vcdp-e2e-tests-base-image/.gitignoreImport{,}
        diff -U $(wc -l < vcdp-e2e-tests-base-image/.gitignoreImport) vcdp-e2e-tests-base-image/.gitignoreImport ../.gitignore | sed -n 's/^-//p' | tail -n +2 >> ../.gitignore
        sort -o ../.gitignore{,}
    fi
    
    cp -f vcdp-e2e-tests-base-image/getSecretsFromAWSSecretsManager.py ../.
    cp -f vcdp-e2e-tests-base-image/getConfigFromFile.py ../. 
    rm -rf vcdp-e2e-tests-base-image
    # remove log files if already exists on projects
    rm -rf .././logging_config.yaml
    if [[ $usePytest != True ]]; then
        cd ..
    fi


}

getTestsPytest(){
    
    cd tests
    rm -rf features features.zip

    mkdir features
    
    # get test scenarios from a JIRA "Test plan"
    curl -s --location --request GET 'https://jira.devops.jlr-apps.com/rest/raven/1.0/export/test?keys='$XRAY_TEST_SUITE_ID \
    --header 'Accept=application/zip' \
    --header 'Authorization: Bearer '"$jiraToken"'' \
    --output features.zip

    unzip -qq features.zip -d features || {
        mkdir features
        echo "failed to unzip file, assuming this is a single feature file project"
        mv features.zip features/1.feature

    }
    for file in features/*.feature; do
        sed -i -e 's/  */ /g' $file
    done
    cd ..
}

getTestsRobot () {
    mkdir -p robot
    cd robot
    rm -rf testsuites featureExport features.zip

    # get test scenarios from a JIRA "Test plan"
    curl -s --location --request GET 'https://jira.devops.jlr-apps.com/rest/raven/1.0/export/test?keys='$XRAY_TEST_SUITE_ID \
    --header 'Accept=application/zip' \
    --header 'Authorization: Bearer '"$jiraToken"'' \
    --output features.zip

    unzip -qq features.zip -d featureExport || {
        mkdir featureExport
        echo "failed to unzip file, assuming this is a single feature file project"
        mv features.zip featureExport/1.feature

    }
    cd featureExport

    for file in *.feature; do
        gherkin2robotframework $file
    done

    mkdir ../testsuites
    for file in *.robot; do
        mv $file ../testsuites/$file
    done

    cd ../testsuites
    rm *_step_definitions.robot

    if [[ -z "$resourceFile" ]]; then
        resourceFile="app-config.robot" #change default resource file name

    fi

    sed_cmd="sed"
    if [[ $(uname -sr) == "Darwin"* ]]; then
    echo "OSX Detected"
    if [[ -z $(which gsed) ]]; then
        echo "GNU sed not installed"
        echo "Run 'brew install gsed' and try again"
        exit
    else
        sed_cmd="gsed"
    fi
    fi

    for file in *.robot; do
        $sed_cmd -i -e 's/TEST_//g' $file
        $sed_cmd -i '/step_definitions.robot/d' $file
        $sed_cmd -i -e 's/ ${/    ${/g' $file
        $sed_cmd -i '/Metadata/d' $file
        $sed_cmd -i -e 's/Given\s/GIVEN /g' $file
        $sed_cmd -i -e 's/When\s/WHEN /g' $file
        $sed_cmd -i -e 's/Then\s/THEN /g' $file
        $sed_cmd -i -e 's/And\s/AND /g' $file
        $sed_cmd -i 's/txt/robot/' $file
        $sed_cmd -i 's/Resource	keywords.robot//' $file
        $sed_cmd -i "/Test Cases/ i Resource        ../resource/$resourceFile" $file
        if [[ ! -z "$suiteSetUpStep" ]]; then  #add a setup line to robot files
            $sed_cmd -i "/Test Cases/ i Suite Setup      $suiteSetUpStep" $file
        fi
        if [[ ! -z "$testSetUpStep" ]]; then  #add a setup line to robot files
            $sed_cmd -i "/Test Cases/ i Test Setup       $testSetUpStep" $file
        fi
        if [[ ! -z "$testTearDownStep" ]]; then #add a teardown line to robot files
            $sed_cmd -i "/Test Cases/ i Test Teardown 	       $testTearDownStep" $file

        fi
        if [[ ! -z "$suiteTearDownStep" ]]; then #add a teardown line to robot files
            $sed_cmd -i "/Test Cases/ i Suite Teardown 	       $suiteTearDownStep" $file

        fi
        $sed_cmd -i '/Test Cases/ i \\' $file         # blank line
    done
    cd ../..
    rm -rf featureExport features.zip
}
getSecrets () {

    #Get secrets from secrets manager and load them as environment variables
    python3 getSecretsFromAWSSecretsManager.py

}

setSecrets () {

    while read line
    do
        export "$line"
    done < ci_secrets.env  &> /dev/null
}
runTestsPytest(){
    excludeList='not AutomatedTest-NotReady or not AutomatedTest-Blocked or not AutomatedVCDPTest-Isolated or not AutomatedVCDPTest-RETIRED'

    if [[ ! -z $excludeOverride ]]; then
        excludeList=" or not $excludeOverride"
    fi

    if [[ $excludeNothing == True ]]; then
        unset excludeList
    fi

    if [[ ! -z $includedTagOverride ]]; then
        includedTags=" -k $includedTagOverride --junitxml=$includedTagOverride.xml"
    fi

    pytest -k "$excludeList" $includedTags --bdd-report=report.html --self-contained-html --junitxml=$includedTagOverride.xml
    exitCode=$?
}
runTestsRobot () {
    excludeList="--exclude AutomatedTest-NotReadyORAutomatedTest-BlockedORAutomatedVCDPTest-IsolatedORAutomatedVCDPTest-RETIRED"
   
    if [[ ! -z $excludeOverride ]]; then
        excludeList="--exclude $excludeOverride"

    fi

    if [[ $excludeNothing == True ]]; then
        unset excludeList
    fi

    if [[ ! -z $includedTagOverride ]]; then
        includedTags="--include $includedTagOverride --output $includedTagOverride.xml"

    fi
    echo $excludeList
    echo $includedTags
    cd robot/testsuites
    if [[ $usePabot != True ]]; then 
        if [[ ! -n "$logLevel" || "$logLevel" == "INFO" ]]; then
            robot $excludeList --xunit junitOutput.xml -v useProxy:False $testSuite $includedTags
        else
            robot --loglevel=$logLevel $excludeList --xunit junitOutput.xml -v useProxy:False $testSuite $includedTags
        fi        
    else
        if [[ $splitOnTests == True ]]; then
            pabot  --testlevelsplit $excludeList $includedTags --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
        else
            if [[ ! -z $pabotProcessors  ]]; then
                pabot $excludeList $includedTags --processes $pabotProcessors --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
            else
                pabot $excludeList $includedTags --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
            fi
        fi
    fi
    exitCode=$?
}

setAWSProfileName () {

    if [ -z ${AWS_PROFILE+x} ]; then 
        echo "AWS_PROFILE=vcdp-developers" >> ci_config.env
    else 
        echo "AWS_PROFILE=${AWS_PROFILE}" >> ci_config.env
    fi

}


pullAndSetConfig () {

    if [ -f "getConfigFromFile.py" ]; then
        python3 getConfigFromFile.py
        setAWSProfileName
        while read line
        do
            export "$line"
        done < ci_config.env  &> /dev/null
    else 
        echo "base image not found - pulling down base image"
        getBaseImage
        export pullBaseImage=False
        python3 getConfigFromFile.py
        setAWSProfileName
        while read line
        do
            export "$line"
        done < ci_config.env  &> /dev/null
    fi

}

sendSlackReports () {
    cd -
    python3 robot/libs/slackIntegration.py

}

#if we have a local .env file load it in
[ -s .env ] && export $(cat .env | xargs) &> /dev/null

pullAndSetConfig


if [[ $pullBaseImage != False ]]; then
    getBaseImage
fi

if [[ $pullSecrets != False ]]; then
    getSecrets
fi

setSecrets

if [[ $pullTests != False ]] && [[ $usePytest != True ]]; then
    getTestsRobot
fi

if [[ $pullTests != False ]] && [[ $usePytest = True ]]; then
    
    getTestsPytest
fi

if [[ $usePytest = True ]]; then
    cd tests
    runTestsPytest
else
    runTestsRobot
fi

if [[ $sendReports = True ]]; then
    sendSlackReports
fi

exit $exitCode