# import os
# import time
# import boto3
# import boto3.dynamodb
# import traceback
# import defusedxml.ElementTree as dxml
#
#
# def connect_to_dynamodb():
#    try:
#        session = boto3.session.Session()
#
#        dynamodb = session.resource(service_name="dynamodb", region_name="eu-west-2")
#
#    except ConnectionError as ce:
#        print("Database Connection error" + str(ce) + ":: " + str(ce))
#        return -1
#
#    except TimeoutError:
#        print("Database connection FAILED")
#        traceback.print_exc()
#        return -1
#
#    return dynamodb
#
#
# def get_item_from_dynamo_db(table_name, pipeline_name, test_name):
#    dynamodb = connect_to_dynamodb()
#    table = dynamodb.Table(table_name)
#    response = table.get_item(Key={"pipeline-name": pipeline_name, "test-name": test_name})
#    return response
#
#
# def save_item_in_table(pipeline_name, test_name):
#    table_name = "seit-test-analysis"
#
#    dynamodb = connect_to_dynamodb()
#    table = dynamodb.Table(table_name)
#
#    event_time = int(time.time())
#
#    response = get_item_from_dynamo_db(table_name, pipeline_name, test_name)
#    try:
#        current_number_of_failures = response["Item"]["number_of_failures"]
#        inital_failure_timestamp = response["Item"]["inital_failure_timestamp"]
#        response = table.put_item(
#            Item={
#                "pipeline-name": pipeline_name,
#                "test-name": test_name,
#                "inital_failure_timestamp": inital_failure_timestamp,
#                "failure_timestamp": event_time,
#                "number_of_failures": current_number_of_failures + 1,
#                "expiry_time": event_time + 86400,  # 1 day since last failure
#            }
#        )
#    except KeyError:
#        response = table.put_item(
#            Item={
#                "pipeline-name": pipeline_name,
#                "test-name": test_name,
#                "inital_failure_timestamp": event_time,
#                "failure_timestamp": event_time,
#                "number_of_failures": 1,
#                "expiry_time": event_time + 86400,  # 1 day since last failure
#            }
#        )
#
#    return response
#
#
# def get_xml_file_name():
#    if os.environ.get("includedTagOverride") is not None:
#        title = str(os.environ.get("includedTagOverride"))
#    else:
#        title = "output"
#    return title
#
#
# def extract_test_run_data():
#    filename = get_xml_file_name()
#    tree = dxml.parse(f"robot/testsuites/{filename}.xml")
#    root = tree.getroot()
#    for test in root.findall(".//test"):
#        test_name = test.attrib.get("name")
#
#        test_status = test.findall(".//status")
#        for keyword in test_status:
#            if keyword.attrib.get("status") == "FAIL":
#                save_item_in_table(os.getenv("CI_PROJECT_NAME"), test_name)
#                break
#
#
# if __name__ == "__main__":
#    extract_test_run_data()
#

print("test analisys has been removed from the base image")
