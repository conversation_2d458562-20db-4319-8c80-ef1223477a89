{"authId": "", "callbacks": [{"type": "PasswordCallback", "output": [{"name": "prompt", "value": "Password"}], "input": [{"name": "IDToken1", "value": ""}], "_id": 0}, {"type": "ChoiceCallback", "output": [{"name": "prompt", "value": "fr_vc_login_password_route"}, {"name": "choices", "value": ["fr_vc_login", "fr_vc_account_recovery"]}, {"name": "defaultChoice", "value": 0}], "input": [{"name": "IDToken2", "value": 0}], "_id": 1}], "stage": "customer-login-password"}