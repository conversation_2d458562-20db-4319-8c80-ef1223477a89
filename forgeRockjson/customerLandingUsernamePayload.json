{"authId": "", "callbacks": [{"type": "SelectIdPCallback", "output": [{"name": "providers", "value": [{"provider": "google-social-auth", "uiConfig": {"buttonImage": "images/g-logo.png", "buttonCustomStyle": "background-color: #fff; color: #757575; border-color: #ddd;", "buttonClass": "", "buttonDisplayName": "Google", "buttonCustomStyleHover": "color: #6d6d6d; background-color: #eee; border-color: #ccc;", "iconClass": "fa-google", "iconFontColor": "white", "iconBackground": "#4184f3"}}, {"provider": "localAuthentication"}]}, {"name": "value", "value": ""}], "input": [{"name": "IDToken1", "value": "localAuthentication"}], "_id": 0}, {"type": "ValidatedCreateUsernameCallback", "output": [{"name": "policies", "value": {"policyRequirements": ["REQUIRED", "VALID_TYPE", "MATCH_REGEXP"], "fallbackPolicies": null, "name": "mail", "policies": [{"policyRequirements": ["REQUIRED"], "policyId": "required"}, {"policyRequirements": ["VALID_TYPE"], "policyId": "valid-type", "params": {"types": ["string"]}}, {"policyId": "regexpMatches", "params": {"regexp": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]{2,}\\.[a-zA-Z]{2,4}$"}, "policyRequirements": ["MATCH_REGEXP"]}, {"policyId": "not-empty", "policyRequirements": ["REQUIRED"]}], "conditionalPolicies": null}}, {"name": "failedPolicies", "value": []}, {"name": "validateOnly", "value": false}, {"name": "prompt", "value": "User Email"}], "input": [{"name": "IDToken2", "value": "<EMAIL>"}, {"name": "IDToken2validateOnly", "value": false}], "_id": 1}], "stage": "customer-landing-username"}