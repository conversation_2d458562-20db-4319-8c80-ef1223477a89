# Reads config part of config.yml file
import re
import yaml
import os


def sanitize_input(input):
    base_path = os.getcwd()
    base_path_present = False
    if input.startswith(base_path):
        if input.startswith(base_path + "/tests/fixtures"):
            base_path = base_path + "/tests/fixtures"
        base_path_present = True
    if input.endswith(".yml"):
        input = input.replace(base_path, "")
        input = input.replace(".yml", "")
        input = re.sub(r"[^a-zA-Z0-9_-]", "", input)
        input = input + ".yml"
        if base_path_present:
            input = base_path + "/" + input
        return input
    return "input does not start with the base path or end with .yml"


class GetConfig:
    def read_config_yaml(self, file_name, key_type):
        file_name = sanitize_input(file_name)
        with open(file_name, "r") as file:
            configs = yaml.safe_load(file)

            config_array = []
        if configs[key_type]:
            for config in configs[key_type]:
                config_entry = []

                for key in config:
                    config_entry.append(key)

                    config_entry.append(config[key])
                config_array.append(config_entry)

        return config_array

    def write_config_environment_variable_to_file(self, key, value, envfile):
        envfile.write(key + "=" + str(value) + "\n")

    def main(self):
        envfile = open("ci_config.env", "w")

        env_secrets_file = os.environ.get("ENV_SECRETS_YAML")

        # For config items - ie pulling new tests or not
        secrets_filename = env_secrets_file if env_secrets_file else "config.yml"
        try:
            config_array = self.read_config_yaml(secrets_filename, "config")
        except OSError:
            raise OSError(
                "cannot find your config file looked for a file called "
                + secrets_filename
                + " this can be changed by setting the ENV_SECRETS_YAML variable"
            )

        if config_array:
            for config_item in config_array:
                self.write_config_environment_variable_to_file(config_item[0], config_item[1], envfile)

        envfile.close()


print(os.getcwd())
if __name__ == "__main__":
    getConfig = GetConfig()
    getConfig.main()
