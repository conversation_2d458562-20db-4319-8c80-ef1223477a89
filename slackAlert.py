import re
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
import os
import defusedxml.ElementTree as ET
from bs4 import BeautifulSoup

__version__ = "0.0.1"
ROBOT_LIBRARY_VERSION = __version__
ROBOT_LIBRARY_SCOPE = "GLOBAL"

failed_tests = 0  # Global variable to store the number of failed tests


def get_slack_bot_token():
    if os.environ.get("SLACK_BOT_TOKEN") is not None:
        slack_token = str(os.environ.get("SLACK_BOT_TOKEN"))
    else:
        slack_token = str(os.environ.get("SLACK_BOT_TOKEN_VCDP"))
    return slack_token


def read_results_file(file="test_summary.txt"):
    try:
        with open(file, "r") as file:
            file_contents = file.read()
    except FileNotFoundError:
        return "file was not found."

    return file_contents


def get_correct_icon(failed_tests):
    if failed_tests == 0:
        icon = ":white_check_mark: Tests Passed"
    else:
        if os.environ.get("CUSTOM_SLACK_ALERT_ICON") is not None:
            icon = os.environ.get("CUSTOM_SLACK_ALERT_ICON")
        else:
            icon = ":crossbox: Tests Failed"
    return icon


def get_title():
    if os.environ.get("CUSTOM_SLACK_ALERT_TITLE") is not None:
        title = str(os.environ.get("CUSTOM_SLACK_ALERT_TITLE"))
    else:
        title = str(os.environ.get("XRAY_TEST_SUITE_ID"))
    return title


def get_xml_file_name():
    if os.environ.get("includedTagOverride") is not None:
        title = str(os.environ.get("includedTagOverride"))
        title = re.sub(r"[^a-zA-Z0-9_-]", "", title)
    elif os.environ.get("usePytest"):
        title = "results"
    else:
        title = "output"
    return title


def write_alert_message(total_tests, passed_tests, failed_tests, error_messages=None, output_file="test_summary.txt"):
    max_error_message_size = 2500
    project_name = str(os.environ.get("CI_PROJECT_NAME"))
    job_id = str(os.environ.get("CI_JOB_ID"))
    project_namespace = str(os.environ.get("CI_PROJECT_NAMESPACE"))
    project_group = re.sub(r"^.*?D9/", "", project_namespace)
    if os.environ.get("usePytest"):
        path_to_log = (
            f"http://d9.docs.git-gdd.sdo.jlrmotor.com/-/{project_group}/{project_name}/-/jobs/{job_id}/artifacts/tests/report.html"  # NOSONAR
        )
    else:
        path_to_log = (
            f"http://d9.docs.git-gdd.sdo.jlrmotor.com/-/{project_group}/{project_name}/-/jobs/{job_id}/artifacts/robot/testsuites/log.html"  # NOSONAR
        )
    icon = get_correct_icon(failed_tests)
    title = get_title()
    testplan_id = str(os.environ.get("XRAY_TEST_SUITE_ID"))
    with open(output_file, "w") as file:
        file.write(f"*{icon} Test Report for Test Plan: {title}* [<{path_to_log}|Report>] \n")
        file.write("```-----------------------------\n")
        file.write(f"Test Plan: {testplan_id}\n")
        file.write(f"Total Tests: {total_tests}   \n")
        file.write(f"Passed Tests: {passed_tests} \n")
        file.write(f"Failed Tests: {failed_tests} \n")
        file.write(" ----------------------------\n")
        if error_messages is not None:
            file.write("Error Messages:              \n")
            file.write(" ----------------------------\n")
            error_message_char_length = 0
            for msg in error_messages:
                error_message_char_length = error_message_char_length + (len(msg))
                if error_message_char_length < max_error_message_size:
                    file.write(f"{msg}\n")
                else:
                    print(f"maximum error message size reached - {max_error_message_size}")
                    break
            file.write(" ----------------------------\n")
        if os.environ.get("SLACK_ALERT_SEIT_ID") is not None:
            seit_contact = str(os.environ.get("SLACK_ALERT_SEIT_ID"))
            file.write(f"SEIT Contact: {seit_contact} \n")
        file.write("```")


def extract_test_run_data():
    """
    Method extracts test run data from the project and creates a text file in repo to use as a payload
    """
    global failed_tests  # Declare the global variable
    filename = get_xml_file_name()
    if os.environ.get("usePytest"):
        tree = ET.parse(f"tests/{filename}.xml")
        root = tree.getroot()
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        for suite in root.findall(".//testsuite"):
            failed = int(suite.attrib.get("failures", 0))
            total_tests = int(suite.attrib.get("tests", 0))
            passed_tests += total_tests - failed
            failed_tests += failed
        write_alert_message(total_tests, passed_tests, failed_tests)

    else:
        tree = ET.parse(f"robot/testsuites/{filename}.xml")
        root = tree.getroot()
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        for suite in root.findall(".//suite"):
            first_stat = suite.find(".//stat")
            if first_stat is not None:
                passed = int(first_stat.attrib.get("pass", 0))
                failed = int(first_stat.attrib.get("fail", 0))
                total_tests += passed + failed
                passed_tests += passed
                failed_tests += failed
        if failed_tests != 0:
            with open(f"robot/testsuites/{filename}.xml", "r") as f:
                data = f.read()
            soup = BeautifulSoup(data, "xml")
            fail_msgs = soup.find_all("msg", {"level": "FAIL"})
            error_messages = [msg.get_text(strip=True) for msg in fail_msgs]
            write_alert_message(total_tests, passed_tests, failed_tests, error_messages)
        else:
            write_alert_message(total_tests, passed_tests, failed_tests)


def send_slack_message():
    """
    Method uses a filepath as a payload to send a slack message to a particular channel
    """
    global failed_tests  # Declare the global variable
    slack_token = get_slack_bot_token()
    file_contents = read_results_file()
    client = WebClient(token=slack_token)
    try:
        # Always send the report to the primary channel
        channel_id = str(os.environ.get("SLACK_ALERT_CHANNEL"))
        client.chat_postMessage(channel=channel_id, text=file_contents, unfurl_links=False, unfurl_media=False)
        print("Report sent successfully!")
        secondary_channel_id = os.environ.get("SLACK_ALERT_CHANNEL_SECONDARY")
        send_only_failure_report = os.environ.get("SEND_ONLY_FAILURE_REPORT_TO_SECONDARY_CHANNEL")
        # Send failure report to the secondary channel if conditions are met
        if secondary_channel_id:
            if not send_only_failure_report or (send_only_failure_report and failed_tests != 0):
                client.chat_postMessage(channel=str(secondary_channel_id), text=file_contents, unfurl_links=False, unfurl_media=False)
        print("Failure report sent successfully!")
    except SlackApiError as e:
        assert e.response["ok"] is False
        assert e.response["error"]
        print(f"Got an error: {e.response['error']}")


if __name__ == "__main__":
    extract_test_run_data()
    send_slack_message()
