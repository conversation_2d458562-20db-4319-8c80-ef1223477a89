# Get secrets from AWS secrets manager and adds them as environment variables using there key as the variable name
import base64
import binascii
import boto3
import json
from botocore.exceptions import ClientError
from pathlib import Path
import yaml
import os
import botocore
from dateutil.tz import tzlocal
from datetime import datetime

from getConfigFromFile import sanitize_input


class GetSecrets:
    def __init__(self):
        self.aws_profile = "vcdp-developers"

    def get_aws_profile(self):
        return self.aws_profile

    def set_aws_profile(self, a):
        self.aws_profile = a

    def read_secret_yaml(self, file_name, key_type):
        file_name = sanitize_input(file_name)
        with open(file_name, "r") as file:
            secrets = yaml.safe_load(file)

            secret_array = []
        if secrets[key_type]:
            for secret in secrets[key_type]:
                secret_entry = []

                for key in secret:
                    secret_entry.append(key)
                    key_array = []

                    for value in secret[key]:
                        key_array.append(value)
                    secret_entry.append(key_array)
                secret_array.append(secret_entry)

        return secret_array

    def assume_crypo_role(self):
        print("pulling secret from cypto account")
        base_session = boto3.session.Session()._session
        account_id = boto3.client("sts").get_caller_identity()["Account"]
        arn = boto3.client("sts").get_caller_identity()["Arn"]

        match arn:
            case arn if "Accenture-access-dev" in arn:
                role_arn = "arn:aws:iam::" + str(account_id) + ":role/jlr-gdd-seit-accenture-hivemq-role"
            case arn if "paak-developers" in arn or "paak-pre-production-gitlab-runner" in arn:
                role_arn = "arn:aws:iam::" + str(account_id) + ":role/jlr-gdd-seit-paak-hivemq-role"
            case _:
                role_arn = "arn:aws:iam::" + str(account_id) + ":role/jlr-gdd-seit-hivemq-role"

        fetcher = botocore.credentials.AssumeRoleCredentialFetcher(
            client_creator=base_session.create_client,
            source_credentials=base_session.get_credentials(),
            role_arn=role_arn,
            extra_args={
                # 'RoleSessionName': None # set this if you want something non-default
            },
        )

        creds = botocore.credentials.DeferredRefreshableCredentials(
            method="assume-role", refresh_using=fetcher.fetch_credentials, time_fetcher=lambda: datetime.now(tzlocal())
        )
        botocore_session = botocore.session.Session()
        botocore_session._credentials = creds
        session = boto3.Session(botocore_session=botocore_session)
        client = session.client(service_name="secretsmanager", region_name="eu-west-2")

        return client, account_id, arn

    def get_hive_passphase_and_set_variable(self, envfile, client, account_id, arn):
        print("pulling secret from cypto account - pass phrase")
        match str(account_id):
            case "************":  # dev
                passcode = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-e2e-test_to_hivemq_dev_2024-11-06_key.passphrase"
            case "************" | "************":  # preprod, vcdp/ecosys
                match arn:
                    case arn if "Accenture-access-dev" in arn:  # if we are using the accenture role
                        passcode = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-accenture-e2e-test_to_mqtt_client_keystore_pre_production_2025-05-06_key.passphrase"  # noqa : E501
                    case _:
                        passcode = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-e2e-test_to_mqtt_client_keystore_pre_production_2024-11-11_key.passphrase"  # noqa : E501
            case "************":
                passcode = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-paak-e2e-test_to_mqtt_client_keystore_pre_production_2025-02-27_key.passphrase"  # noqa : E501
            case _:
                raise ValueError("Unknown account ID")

        response_passcode = client.get_secret_value(SecretId=passcode)
        envfile.write("hive_cert_passphrase=" + response_passcode.get("SecretString") + "\n")

    def download_secrets_cypto_account(self, envfile):
        print("pulling secret from cypto account - certificates")
        client, account_id, arn = self.assume_crypo_role()
        match str(account_id):
            case "************":  # dev
                chain_pem = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-e2e-test_to_hivemq_dev_2024-11-06_chain.pem"
                key = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-e2e-test_to_hivemq_dev_2024-11-06.key"
            case "************" | "************":  # preprod, vcdp/ecosys
                match arn:
                    case arn if "Accenture-access-dev" in arn:  # if we are using the accenture role
                        print("accessing Accenture certificates")
                        chain_pem = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-accenture-e2e-test_to_mqtt_client_keystore_pre_production_2025-06-05-chain.pem"  # noqa : E501
                        key = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-accenture-e2e-test_to_mqtt_client_keystore_pre_production_2025-06-05.key"  # noqa : E501
                    case _:
                        print("accessing vcdp-developers certificates")
                        chain_pem = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-e2e-test_to_mqtt_client_keystore_pre_production_2024-11-11_chain.pem"  # noqa : E501
                        key = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-e2e-test_to_mqtt_client_keystore_pre_production_2024-11-11.key"  # noqa : E501
            case "************":
                chain_pem = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-paak-e2e-test_to_mqtt_client_keystore_pre_production_2025-02-27_chain.pem"  # noqa : E501
                key = "arn:aws:secretsmanager:eu-west-2:************:secret:certificate/seit-paak-e2e-test_to_mqtt_client_keystore_pre_production_2025-02-27.key"  # noqa : E501
            case _:
                raise ValueError("Unknown account ID")

        response_cert = client.get_secret_value(SecretId=chain_pem)
        with open("hive_certfile", "w") as cert_file:
            cert_file.write(response_cert.get("SecretString"))
        envfile.write("hive_certfile=" + str(Path.cwd()) + "/hive_certfile\n")

        response_key = client.get_secret_value(SecretId=key)
        with open("hive_keyfile", "w") as cert_file:
            cert_file.write(response_key.get("SecretString"))
        envfile.write("hive_keyfile=" + str(Path.cwd()) + "/hive_keyfile\n")

        self.get_hive_passphase_and_set_variable(envfile, client, account_id, arn)

    def extract_secret_and_write_environment_variable_file(self, secret, keys, envfile):
        json_secret = json.loads(secret, strict=False)

        for key in keys:
            try:
                envfile.write(key + "=" + json_secret[key] + "\n")
            except KeyError:
                print("I can't find a secret called " + key)

    def create_and_write_to_file(self, key, value, envfile):
        cert_file = open(key, "w")
        # cert_file.write(value)
        cert_file.write(self.format_file(value))
        if "." in key:
            no_ext = key.split(".")
            envfile.write(no_ext[0] + "=" + str(Path.cwd()) + "/" + key + "\n")
        else:
            envfile.write(key + "=" + str(Path.cwd()) + "/" + key + "\n")
        cert_file.close()

    def extract_secret_file_and_set_path(self, secret, keys, envfile):
        json_secret = json.loads(secret, strict=False)
        hive_certs = ["hive_certfile", "hive_keyfile"]
        for key in keys:
            if key in hive_certs:  # ie we need to pull from the cypto account except eco sys account(temp hack TODO)
                self.download_secrets_cypto_account(envfile)
            else:
                try:
                    decoded_bytes = base64.b64decode(json_secret[key])
                    decoded_string = decoded_bytes.decode("utf-8")
                    self.create_and_write_to_file(key, decoded_string, envfile)

                except (UnicodeDecodeError, binascii.Error):
                    print("certificate not base64 encoded")
                    self.create_and_write_to_file(key, json_secret[key], envfile)

    def format_file(self, value):
        return (
            value.replace(" CERT", "CERT")
            .replace("BEGIN ENCRYPTED PRIVATE KEY", "BEGINENCRYPTEDPRIVATEKEY")
            .replace("END ENCRYPTED PRIVATE KEY", "ENDENCRYPTEDPRIVATEKEY")
            .replace(" RSA PRIVATE KEY", "RSAPRIVATEKEY")
            .replace("BEGIN PRIVATE KEY", "BEGINPRIVATEKEY")
            .replace("END PRIVATE KEY", "ENDPRIVATEKEY")
            .replace(" ", "\n")
            .replace("CERT", " CERT")
            .replace("BEGINENCRYPTEDPRIVATEKEY", "BEGIN ENCRYPTED PRIVATE KEY")
            .replace("ENDENCRYPTEDPRIVATEKEY", "END ENCRYPTED PRIVATE KEY")
            .replace("RSAPRIVATEKEY", " RSA PRIVATE KEY")
            .replace("BEGINPRIVATEKEY", "BEGIN PRIVATE KEY")
            .replace("ENDPRIVATEKEY", "END PRIVATE KEY")
        )

    def write_secret_environment_variable_to_file(self, secret, keys, envfile):
        json_secret = json.loads(secret, strict=False)
        for key in keys:
            envfile.write(key + "=" + json_secret[key] + "\n")

    def get_secret(self, secret_name):
        if "AWS_REGION_NAME" in os.environ:
            region_name = os.environ.get("AWS_REGION_NAME")
        else:
            region_name = "eu-west-2"
        service_name = "secretsmanager"

        # Create a Secrets Manager client
        if "GITLAB_CI" in os.environ:
            session = boto3.session.Session()
        else:
            if "AWS_PROFILE" in os.environ:
                self.set_aws_profile(os.environ.get("AWS_PROFILE"))

            session = boto3.session.Session(profile_name=self.get_aws_profile())

        client = session.client(service_name=service_name, region_name=region_name)

        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)

        except ClientError:
            print("connection failed")
            print("resource not found", secret_name)
            return "not found"
        # Decrypts secret using the associated KMS key.
        secret = get_secret_value_response["SecretString"]
        return secret

    def get_s3(self, bucket, file_name):
        if "AWS_REGION_NAME" in os.environ:
            region_name = os.environ.get("AWS_REGION_NAME")
        else:
            region_name = "eu-west-2"
        service_name = "s3"

        # Create a s3 Manager client
        if "GITLAB_CI" in os.environ:
            session = boto3.session.Session()
        else:
            if "AWS_PROFILE" in os.environ:
                self.set_aws_profile(os.environ.get("AWS_PROFILE"))

            session = boto3.session.Session(profile_name=self.get_aws_profile())

        client = session.client(service_name=service_name, region_name=region_name)
        if os.path.exists("./" + file_name):
            os.remove("./" + file_name)
        try:
            client.download_file(bucket, file_name, "./" + file_name)
            print("token was downloaded succesfully")
        except Exception as e:
            print((repr(e)))

    def main(self):
        envfile = open("ci_secrets.env", "w")

        env_secrets_file = os.environ.get("ENV_SECRETS_YAML")

        secrets_filename = env_secrets_file if env_secrets_file else "config.yml"
        secret_name_array = None
        try:
            secret_name_array = self.read_secret_yaml(secrets_filename, "secretVars")
            # For files - ie certificates where the value of the variable is a path to the file
            secret_file_array = self.read_secret_yaml(secrets_filename, "secretFiles")
        except OSError:
            raise OSError(
                "cannot find your config file looked for a file called "
                + secrets_filename
                + " this can be changed by setting the ENV_SECRETS_YAML variable"
            )
        try:
            # For files in S3
            s3_file_array = self.read_secret_yaml(secrets_filename, "s3Files")
        except KeyError:
            print("Warning - S3 file cannot be found")
            s3_file_array = False

        if secret_name_array:
            for secret in secret_name_array:
                self.extract_secret_and_write_environment_variable_file(self.get_secret(secret[0]), secret[1], envfile)

        if secret_file_array:
            for secret_file in secret_file_array:
                self.extract_secret_file_and_set_path(self.get_secret(secret_file[0]), secret_file[1], envfile)
        envfile.write("\n")  # make sure we have a extra line for shell parsing purposes

        if s3_file_array:
            for s3_file in s3_file_array:
                self.get_s3(s3_file[0], s3_file[1][0])

        envfile.close()


if __name__ == "__main__":
    getSecrets = GetSecrets()
    getSecrets.main()
