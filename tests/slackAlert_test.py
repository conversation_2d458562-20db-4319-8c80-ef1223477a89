import contextlib
import os
from slackAlert import get_slack_bot_token, read_results_file, write_alert_message, get_correct_icon, get_title, get_xml_file_name


def test_should_use_slack_bot_token_when_environment_var_exists():
    os.environ["SLACK_BOT_TOKEN"] = "some_slack_token"
    os.environ["SLACK_BOT_TOKEN_VCDP"] = "some_slack_token_vcdp"
    assert get_slack_bot_token() == "some_slack_token"


def test_should_use_slack_bot_vcdp_token_when_environment_var_doesnt_exist():
    del os.environ["SLACK_BOT_TOKEN"]
    os.environ["SLACK_BOT_TOKEN_VCDP"] = "some_slack_token_vcdp"
    assert get_slack_bot_token() == "some_slack_token_vcdp"


def test_should_return_file_contents_when_file_exists():
    assert read_results_file("tests/fixtures/test_summary.txt") == "test_summary contents"


def test_should_throw_file_not_found_when_file_not_found():
    with contextlib.suppress(FileNotFoundError):
        os.remove("ci_config.env")
    assert read_results_file() == "file was not found."


def test_should_write_correct_alert_message():
    os.environ["CI_JOB_ID"] = "job_id"
    os.environ["CI_PROJECT_NAME"] = "project_name"
    os.environ["XRAY_TEST_SUITE_ID"] = "suite_id"
    os.environ["CI_PROJECT_NAMESPACE"] = "D9/testing"
    write_alert_message("1", "2", "3", ["some_alert_message"], "tests/fixtures/test_file.txt")
    with open("tests/fixtures/test_file.txt", "r") as testFile, open("tests/fixtures/expected_summary.txt", "r") as assertFile:
        assert testFile.read() == assertFile.read()
    os.remove("tests/fixtures/test_file.txt")


def test_title_should_be_custom_value_if_set():
    os.environ["CUSTOM_SLACK_ALERT_TITLE"] = "custom title"
    assert get_title() == "custom title"


def test_icon_should_be_custom_value_if_set():
    os.environ["CUSTOM_SLACK_ALERT_ICON"] = ":warning:"
    assert get_correct_icon(1) == ":warning:"
    del os.environ["CUSTOM_SLACK_ALERT_ICON"]


def test_title_should_be_jira_number_if_custom_title_not_set():
    del os.environ["CUSTOM_SLACK_ALERT_TITLE"]
    os.environ["XRAY_TEST_SUITE_ID"] = "suite_id"
    assert get_title() == "suite_id"


def test_output_file_name_should_be_output_if_includedTagOverride_not_set():
    assert get_xml_file_name() == "output"


def test_output_file_name_should_be_included_tag_if_includedTagOverride_set():
    os.environ["includedTagOverride"] = "included_tag"
    assert get_xml_file_name() == "included_tag"


def test_output_file_name_should_be_stripped_of_special_characters_if_includedTagOverride_set():
    os.environ["includedTagOverride"] = "tag.with.na\\ughty*`/chars"
    assert get_xml_file_name() == "tagwithnaughtychars"


def test_should_provide_fail_icon_when_test_failures_are_greater_then_zero():
    assert get_correct_icon(1) == ":crossbox: Tests Failed"


def test_should_provide_pass_icon_when_test_failures_are_zero():
    assert get_correct_icon(0) == ":white_check_mark: Tests Passed"


def test_should_limit_error_message_output_to_2500_chars():
    error_list = []
    with open("tests/fixtures/error_message.txt") as file:
        for line in file:
            error_list.append(line.rstrip())
    os.environ["CI_JOB_ID"] = "job_id"
    os.environ["CI_PROJECT_NAME"] = "project_name"
    os.environ["XRAY_TEST_SUITE_ID"] = "suite_id"
    os.environ["CI_PROJECT_NAMESPACE"] = "D9/testing"
    write_alert_message("1", "2", "3", error_list, "tests/fixtures/test_file_long.txt")
    with open("tests/fixtures/test_file_long.txt", "r") as testFile, open("tests/fixtures/expected_long_summary.txt", "r") as assertFile:
        assert testFile.read() == assertFile.read()
    os.remove("tests/fixtures/test_file_long.txt")


def test_should_display_all_error_message_when_total_is_less_then_2500_chars():
    os.environ["CI_JOB_ID"] = "job_id"
    os.environ["CI_PROJECT_NAME"] = "project_name"
    os.environ["XRAY_TEST_SUITE_ID"] = "suite_id"
    os.environ["CI_PROJECT_NAMESPACE"] = "D9/testing"
    write_alert_message("1", "2", "3", ["some_alert_message"], "tests/fixtures/test_file.txt")
    with open("tests/fixtures/test_file.txt", "r") as testFile, open("tests/fixtures/expected_summary.txt", "r") as assertFile:
        assert testFile.read() == assertFile.read()
    os.remove("tests/fixtures/test_file.txt")
