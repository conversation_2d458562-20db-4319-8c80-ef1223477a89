import contextlib
import pytest
from getSecretsFromAWSSecretsManager import GetSecrets
import os


@pytest.fixture(autouse=True)
def setup_teardown():
    global getSecrets
    getSecrets = GetSecrets()
    yield
    filesToRemove = ["ci_secrets.env", "dummyJiraToken"]
    for file in filesToRemove:
        with contextlib.suppress(FileNotFoundError):
            os.remove(file)


def test_executing_main_method_should_genrerate_secrets_output():
    getSecrets.main()
    testFile = open("ci_secrets.env", "r")

    # although this looks like a valid jira token - it isn't!
    expected = "dummyJiraToken=V73+1M2Ov88E/Xm6j30Cnvhq+WVXTsODI4MjQ2MjMzMD\n" + "dummyJiraToken=" + os.getcwd() + "/dummyJiraToken\n\n"
    assert expected == testFile.read()
    testFile.close()

    secretFile = open("dummyJiraToken", "r")
    expected = "V73+1M2Ov88E/Xm6j30Cnvhq+WVXTsODI4MjQ2MjMzMD"
    assert expected == secretFile.read()
    secretFile.close()
