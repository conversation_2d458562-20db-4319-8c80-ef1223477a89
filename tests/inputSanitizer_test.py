import os
from inputSanitizer import sanitize_input


def test_should_return_correct_file_name_with_no_naughty_characters():
    assert "test.yml" == sanitize_input("test.yml")


def test_should_return_correct_file_name_with_expected_base_file_path():
    base_path = os.getcwd()
    assert base_path + "/test.yml" == sanitize_input(base_path + "/test.yml")


def test_should_return_correct_file_name_with_expected_test_file_path():
    base_path = os.getcwd() + "/tests/fixtures/"
    assert base_path + "test.yml" == sanitize_input(base_path + "test.yml")


def test_should_return_error_if_file_extention_is_missing():
    assert "input does not start with the base path or end with .yml" == sanitize_input("test.bob")


def test_should_remove_all_special_characters_from_raw_test_file():
    assert "test.yml" == sanitize_input("../test/.&.yml")
