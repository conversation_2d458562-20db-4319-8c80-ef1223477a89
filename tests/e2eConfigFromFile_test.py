import contextlib
import pytest
from getConfigFromFile import GetConfig
import os


@pytest.fixture(autouse=True)
def setup_teardown():
    global getConfig
    getConfig = GetConfig()
    yield
    with contextlib.suppress(FileNotFoundError):
        os.remove("ci_config.env")


def test_executing_main_method_should_genrerate_config_output():
    os.environ["ENV_SECRETS_YAML"] = os.getcwd() + "/tests/fixtures/config.yml"
    getConfig.main()

    testFile = open("ci_config.env", "r")

    expected = "pullTests=True\n" + "pullBaseImage=False\n"
    assert expected == testFile.read()
    testFile.close()
