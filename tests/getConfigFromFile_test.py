from getConfigFromFile import GetConfig
import os


def test_read_yaml_file():
    getConfig = GetConfig()
    configArray = getConfig.read_config_yaml(os.getcwd() + "/tests/fixtures/testConfig.yml", "config")
    assert configArray == [["pullTests", True], ["usePabot", False]]


def test_write_config_environment_variable_to_file():
    getConfig = GetConfig()
    testFile = open("testFile.env", "w")
    getConfig.write_config_environment_variable_to_file("testKey", "testValue", testFile)
    testFile.close()
    testFile = open("testFile.env", "r")
    expected = "testKey=testValue\n"
    assert expected == testFile.read()
    testFile.close()
    os.remove("testFile.env")
