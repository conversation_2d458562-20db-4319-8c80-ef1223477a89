import contextlib
import pytest
from getSecretsFromAWSSecretsManager import GetSecrets
import os


@pytest.fixture(autouse=True)
def setup_teardown():
    global getSecrets
    getSecrets = GetSecrets()
    yield
    filesToRemove = ["testFile.env", "testKey", "testKey1"]
    for file in filesToRemove:
        with contextlib.suppress(FileNotFoundError):
            os.remove(file)


def test_should_create_and_write_a_key_value_pair_to_file():
    testFile = open("testFile.env", "w")

    getSecrets.create_and_write_to_file("testKey", "testValue", testFile)
    testFile.close()
    testFile = open("testFile.env", "r")
    expected = "testKey=" + os.getcwd() + "/testKey\n"
    assert expected == testFile.read()
    testFile.close()


def test_should_extract_secret_and_write_environment_variable_file():
    testJson = '{"testKey1":"value1","testKey2":"value2"}'
    testKeys = ["testKey1", "testKey2"]
    testFile = open("testFile.env", "w")

    getSecrets.extract_secret_and_write_environment_variable_file(testJson, testKeys, testFile)
    testFile.close()
    testFile = open("testFile.env", "r")
    expected = "testKey1=value1\ntestKey2=value2\n"
    assert expected == testFile.read()
    testFile.close()


def test_should_extract_secret_file_and_set_path_with_base64_cert():
    encodededCert = "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCnNvbWUgdGVzdCBjZXJ0IHN0dWZmCi0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ=="
    testJson = '{"testKey1": "' + str(encodededCert) + '"}'
    testKeys = ["testKey1"]
    testFile = open("testFile.env", "w")

    getSecrets.extract_secret_file_and_set_path(testJson, testKeys, testFile)
    testFile.close()
    testFile = open("testFile.env", "r")
    expected = "testKey1=" + os.getcwd() + "/testKey1\n"
    assert expected == testFile.read()
    testFile.close()


def test_should_extract_secret_file_and_set_path_with_plaintext_cert():
    testCert = "-----BEGIN CERTIFICATE-----\n\
some test cert stuff\n\
----END CERTIFICATE-----"
    testJson = '{"testKey1": "' + testCert + '"}'
    testKeys = ["testKey1"]
    testFile = open("testFile.env", "w")

    getSecrets.extract_secret_file_and_set_path(testJson, testKeys, testFile)
    testFile.close()
    testFile = open("testFile.env", "r")
    expected = "testKey1=" + os.getcwd() + "/testKey1\n"
    assert expected == testFile.read()
    testFile.close()


def test_get_default_aws_profile():
    assert "vcdp-developers" == getSecrets.get_aws_profile()


def test_set_aws_profile():
    getSecrets.set_aws_profile("test-profile")
    assert "test-profile" == getSecrets.get_aws_profile()


def test_read_yaml_file():
    configArray = getSecrets.read_secret_yaml(os.getcwd() + "/tests/fixtures/testConfig.yml", "secretVars")
    assert configArray == [
        ["testVar1", ["value1", "value2"]],
        ["testVar2", ["value3", "value4"]],
    ]


def test_write_secret_environment_variable_to_file():
    testFile = open("testFile.env", "w")
    testJson = '{"testKey1": "testValue"}'
    getSecrets.write_secret_environment_variable_to_file(testJson, ["testKey1"], testFile)
    testFile.close()
    testFile = open("testFile.env", "r")
    expected = "testKey1=testValue\n"
    assert expected == testFile.read()
    testFile.close()


def test_assume_crypo_role():
    # Create an instance of GetSecrets and call the method
    instance = GetSecrets()
    client, account_id, arn = instance.assume_crypo_role()

    # Assertions
    assert isinstance(client, object)  # Check if client is an object
    assert isinstance(account_id, str)  # Check if account_id is a string
    assert isinstance(arn, str)  # Check if arn is a string
