include:
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/security/base-sec.yml"
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/security/python-sec.yml"


variables:
  GCP_PROJECT: jlr-eng-d9platform-dev
  ECR_REGION: eu-west-2
  SHARED_SERVICES_ACCOUNT: ************
  KANIKO_IMAGE_TAG: "$SHARED_SERVICES_ACCOUNT.dkr.ecr.$ECR_REGION.amazonaws.com/${CI_PROJECT_NAME}"
stages:
  - test
  - docker build
  - enhanced docker build
  - validate
  - mutate
  - inspect code
  - inspect image

################
# DOCKER BUILD #
################

snyk:
  image: snyk/snyk:python-3.11
  stage: inspect code
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always 
    - when: always       

  
build_image_aws:
  stage: docker build
  allow_failure: false
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  tags:
    - aws
    - vcdp-dev
  script:
    - mkdir -p /kaniko/.docker
    - echo "Kaniko image tags are ${KANIKO_IMAGE_TAG}:${CI_COMMIT_SHA} and ${KANIKO_IMAGE_TAG}:latest"
    - echo "{\"credsStore\":\"ecr-login\",\"credHelpers\":{\"$SHARED_SERVICES_ACCOUNT.dkr.ecr.$ECR_REGION.amazonaws.com\":\"ecr-login\"}}" > /kaniko/.docker/config.json
    - cat /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${KANIKO_IMAGE_TAG}:${CI_COMMIT_SHA}"
      --destination "${KANIKO_IMAGE_TAG}:latest"
      --build-arg commit_sha=${CI_COMMIT_SHORT_SHA}
      --cache=true
      --cache-ttl=1h
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      changes:
        - CHANGELOG.md
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: on_success


build_image_ui:
  stage: enhanced docker build
  allow_failure: true
  rules:
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH 
      when: never
    - when: always
  trigger:
    project: D9/testing/vcdp-e2e-tests-base-image-ui-support
    branch: main
    strategy: depend

build_image_java:
  stage: enhanced docker build
  allow_failure: true
  rules: 
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH 
      when: never
    - when: always
  trigger:
    project: D9/testing/vcdp-e2e-tests-base-image-java-support
    branch: main
    strategy: depend

build_beta_image_aws:
  stage: docker build
  allow_failure: false
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  tags:
    - aws
    - vcdp-dev
  script:
    - mkdir -p /kaniko/.docker
    - echo "Kaniko image tags are ${KANIKO_IMAGE_TAG}:${CI_COMMIT_SHA} and ${KANIKO_IMAGE_TAG}:latest"
    - echo "{\"credsStore\":\"ecr-login\",\"credHelpers\":{\"$SHARED_SERVICES_ACCOUNT.dkr.ecr.$ECR_REGION.amazonaws.com\":\"ecr-login\"}}" > /kaniko/.docker/config.json
    - cat /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${KANIKO_IMAGE_TAG}:${CI_COMMIT_SHA}"
      --destination "${KANIKO_IMAGE_TAG}:beta"
      --build-arg commit_sha=${CI_COMMIT_SHORT_SHA}
      --cache=true
      --cache-ttl=1h
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: never
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: always 

unit_tests:

  image: ************.dkr.ecr.eu-west-2.amazonaws.com/python:3

  stage: test
  tags:
    - aws
    - vcdp-dev
  script:
    - pip3 install  --upgrade pip -r localRequirements.txt
    - coverage run -m pytest
    - coverage report
    - coverage html
    - coverage xml
  artifacts:
    paths:
    - htmlcov
    - coverage.xml
    when: always
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: always 

  

code_style_tests:
  image:  ************.dkr.ecr.eu-west-2.amazonaws.com/python:3
  stage: test
  tags:
    - aws
    - vcdp-dev
  script:
    - pip3 install  --upgrade pip -r localRequirements.txt
    - python3 -m flake8
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: always 

sonarqube:
  dependencies:
    - unit_tests
  needs: 
    - unit_tests
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: always


semgrep:
  stage: inspect code
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: always      

snyk-code:
  stage: inspect code
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: always      

trivy:
  stage: inspect code
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: always      
