#!/bin/bash
if [[ -z $backup_bucket_name ]]; then
        backup_bucket_name=seit-jira-test-backup-dev

fi
#get jira test plan and jira token from secrets or variables
exitCode=1

pushBackUpToS3(){
    cd ..
    aws s3 cp ./features.zip s3://$backup_bucket_name/$XRAY_TEST_SUITE_ID/features.zip
    cd featureExport 
}

getBackupFromS3(){
    #if we have ended up here it looks like we have failed to pull the tests from jira and are attempting to pull from s3
    cd ..
    #do s3 things
    rm -rf testsuites featureExport features.zip
    aws s3 cp --recursive s3://$backup_bucket_name/$XRAY_TEST_SUITE_ID .
    unzip -qq features.zip -d featureExport || {
        mkdir featureExport
        echo "failed to unzip file, assuming this is a single feature file project"
        mv features.zip featureExport/1.feature

    }
    cd featureExport
    for file in *.feature; do
        gherkin2robotframework $file || {
            echo "failed to parse backup from S3 - could be the backup file is invalid in someway or is just missing, tests will now exit"
            exit 1
        } 

    done  
    #by this point we should have tests in the right format from the s3 bucket ready to pick up from line 52

}

getTestsPytest(){
    
    cd tests
    rm -rf features features.zip

    mkdir features
    
    # get test scenarios from a JIRA "Test plan"
    curl -s --location --request GET 'https://jira.devops.jlr-apps.com/rest/raven/1.0/export/test?keys='$XRAY_TEST_SUITE_ID \
    --header 'Accept=application/zip' \
    --header 'Authorization: Bearer '"$jiraToken"'' \
    --output features.zip

    unzip -qq features.zip -d features || {
        mkdir features
        echo "failed to unzip file, assuming this is a single feature file project"
        mv features.zip features/1.feature

    }
    for file in features/*.feature; do
        sed -i -e 's/  */ /g' $file
    done
    cd ..
}
runTestsPytest(){
    excludeList='not AutomatedTest-NotReady or not AutomatedTest-Blocked or not AutomatedVCDPTest-Isolated or not AutomatedVCDPTest-RETIRED'

    if [[ ! -z $excludeOverride ]]; then
        excludeList=" or not $excludeOverride"
    fi

    if [[ ! -z $includedTagOverride ]]; then
        includedTags=" -k $includedTagOverride --junitxml=$includedTagOverride.xml"
    fi

    pytest -k "$excludeList" $includedTags --bdd-report=report.html --self-contained-html --junitxml=$includedTagOverride.xml
    exitCode=$?
}
getTestsRobot () {
    mkdir -p robot
    cd robot
    rm -rf testsuites featureExport features.zip

    # get test scenarios from a JIRA "Test plan"
    curl -s --location --request GET 'https://jira.devops.jlr-apps.com/rest/raven/1.0/export/test?keys='$XRAY_TEST_SUITE_ID \
    --header 'Accept=application/zip' \
    --header 'Authorization: Bearer '"$jiraToken"'' \
    --output features.zip

    unzip -qq features.zip -d featureExport || {
        mkdir featureExport
        echo "failed to unzip file, assuming this is a single feature file project"
        mv features.zip featureExport/1.feature

    }
    cd featureExport
    for file in *.feature; do
        gherkin2robotframework $file || {
            #if we end up in a failure state we should only have 1 feature file to parse so the for loop will only run once
            echo "gherkin2robotframework has failed to parse the feature file - possibly because we have failed to pull down the tests from xray"
            echo "attempting to get backup version of features from s3"
            #get from s3
            getBackupFromS3
            
        }

    done
    #push a backup file into s3
    pushBackUpToS3
   
    mkdir ../testsuites
    for file in *.robot; do
        mv $file ../testsuites/$file
    done

    cd ../testsuites
    rm *_step_definitions.robot

    if [[ -z "$resourceFile" ]]; then
        resourceFile="app-config.robot" #change default resource file name

    fi

    for file in *.robot; do
        sed -i -e 's/TEST_//g' $file
        sed -i '/step_definitions.robot/d' $file
        sed -i -e 's/ ${/    ${/g' $file
        sed -i '/Metadata/d' $file
        sed -i -e 's/Given\s/GIVEN /g' $file
        sed -i -e 's/When\s/WHEN /g' $file
        sed -i -e 's/Then\s/THEN /g' $file
        sed -i -e 's/And\s/AND /g' $file
        sed -i 's/txt/robot/' $file
        sed -i 's/Resource	keywords.robot//' $file
        sed -i "/Test Cases/ i Resource        ../resource/$resourceFile" $file
        if [[ ! -z "$suiteSetUpStep" ]]; then  #add a setup line to robot files
            sed -i "/Test Cases/ i Suite Setup      $suiteSetUpStep" $file
        fi
        if [[ ! -z "$testSetUpStep" ]]; then  #add a setup line to robot files
            sed -i "/Test Cases/ i Test Setup       $testSetUpStep" $file
        fi
        if [[ ! -z "$testTearDownStep" ]]; then #add a teardown line to robot files
            sed -i "/Test Cases/ i Test Teardown 	       $testTearDownStep" $file

        fi
        if [[ ! -z "$suiteTearDownStep" ]]; then #add a teardown line to robot files
            sed -i "/Test Cases/ i Suite Teardown 	       $suiteTearDownStep" $file

        fi
        sed -i '/Test Cases/ i \\' $file         # blank line
    done

    cd ../..
    rm -rf featureExport features.zip

}

getAndSetSecrets () {

    #Get secrets from secrets manager and load them as environment variables
    python3 getSecretsFromAWSSecretsManager.py
    while read line
    do
        export "$line"
    done < ci_secrets.env  &> /dev/null
}

runTestsRobot () {
    cd robot/testsuites

    excludeList="--exclude AutomatedTest-NotReadyORAutomatedTest-BlockedORAutomatedVCDPTest-IsolatedORAutomatedVCDPTest-RETIRED"
   
    if [[ ! -z $excludeOverride ]]; then
        excludeList="${excludeList}OR${excludeOverride}"

    fi

    if [[ $excludeNothing == True ]]; then
        unset excludeList
    fi

    if [[ ! -z $includedTagOverride ]]; then
        includedTags="--include $includedTagOverride --output $includedTagOverride.xml"

    fi
    echo $includedTags
    if [[ $usePabot != True ]]; then
        robot $excludeList $includedTags --xunit junitOutput.xml -v useProxy:False *.robot
    else
        if [[ $splitOnTests == True ]]; then
            if [[ ! -z $pabotProcessors ]]; then
                pabot --processes $pabotProcessors --testlevelsplit $excludeList $includedTags  --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
            else
                pabot --processes 2 --testlevelsplit $excludeList $includedTags  --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
            fi
        
        else
            if [[ ! -z $pabotProcessors ]]; then
                pabot $excludeList $includedTags --processes $pabotProcessors --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
            else
                pabot $excludeList $includedTags --listener RetryFailed:$numberOfRetries --xunit junitOutput.xml -v useProxy:False *.robot
            fi
        fi
    
    fi
    exitCode=$?
}


uploadResults(){
    project_key="${XRAY_TEST_SUITE_ID:0:3}"
    echo $project_key

    if [[ $uploadJunit != True ]]; then 
        outputFile="output.xml"
        if [[ ! -z $includedTagOverride ]]; then
            outputFile="$includedTagOverride.xml"
        fi
    else
        outputFile="junitOutput.xml"
    fi
    echo "Pushing results to Jira x-ray......"
    curl -s -H "Content-Type: multipart/form-data" \
    -F "file=@./$outputFile" "https://jira.devops.jlr-apps.com/rest/raven/1.0/import/execution/robot?projectKey=$project_key&testPlanKey="$XRAY_TEST_SUITE_ID \
    --header 'Accept: application/json' \
    --header 'Authorization: Bearer '"$jiraToken"''


}

pullAndSetConfig () {

    python3 getConfigFromFile.py
    while read line
    do
        export "$line"
    done < ci_config.env  &> /dev/null

}


sendSlackReports () {
    cd -
    python3 robot/libs/slackIntegration.py

}

sendSlackAlert () {
    if [[ $usePytest = True ]]; then
        cd ..
    else
        cd ../..
    fi
    
    if [[ ! -z $includedTagOverride ]]; then
        includedTagOverride=$includedTagOverride python3 slackAlert.py
    else
        python3 slackAlert.py
    fi

}

timestamp_start_ms=$(($(date +%s%N)/1000000))
# Echo the timestamp

#if we have a local .env file load it in
[ -s .env ] && export $(cat .env | xargs) &> /dev/null

timestamp_environment_vars_set_ms=$(($(date +%s%N)/1000000))
pullAndSetConfig
timestamp_pulled_and_set_config_ms=$(($(date +%s%N)/1000000))
getAndSetSecrets
timestamp_got_and_set_secrets_ms=$(($(date +%s%N)/1000000))


if [[ $usePytest != True ]] && [[ $XRAY_TEST_SUITE_ID != "" ]]; then
    getTestsRobot
fi

if [[ $usePytest = True ]]; then
    getTestsPytest
fi
timestamp_got_tests_ms=$(($(date +%s%N)/1000000))

if [[ $usePytest = True ]]; then
    echo "Codebase using Pytest - $CI_JOB_NAME"
    cd tests
    runTestsPytest
else
    echo "Codebase using Robot framework - $CI_JOB_NAME"
    runTestsRobot
fi

timestamp_ran_tests_ms=$(($(date +%s%N)/1000000))

#only upload test results if running in gitlab

if [[ ! -z "$GITLAB_CI" ]] && [[ $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH ]] && [[ -z "$MULTI_JOB_RUN" ]]; then
    uploadResults
fi

timestamp_uploaded_results_ms=$(($(date +%s%N)/1000000))

if [[ $sendReports = True ]]; then
    sendSlackReports

fi

if [[ ! -z "$SLACK_ALERT_CHANNEL" ]]; then
    sendSlackAlert
fi

timestamp_sent_slack_reports_ms=$(($(date +%s%N)/1000000))

echo "timings for job:"
new_timestamp=$((timestamp_start_ms-timestamp_environment_vars_set_ms))
echo "time to get enviroment variables: $new_timestamp"
new_timestamp=$((timestamp_pulled_and_set_config_ms-timestamp_start_ms))
echo "time to pull and set cofig: $new_timestamp"
new_timestamp=$((timestamp_got_and_set_secrets_ms-timestamp_pulled_and_set_config_ms))
echo "time to pull and set secrets: $new_timestamp"
new_timestamp=$((timestamp_got_tests_ms-timestamp_got_and_set_secrets_ms))
echo "time to pull tests: $new_timestamp"
new_timestamp=$((timestamp_ran_tests_ms-timestamp_got_tests_ms))
echo "time to run tests: $new_timestamp"
new_timestamp=$((timestamp_uploaded_results_ms-timestamp_ran_tests_ms))
echo "time to upload results: $new_timestamp"
new_timestamp=$((timestamp_sent_slack_reports_ms-timestamp_uploaded_results_ms))
echo "time to send slack reports: $new_timestamp"

exit $exitCode


