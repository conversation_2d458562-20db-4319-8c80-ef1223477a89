import sys
import os
import requests
from robot.api.deco import keyword, not_keyword
import json
from pyaml_env import BaseConfig, parse_config

sys.path.append("../libs")
import forgeRock  # noqa: E402

systemVarFile = "../variables/system.yml"
systemHilVarFile = "../variables/system-hil.yml"

authType = "Bearer "
request_sent = "Request sent: "
CONTENT_TYPE = "application/json"

command_config = {
    "rdl": ("../json-request-data/one-app-backend-commands/door-lock.json", "/door"),
    "rdu": ("../json-request-data/one-app-backend-commands/door-unlock.json", "/door"),
    "evStart": ("../json-request-data/one-app-backend-commands/evStartPayload.json", "/evcharge"),
    "evStop": ("../json-request-data/one-app-backend-commands/evStopPayload.json", "/evcharge"),
    "cacStart": ("../json/cac/cacStartRequest.json", "/cabinairclean"),
    "cacStop": ("../json/cac/cacStopRequest.json", "/cabinairclean"),
    "bnf": ("../json-request-data/one-app-backend-commands/beep-flash.json", "/beepflash"),
    "ar": ("../json-request-data/one-app-backend-commands/arPayload.json", "/alarm"),
    "cbaLeft": ("../json/cba/cbaRequest.json", "/chargedoor/left"),
    "cbaRight": ("../json/cba/cbaRequest.json", "/chargedoor/right"),
    "hvacStart": ("../json-request-data/one-app-backend-commands/climate-start.json", "/climate"),
    "hvacStop": ("../json-request-data/one-app-backend-commands/climate-stop.json", "/climate"),
    "ccuLock": ("../json-request-data/one-app-backend-commands/ccuLockPayload.json", "/chargecablelock"),
    "ccuUnlock": ("../json-request-data/one-app-backend-commands/ccuUnlockPayload.json", "/chargecablelock"),
    "hsSwOff": ("../json-request-data/one-app-backend-commands/hsSwOff.json", "/heatedsurface/steeringwheel"),
    "hsSwOn": ("../json-request-data/one-app-backend-commands/hsSwOn.json", "/heatedsurface/steeringwheel"),
}


@not_keyword
def get_oneapp_login(test_rig_vin):

    if test_rig_vin == "TSTLRGBVDNRIG7110":
        oneapp_user = os.environ.get("PreProdRig7110OneAppUser")
        oneapp_pw = os.environ.get("PreProdRig7110OneAppPw")
    elif test_rig_vin == "TSTLRGBVDNRIG6722":
        oneapp_user = os.environ.get("PreProdRig6722OneAppUser")
        oneapp_pw = os.environ.get("PreProdRig6722OneAppPw")
    elif test_rig_vin == "TSTLRGBVDNRIG9258":
        oneapp_user = os.environ.get("ProdRigOneAppUser")
        oneapp_pw = os.environ.get("ProdRigOneAppPw")
    else:
        print("Unknown test rig VIN, no oneapp Account::", test_rig_vin)

    approov_token = os.environ["ApproovToken"]
    # print(oneapp_user, oneapp_pw, approov_token)
    return (oneapp_user, oneapp_pw, approov_token)


@keyword("Send OneApp Command to rig lib")
def send_oneapp_command_to_rig(command):
    system_vars = BaseConfig(parse_config(systemVarFile))
    system_hil_vars = BaseConfig(parse_config(systemHilVarFile))
    unique_id = getattr(system_hil_vars, "uniqueId")
    vin = getattr(system_hil_vars, "vin")

    payload, endpoint = command_config[command]
    url_path = getattr(system_vars, "oneapp-backend-url-2") + "/vehicles/" + unique_id + "/commands" + endpoint
    oneapp_user, oneapp_pw, approov_token = get_oneapp_login(vin)
    token = forgeRock.get_token(oneapp_user, oneapp_pw)
    headers = {"accept": "*/*", "Content-Type": CONTENT_TYPE, "Authorization": authType + token, "Attestation": approov_token}

    json_file = open(payload, "r")
    read = json_file.read()
    req_body = json.loads(read)

    print(url_path)
    print(req_body)
    response = requests.request("POST", url_path, headers=headers, json=req_body)
    print(response.status_code, response.text, response.headers)
    return (response.status_code, response.text)


# send_oneapp_command_to_rig("bnf")
